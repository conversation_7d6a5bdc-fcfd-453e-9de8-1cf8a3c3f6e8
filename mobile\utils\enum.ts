export const getEnumOptions = (enumObj: Record<string, string | number>) => {
    return Object.entries(enumObj)
        .filter(([k, v]) => typeof v === 'string')
        .map(([key, value]) => ({ label: value as string, value: key }));
};

/**
 * Compare a value to an enum value.
 * @param enumValue The enum value (e.g. enums.ToiletFacilityType.PIT_WITH_SLAB)
 * @param value The value to compare (usually from form state)
 * @returns true if value matches the enum value
 */
export function isEnumValue(enumValue: unknown, value: unknown): boolean {
  return value === enumValue;
}

/**
 * Get the enum key for a given value.
 * @param enumObj The enum object
 * @param value The value to find the key for
 * @returns The key as a string, or undefined if not found
 */
export function getEnumKeyByValue<T extends object>(enumObj: T, value: unknown): keyof T | undefined {
  return (Object.keys(enumObj) as Array<keyof T>).find(key => enumObj[key] === value);
}

/**
 * Checks if a value is the key of the enum for a given enum object.
 * @param enumObj The enum object
 * @param key The key to compare (e.g. "PIT_WITH_SLAB")
 * @param value The value to compare (usually from form state)
 * @returns true if value is the key or the value of the enum key
 */
export function isEnumKey(enumObj: Record<string, string | number>, key: string, value: unknown): boolean {
  return value === key || value === enumObj[key];
}