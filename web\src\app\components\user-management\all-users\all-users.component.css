/* ============ GLOBAL STYLES ============ */
:host {
  display: block;
  min-height: 100vh;
}

/* ============ PAGE HEADER ============ */
.page-header {
  margin-bottom: 2rem;
  text-align: left;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #2078FF 0%, #2078FF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.page-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
  font-weight: 400;
  line-height: 1.5;
}

/* ============ MAIN CONTENT ============ */
.main-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 1rem;
  overflow: hidden;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* ============ TAB NAVIGATION ============ */
.tab-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
}

.tab-buttons {
  display: flex;
  gap: 0;
  background-color: #f1f5f9;
  border-radius: 0.75rem;
  padding: 0.25rem;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
}

.tab-button {
  background: transparent;
  border: none;
  padding: 0.8rem 1rem;
  font-size: 0.95rem;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  border-radius: 0.5rem;
  position: relative;
  white-space: nowrap;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-button:hover {
  color: #2563eb;
  background-color: rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.tab-button.active {
  color: #2563eb;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.tab-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* ============ ENHANCED PRIMENG BUTTON STYLES ============ */
::ng-deep .add-user-btn {
  background: linear-gradient(135deg, #2078FF 0%, #1d4ed8 100%) !important;
  border: none !important;
  border-radius: 0.75rem !important;
  font-weight: 500 !important;
  padding: 0.8rem 1rem;
  color: white !important;
  box-shadow: 0 4px 12px rgba(32, 120, 255, 0.4) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

::ng-deep .add-user-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 20px rgba(32, 120, 255, 0.5) !important;
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%) !important;
}

::ng-deep .import-btn,
::ng-deep .export-btn {
  border: 1px solid #2078FF !important;
  color: #2078FF !important;
  padding: 0.8rem 1rem;
  border-radius: 0.75rem !important;
  font-weight: 500 !important;
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

::ng-deep .import-btn:hover,
::ng-deep .export-btn:hover {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(32, 120, 255, 0.2) !important;
}

/* ============ CONTENT CONTAINER ============ */
.content-container {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
}

/* ============ SEARCH BAR ============ */
.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.6) 100%);
  backdrop-filter: blur(10px);
}

.search-field {
  flex: 1;
  max-width: 20rem;
}

::ng-deep .search-field .p-inputtext {
  width: 100% !important;
  border: 2px solid #e2e8f0 !important;
  border-radius: 0.75rem !important;
  padding: 0.875rem 1rem 0.875rem 3rem !important;
  font-size: 0.95rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px) !important;
}

::ng-deep .search-field .p-inputtext:focus {
  background-color: white !important;
  border-color: #3b82f6 !important;
  outline: none !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

::ng-deep .search-field .p-inputtext:hover {
  border-color: #9ca3af !important;
  transform: translateY(-1px) !important;
}

::ng-deep .filter-btn {
  border: 2px solid #e2e8f0 !important;
  color: #64748b !important;
  border-radius: 0.75rem !important;
  width: 3.5rem !important;
  height: 3.5rem !important;
  background: rgba(255, 255, 255, 0.9) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(10px) !important;
}

::ng-deep .filter-btn:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  border-color: #3b82f6 !important;
  color: #2563eb !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* ============ ENHANCED TABLE STYLING ============ */
::ng-deep .users-table {
  border: none !important;
  background: transparent !important;
}

::ng-deep .users-table .p-datatable-thead > tr > th {
  color: #374151 !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  padding: 1.5rem 1.5rem !important;
  border-bottom: 2px solid #e2e8f0 !important;
  border-top: none !important;
  border-left: none !important;
  border-right: none !important;
  text-align: left !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.6) 100%) !important;
  backdrop-filter: blur(10px) !important;
}

::ng-deep .users-table .p-datatable-tbody > tr {
  border-bottom: 1px solid #f1f5f9 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: rgba(255, 255, 255, 0.5) !important;
}

::ng-deep .users-table .p-datatable-tbody > tr:hover {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.6) 100%) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
  transform: translateY(-1px) !important;
}

::ng-deep .users-table .p-datatable-tbody > tr > td {
  padding: 1.25rem 1.5rem !important;
  border: none !important;
  color: #1e293b !important;
  font-size: 0.9rem !important;
  vertical-align: middle !important;
}

/* ============ TABLE CONTENT STYLING ============ */
.row-number {
  font-weight: 600;
  color: #64748b;
  font-size: 0.875rem;
}

.user-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.95rem;
}

.user-role {
  padding: 0.375rem 0.875rem;
  font-size: 0.8rem;
  font-weight: 500;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  color: #1e40af;
  border-radius: 0.5rem;
  border: 1px solid #bfdbfe;
  display: inline-block;
}

.user-phone {
  color: #64748b;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.875rem;
}

.user-location {
  color: #64748b;
  font-size: 0.875rem;
  line-height: 1.4;
}

/* ============ STATUS TAGS ============ */
::ng-deep .status-tag.p-tag {
  border-radius: 0.75rem !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  padding: 0.5rem 1rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  border: 2px solid transparent !important;
}

::ng-deep .status-tag.p-tag-success {
  color: #166534 !important;
  border-color: #22c55e !important;
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%) !important;
}

::ng-deep .status-tag.p-tag-warning {
  color: #d97706 !important;
  border-color: #f59e0b !important;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%) !important;
}

::ng-deep .status-tag.p-tag-danger {
  color: #dc2626 !important;
  border-color: #ef4444 !important;
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%) !important;
}

/* ============ TABLE ACTIONS ============ */
.table-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

::ng-deep .table-actions .p-button {
  width: 2.75rem !important;
  height: 2.75rem !important;
  border: 2px solid transparent !important;
  background: transparent !important;
  border-radius: 0.75rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

::ng-deep .edit-btn {
  color: #6b7280 !important;
}

::ng-deep .edit-btn:hover {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%) !important;
  border-color: #3b82f6 !important;
  color: #2563eb !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2) !important;
}

::ng-deep .delete-btn {
  color: #6b7280 !important;
}

::ng-deep .delete-btn:hover {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%) !important;
  border-color: #ef4444 !important;
  color: #dc2626 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2) !important;
}

/* ============ ENHANCED PAGINATION ============ */
::ng-deep .users-table .p-paginator {
  border: none !important;
  border-top: 2px solid #e2e8f0 !important;
  padding: 1.5rem 2rem !important;
  justify-content: space-between !important;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.8) 100%) !important;
  backdrop-filter: blur(20px) !important;
}

::ng-deep .users-table .p-paginator .p-paginator-first,
::ng-deep .users-table .p-paginator .p-paginator-prev,
::ng-deep .users-table .p-paginator .p-paginator-next,
::ng-deep .users-table .p-paginator .p-paginator-last {
  border: 2px solid #e2e8f0 !important;
  border-radius: 0.75rem !important;
  color: #374151 !important;
  margin: 0 0.25rem !important;
  background: rgba(255, 255, 255, 0.9) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(10px) !important;
}

::ng-deep .users-table .p-paginator .p-paginator-page {
  border: 2px solid transparent !important;
  border-radius: 0.75rem !important;
  color: #374151 !important;
  margin: 0 0.25rem !important;
  min-width: 3rem !important;
  background: rgba(255, 255, 255, 0.9) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(10px) !important;
}

::ng-deep .users-table .p-paginator .p-paginator-page.p-highlight {
  color: white !important;
  border-color: #3b82f6 !important;
  background: linear-gradient(135deg, #2078FF 0%, #1d4ed8 100%) !important;
  box-shadow: 0 4px 12px rgba(32, 120, 255, 0.4) !important;
  transform: translateY(-1px) !important;
}

::ng-deep .users-table .p-paginator .p-paginator-page:hover:not(.p-highlight) {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  border-color: #3b82f6 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.paginator-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

::ng-deep .items-per-page-dropdown .p-select {
  border: 2px solid #e2e8f0 !important;
  border-radius: 0.5rem !important;
  min-width: 5rem !important;
  height: 2.5rem !important;
  font-size: 0.875rem !important;
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px) !important;
}

::ng-deep .items-per-page-dropdown .p-select .p-select-label {
  padding: 0.375rem 0.75rem !important;
  font-size: 0.875rem !important;
}

/* ============ ENHANCED CHECKBOX STYLING ============ */
::ng-deep .p-checkbox .p-checkbox-box {
  border: 2px solid #9ca3af !important;
  border-radius: 0.5rem !important;
  width: 1.25rem !important;
  height: 1.25rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px) !important;
}

::ng-deep .p-checkbox .p-checkbox-box:hover {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  transform: scale(1.05) !important;
}

::ng-deep .p-checkbox .p-checkbox-box.p-highlight {
  border-color: #3b82f6 !important;
  background: linear-gradient(135deg, #2078FF 0%, #1d4ed8 100%) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
}

::ng-deep .p-checkbox .p-checkbox-box .p-checkbox-icon {
  color: white !important;
  font-size: 0.875rem !important;
  font-weight: bold !important;
}

/* ============ BULK ACTIONS BAR ============ */
.bulk-actions-bar {
  border-top: 2px solid #3b82f6 !important;
  padding: 1rem 2rem !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%) !important;
  backdrop-filter: blur(20px) !important;
}

.bulk-info {
  font-weight: 600 !important;
  color: #1e40af !important;
  font-size: 0.95rem !important;
}

.bulk-actions {
  display: flex;
  gap: 1rem;
}

/* ============ ENHANCED EMPTY STATE ============ */
.empty-state {
  text-align: center !important;
  padding: 4rem 2rem !important;
}

.empty-content {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  gap: 1.5rem !important;
}

.empty-icon {
  font-size: 4rem !important;
  color: #d1d5db !important;
  border-radius: 50% !important;
  width: 6rem !important;
  height: 6rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
  margin: 0 auto !important;
}

.empty-content h3 {
  margin: 0 !important;
  color: #374151 !important;
  font-size: 1.25rem !important;
  font-weight: 600 !important;
}

.empty-content p {
  margin: 0 !important;
  color: #6b7280 !important;
  font-size: 0.95rem !important;
}

/* ============ USER DIALOG STYLES ============ */
::ng-deep .user-dialog .p-dialog {
  border-radius: 1rem !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

::ng-deep .user-dialog .p-dialog-content {
  padding: 2rem !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%) !important;
  backdrop-filter: blur(20px) !important;
}

::ng-deep .user-dialog .p-dialog-header {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  padding: 2rem 2rem 1rem 2rem !important;
  border-bottom: 1px solid #e5e7eb !important;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  backdrop-filter: blur(20px) !important;
  border-radius: 1rem 1rem 0 0 !important;
}

::ng-deep .user-dialog .p-dialog-header .p-dialog-title {
  background: linear-gradient(135deg, #2078FF 0%, #2078FF 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

/* ============ FORM STYLING ============ */
.form-row {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.form-field {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 250px;
  margin-bottom: 1rem;
}

.form-field label {
  margin-bottom: 0.75rem;
  color: #374151;
  font-weight: 600;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.required {
  color: #ef4444;
  font-weight: bold;
}

/* ============ ENHANCED INPUT STYLING ============ */
::ng-deep .user-dialog input[pInputText],
::ng-deep .user-dialog .p-inputtext,
::ng-deep .user-dialog .p-select {
  padding: 0.5rem 1rem !important;
  border: 2px solid #e2e8f0 !important;
  border-radius: 0.75rem !important;
  font-size: 0.95rem !important;
  background: rgba(255, 255, 255, 0.9) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  width: 100% !important;
  backdrop-filter: blur(10px) !important;
}

::ng-deep .user-dialog input[pInputText]:focus,
::ng-deep .user-dialog .p-inputtext:focus,
::ng-deep .user-dialog .p-select:focus {
  outline: none !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  background: white !important;
  transform: translateY(-1px) !important;
}

::ng-deep .user-dialog input[pInputText]:hover,
::ng-deep .user-dialog .p-inputtext:hover,
::ng-deep .user-dialog .p-select:hover {
  border-color: #9ca3af !important;
  transform: translateY(-1px) !important;
}

/* ============ P-SELECT SPECIFIC STYLING ============ */
::ng-deep .user-dialog .p-select-label {
  padding: 0.5rem 1rem !important;
  color: #374151 !important;
  font-size: 0.95rem !important;
  background-color: white;
}

::ng-deep .user-dialog .p-select-label.p-placeholder {
  color: #9ca3af !important;
}

::ng-deep .user-dialog .p-select-dropdown {
  border-left: 2px solid #e2e8f0 !important;
  border-radius: 0 0.75rem 0.75rem 0 !important;
}
::ng-deep .user-role-dropdown .p-select-label {
  padding: 0.5rem;
  font-size: 0.95rem;
}

::ng-deep .user-role-dropdown .p-select-overlay {
  background-color: #f9fafb;
}

/* ============ ERROR MESSAGES ============ */
.field-error {
  color: #ef4444 !important;
  font-size: 0.875rem !important;
  margin-top: 0.5rem !important;
  font-weight: 500 !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.375rem !important;
}

.field-error::before {
  content: "⚠" !important;
  font-size: 0.875rem !important;
}

/* ============ LOCATION SECTION ============ */
.location-section {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
  position: relative;
}


/* ============ DIALOG FOOTER BUTTONS ============ */
::ng-deep .user-dialog .cancel-btn,
::ng-deep .user-dialog .save-btn {
  min-width: 140px !important;
  border-radius: 0.75rem !important;
  font-weight: 600 !important;
  padding: 0.6rem 1rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

::ng-deep .user-dialog .save-btn {
  background: linear-gradient(135deg, #2078FF 0%, #1d4ed8 100%) !important;
  border: none !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(32, 120, 255, 0.4) !important;
}

::ng-deep .user-dialog .save-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 20px rgba(32, 120, 255, 0.5) !important;
}

::ng-deep .user-dialog .save-btn:disabled {
  background: linear-gradient(135deg, #93c5fd 0%, #bfdbfe 100%) !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

::ng-deep .user-dialog .cancel-btn {
  background: rgba(229, 231, 235, 0.8) !important;
  color: #374151 !important;
  border: 2px solid #e5e7eb !important;
  backdrop-filter: blur(10px) !important;
}

::ng-deep .user-dialog .cancel-btn:hover {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%) !important;
  border-color: #9ca3af !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* ============ IMPORT DIALOG STYLING ============ */
::ng-deep .p-dialog {
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(20px) !important;
  border-radius: 1rem !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

::ng-deep .p-fileupload {
  border-radius: 0.75rem !important;
  border: 2px dashed #d1d5db !important;
  padding: 2rem !important;
  text-align: center !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.6) 100%) !important;
  backdrop-filter: blur(10px) !important;
}

::ng-deep .p-fileupload:hover {
  border-color: #3b82f6 !important;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%) !important;
  transform: translateY(-2px) !important;
}

::ng-deep .p-fileupload .p-button {
  border-radius: 0.75rem !important;
  font-weight: 600 !important;
  padding: 0.875rem 1.75rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: linear-gradient(135deg, #2078FF 0%, #1d4ed8 100%) !important;
  border: none !important;
  box-shadow: 0 4px 12px rgba(32, 120, 255, 0.4) !important;
}

::ng-deep .p-fileupload .p-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 20px rgba(32, 120, 255, 0.5) !important;
}

/* ============ TOAST MESSAGES ============ */
::ng-deep .p-toast {
  backdrop-filter: blur(20px) !important;
}

::ng-deep .p-toast .p-toast-message {
  border-radius: 0.75rem !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

/* ============ CONFIRMATION DIALOG ============ */
::ng-deep .p-confirm-dialog {
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(20px) !important;
  border-radius: 1rem !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
}

/* ============ RESPONSIVE DESIGN ============ */
@media (max-width: 1024px) {
  :host {
    padding: 1rem;
  }

  .tab-navigation {
    flex-direction: column;
    gap: 1.5rem;
    align-items: stretch;
    padding: 1.5rem;
  }

  .tab-actions {
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 0.75rem;
  }

  .search-bar {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }

  ::ng-deep .users-table .p-datatable-tbody > tr > td {
    padding: 1rem 0.75rem !important;
    font-size: 0.875rem !important;
  }

  ::ng-deep .users-table .p-datatable-thead > tr > th {
    padding: 1rem 0.75rem !important;
    font-size: 0.8rem !important;
  }

  ::ng-deep .user-dialog .p-dialog {
    width: 95vw !important;
    height: 90vh !important;
    margin: 1rem !important;
    max-width: none !important;
  }

  .form-row {
    flex-direction: column;
  }

  .form-field {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.75rem;
  }

  .tab-button {
    font-size: 0.875rem;
    padding: 0.75rem 1.25rem;
  }

  ::ng-deep .users-table .p-datatable-tbody > tr > td {
    padding: 0.75rem 0.5rem !important;
    font-size: 0.8rem !important;
  }

  .tab-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  ::ng-deep .add-user-btn,
  ::ng-deep .import-btn,
  ::ng-deep .export-btn {
    width: 100% !important;
    justify-content: center !important;
  }
}

/* ============ LOADING STATES ============ */
::ng-deep .p-datatable .p-datatable-loading-overlay {
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px) !important;
}

::ng-deep .p-progress-spinner {
  width: 3rem !important;
  height: 3rem !important;
}

/* ============ ANIMATIONS ============ */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.main-content {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ============ ACCESSIBILITY IMPROVEMENTS ============ */
:focus-visible {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
  border-radius: 0.375rem !important;
}

/* ============ PRINT STYLES ============ */
@media print {
  :host {
    background: white !important;
    padding: 0 !important;
  }

  .tab-navigation,
  .search-bar,
  .table-actions,
  .bulk-actions-bar {
    display: none !important;
  }

  ::ng-deep .users-table .p-paginator {
    display: none !important;
  }
}