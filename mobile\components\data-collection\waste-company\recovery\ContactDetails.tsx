import React from 'react';
import { View } from 'react-native';
import { Controller } from 'react-hook-form';
import tw from 'twrnc';
import AppText from '@/components/ui/Text';
import { TextInput } from 'react-native-paper';

interface ContactDetailsSectionProps {
  control: any;
  errors: any;
}

const ContactDetailsSection = ({ control, errors }: ContactDetailsSectionProps) => {
  return (
    <View style={tw`gap-4`}>

      {/* Contact Person */}
      <View>
        <AppText style={tw`mb-2`}>Contact Person</AppText>
        <Controller
          control={control}
          name="contactDetails.contactPerson"
          render={({ field: { onChange, value } }) => (
            <TextInput
              mode="outlined"
              value={value || ''}
              onChangeText={onChange}
              placeholder="Enter contact person name"
              error={!!errors.contactDetails?.contactPerson}
              outlineColor="gray"
            />
          )}
        />
        {errors.contactDetails?.contactPerson && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.contactDetails.contactPerson.message}
          </AppText>
        )}
      </View>

      {/* Contact Phone */}
      <View>
        <AppText style={tw`mb-2`}>Telephone</AppText>
        <Controller
          control={control}
          name="contactDetails.contactPhone"
          render={({ field: { onChange, value } }) => (
            <TextInput
              mode="outlined"
              value={value || ''}
              onChangeText={onChange}
              placeholder="Enter telephone number"
              keyboardType="phone-pad"
              error={!!errors.contactDetails?.contactPhone}
              outlineColor="gray"
            />
          )}
        />
        {errors.contactDetails?.contactPhone && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.contactDetails.contactPhone.message}
          </AppText>
        )}
      </View>

      {/* Contact Email */}
      <View>
        <AppText style={tw`mb-2`}>Email</AppText>
        <Controller
          control={control}
          name="contactDetails.contactEmail"
          render={({ field: { onChange, value } }) => (
            <TextInput
              mode="outlined"
              value={value || ''}
              onChangeText={onChange}
              placeholder="Enter email address"
              keyboardType="email-address"
              autoCapitalize="none"
              error={!!errors.contactDetails?.contactEmail}
              outlineColor="gray"
            />
          )}
        />
        {errors.contactDetails?.contactEmail && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.contactDetails.contactEmail.message}
          </AppText>
        )}
      </View>
    </View>
  );
};

export default ContactDetailsSection;
