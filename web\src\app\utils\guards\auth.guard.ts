import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { AuthService } from '../../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    
    console.log('=== AuthGuard Debug ===');
    console.log('Attempting to access:', state.url);
    console.log('Current timestamp:', new Date().toISOString());
    
    // Allow access to 2FA routes without authentication
    if (state.url === '/verify-2fa' || state.url === '/setup-2fa') {
      console.log('AuthGuard: Allowing access to 2FA route');
      return true;
    }
    
    // Debug localStorage contents
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('currentUser');
    const refreshToken = localStorage.getItem('refreshToken');
    
    console.log('localStorage contents:');
    console.log('- token exists:', !!token);
    console.log('- user exists:', !!user);
    console.log('- refreshToken exists:', !!refreshToken);
    
    if (token) {
      console.log('- token preview:', token.substring(0, 50) + '...');
      console.log('- token length:', token.length);
      
      // Check if token looks like JWT
      const tokenParts = token.split('.');
      console.log('- token parts count:', tokenParts.length);
      
      if (tokenParts.length === 3) {
        try {
          const payload = JSON.parse(atob(tokenParts[1]));
          console.log('- token payload:', payload);
          console.log('- token expiry:', new Date(payload.exp * 1000).toISOString());
          console.log('- token expired:', payload.exp <= (Date.now() / 1000));
        } catch (e) {
          console.log('- token payload parse error:', e);
        }
      }
    }

    if (user) {
      try {
        const parsedUser = JSON.parse(user);
        console.log('- user data:', parsedUser);
      } catch (e) {
        console.log('- user parse error:', e);
      }
    }
    
    // Check AuthService state
    const currentUserValue = this.authService.currentUserValue;
    console.log('AuthService.currentUserValue:', currentUserValue);
    
    // Call isAuthenticated and check result
    const isAuthenticated = this.authService.isAuthenticated();
    console.log('AuthService.isAuthenticated() result:', isAuthenticated);
    
    if (!isAuthenticated) {
      console.log('AuthGuard: BLOCKING access - redirecting to login');
      console.log('Redirect URL will be:', '/login');
      console.log('Return URL will be:', state.url);
      this.router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
      return false;
    }

    // Additional check - verify current user
    const user2FAStatus = currentUserValue?.is2FAEnabled;
    console.log('User 2FA enabled:', user2FAStatus);

    console.log('AuthGuard: ALLOWING access to:', state.url);
    console.log('=== End AuthGuard Debug ===');
    
    return true;
  }
}

// Role-based guard (optional)
@Injectable({
  providedIn: 'root'
})
export class RoleGuard implements CanActivate {
  
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    
    const expectedRoles = next.data['expectedRoles'] as string[];
    
    if (!this.authService.isAuthenticated()) {
      this.router.navigate(['/login']);
      return false;
    }

    if (expectedRoles && !this.authService.hasAnyRole(expectedRoles)) {
      this.router.navigate(['/unauthorized']);
      return false;
    }

    return true;
  }
}