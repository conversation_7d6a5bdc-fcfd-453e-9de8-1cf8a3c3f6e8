import React, { useState } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Controller } from 'react-hook-form';
import { Picker } from '@react-native-picker/picker';
import tw from 'twrnc';
import AppText from '@/components/ui/Text';
import { TextInput, Checkbox, RadioButton } from 'react-native-paper';
import { WasteMaterial, WasteDestination } from '@/types/enums';
import { Ionicons } from '@expo/vector-icons';

interface TechnicalCapacitySectionProps {
  control: any;
  errors: any;
  watch: any;
}

const TechnicalCapacitySection = ({ control, errors, watch }: TechnicalCapacitySectionProps) => {
  const [otherMaterialInputs, setOtherMaterialInputs] = useState<string[]>(['']);
  const wasteSeparation = watch('technicalCapacity.wasteSeparation');
  const wasteDestination = watch('technicalCapacity.wasteDestination');

  const wasteMaterialOptions = [
    { label: 'Biodegradable', value: WasteMaterial.BIODEGRADABLE },
    { label: 'Non-biodegradable', value: WasteMaterial.NON_BIODEGRADABLE },
  ];

  const addOtherMaterialInput = () => {
    setOtherMaterialInputs([...otherMaterialInputs, '']);
  };

  const removeOtherMaterialInput = (index: number) => {
    const newInputs = otherMaterialInputs.filter((_, i) => i !== index);
    setOtherMaterialInputs(newInputs);
  };

  const updateOtherMaterialInput = (index: number, value: string) => {
    const newInputs = [...otherMaterialInputs];
    newInputs[index] = value;
    setOtherMaterialInputs(newInputs);
  };

  return (
    <View style={tw`gap-4`}>

      {/* Waste Separation */}
      <View>
        <AppText style={tw`mb-2`}>Is the waste separately collected from clients?</AppText>
        <Controller
          control={control}
          name="technicalCapacity.wasteSeparation"
          render={({ field: { onChange, value } }) => (
            <View style={tw`gap-2`}>
              <TouchableOpacity
                style={tw`flex-row items-center gap-2`}
                onPress={() => onChange(true)}
              >
                <RadioButton
                  value="true"
                  status={value === true ? 'checked' : 'unchecked'}
                  onPress={() => onChange(true)}
                />
                <AppText>Yes</AppText>
              </TouchableOpacity>
              <TouchableOpacity
                style={tw`flex-row items-center gap-2`}
                onPress={() => onChange(false)}
              >
                <RadioButton
                  value="false"
                  status={value === false ? 'checked' : 'unchecked'}
                  onPress={() => onChange(false)}
                />
                <AppText>No</AppText>
              </TouchableOpacity>
            </View>
          )}
        />
        {errors.technicalCapacity?.wasteSeparation && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.technicalCapacity.wasteSeparation.message}
          </AppText>
        )}
      </View>

      {/* Separated Materials */}
      {wasteSeparation === true && (
        <View>
          <AppText style={tw`mb-2`}>If Yes, please specify the materials *</AppText>
          <Controller
            control={control}
            name="technicalCapacity.separatedMaterials"
            render={({ field: { onChange, value } }) => (
              <View style={tw`gap-2`}>
                {wasteMaterialOptions.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={tw`flex-row items-center gap-2`}
                    onPress={() => {
                      const currentValues = value || [];
                      const isSelected = currentValues.includes(option.value);
                      
                      if (isSelected) {
                        onChange(currentValues.filter((v: WasteMaterial) => v !== option.value));
                      } else {
                        onChange([...currentValues, option.value]);
                      }
                    }}
                  >
                    <Checkbox
                      status={value?.includes(option.value) ? 'checked' : 'unchecked'}
                      onPress={() => {
                        const currentValues = value || [];
                        const isSelected = currentValues.includes(option.value);
                        
                        if (isSelected) {
                          onChange(currentValues.filter((v: WasteMaterial) => v !== option.value));
                        } else {
                          onChange([...currentValues, option.value]);
                        }
                      }}
                    />
                    <AppText style={tw`flex-1`}>{option.label}</AppText>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          />
          {errors.technicalCapacity?.separatedMaterials && (
            <AppText style={tw`text-red-500 text-sm mt-1`}>
              {errors.technicalCapacity.separatedMaterials.message}
            </AppText>
          )}

          {/* Other Separated Materials */}
          <View style={tw`mt-2`}>
            <AppText style={tw`mb-2`}>Other materials (optional)</AppText>
            <Controller
              control={control}
              name="technicalCapacity.otherSeparatedMaterials"
              render={({ field: { onChange, value } }) => (
                <View style={tw`gap-2`}>
                  {otherMaterialInputs.map((input, index) => (
                    <View key={index} style={tw`flex-row items-center gap-2`}>
                      <TextInput
                        mode="outlined"
                        value={input}
                        onChangeText={(text) => {
                          updateOtherMaterialInput(index, text);
                          const newValues = [...otherMaterialInputs];
                          newValues[index] = text;
                          onChange(newValues.filter(v => v.trim() !== ''));
                        }}
                        placeholder={`Other material ${index + 1}`}
                        style={tw`flex-1`}
                      />
                      {otherMaterialInputs.length > 1 && (
                        <TouchableOpacity
                          onPress={() => removeOtherMaterialInput(index)}
                          style={tw`p-2`}
                        >
                          <Ionicons name="remove-circle" size={24} color="red" />
                        </TouchableOpacity>
                      )}
                    </View>
                  ))}
                  <TouchableOpacity
                    onPress={addOtherMaterialInput}
                    style={tw`flex-row items-center gap-2 p-2 border border-dashed border-gray-400 rounded-lg`}
                  >
                    <Ionicons name="add-circle" size={24} color="blue" />
                    <AppText style={tw`text-blue-600`}>Add another material</AppText>
                  </TouchableOpacity>
                </View>
              )}
            />
          </View>
        </View>
      )}

      {/* Waste Destination */}
      <View>
        <AppText style={tw`mb-2`}>Where do you take the waste collected to?</AppText>
        <View style={tw`border border-gray-300 rounded-lg`}>
          <Controller
            control={control}
            name="technicalCapacity.wasteDestination"
            render={({ field: { onChange, value } }) => (
              <Picker
                selectedValue={value}
                onValueChange={onChange}
              >
                <Picker.Item label="Select destination" value="" />
                <Picker.Item 
                  label="Dumpsite/landfill" 
                  value={WasteDestination.DUMPSITE_LANDFILL} 
                />
                <Picker.Item 
                  label="Material recovery/recycling facility" 
                  value={WasteDestination.MATERIAL_RECOVERY_FACILITY} 
                />
                <Picker.Item 
                  label="Others" 
                  value={WasteDestination.OTHERS} 
                />
              </Picker>
            )}
          />
        </View>
        {errors.technicalCapacity?.wasteDestination && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.technicalCapacity.wasteDestination.message}
          </AppText>
        )}
      </View>

      {/* Destination Details */}
      {wasteDestination === WasteDestination.OTHERS && (
        <View>
          <AppText style={tw`mb-2`}>Please specify the destination *</AppText>
          <Controller
            control={control}
            name="technicalCapacity.destinationDetails"
            render={({ field: { onChange, value } }) => (
              <TextInput
                mode="outlined"
                value={value || ''}
                onChangeText={onChange}
                placeholder="Specify destination details"
                error={!!errors.technicalCapacity?.destinationDetails}
              />
            )}
          />
          {errors.technicalCapacity?.destinationDetails && (
            <AppText style={tw`text-red-500 text-sm mt-1`}>
              {errors.technicalCapacity.destinationDetails.message}
            </AppText>
          )}
        </View>
      )}
    </View>
  );
};

export default TechnicalCapacitySection;
