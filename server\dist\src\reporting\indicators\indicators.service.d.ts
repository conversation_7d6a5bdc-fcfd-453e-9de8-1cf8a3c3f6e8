import { PrismaService } from 'src/prisma/prisma.service';
import { IndicatorsRequestDto, IndicatorsResponseDto, GeoMappingRequestDto, GeoMappingResponseDto } from './dto/indicator.dto';
type IndicatorRequestProps = {
    level: "COUNTRY" | "PROVINCE" | "DISTRICT" | "SECTOR" | "CELL" | "VILLAGE";
    levelId: string;
    startDate: Date;
    endDate: Date;
};
export declare class IndicatorsService {
    private prisma;
    constructor(prisma: PrismaService);
    getBasicIndicators(data: IndicatorsRequestDto): Promise<IndicatorsResponseDto>;
    householdWaterSuppy(data: IndicatorRequestProps): Promise<{
        name: string;
        value: number;
        percentage: number;
    }[]>;
    houseHoldSanitation(data: IndicatorRequestProps): Promise<{
        name: string;
        value: number;
        percentage: number;
    }[]>;
    houseHoldHygiene(data: IndicatorRequestProps): Promise<{
        name: string;
        value: number;
        percentage: number;
    }[]>;
    getGeoIndicatorMapping(data: GeoMappingRequestDto): Promise<GeoMappingResponseDto>;
    private getHouseholdCoordinatesQuery;
    private getSchoolCoordinatesQuery;
    private getHealthFacilityCoordinatesQuery;
    schoolWaterSupply(data: IndicatorRequestProps): Promise<{
        name: string;
        value: number;
        percentage: number;
    }[]>;
    schoolSanitation(data: IndicatorRequestProps): Promise<{
        name: string;
        value: number;
        percentage: number;
    }[]>;
    schoolHygiene(data: IndicatorRequestProps): Promise<{
        name: string;
        value: number;
        percentage: number;
    }[]>;
    healthFacilityWaterSupply(data: IndicatorRequestProps): Promise<{
        name: string;
        value: number;
        percentage: number;
    }[]>;
    healthFacilitySanitation(data: IndicatorRequestProps): Promise<{
        name: string;
        value: number;
        percentage: number;
    }[]>;
    healthFacilityHygiene(data: IndicatorRequestProps): Promise<{
        name: string;
        value: number;
        percentage: number;
    }[]>;
    publicPlaceWaterSupply(data: IndicatorRequestProps): Promise<{
        name: string;
        value: number;
        percentage: number;
    }[]>;
    publicPlaceSanitation(data: IndicatorRequestProps): Promise<{
        name: string;
        value: number;
        percentage: number;
    }[]>;
    publicPlaceHygiene(data: IndicatorRequestProps): Promise<{
        name: string;
        value: number;
        percentage: number;
    }[]>;
    private getPublicPlacesByLevel;
}
export {};
