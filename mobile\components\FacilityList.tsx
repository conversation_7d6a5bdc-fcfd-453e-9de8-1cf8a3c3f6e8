import AppText from "@/components/ui/Text";
import { PRIMARY_COLOR } from "@/constants/colors";
import { HealthFacility, Household, PublicPlace, School } from "@/types/facility";
import React from "react";
import { ActivityIndicator, FlatList, ListRenderItem, TouchableOpacity, View } from "react-native";
import tw from "twrnc";

type FacilityListProps =
    | {
        _type: "household";
        data: Household[];
        loading: boolean;
        selectedId?: string;
        onSelect?: (id: string) => void;
    }
    | {
        _type: "publicplace";
        data: PublicPlace[];
        loading: boolean;
        selectedId?: string;
        onSelect?: (id: string) => void;
    }
    | {
        _type: "school";
        data: School[];
        loading: boolean;
        selectedId?: string;
        onSelect?: (id: string) => void;
    }
    | {
        _type: "healthfacility";
        data: HealthFacility[];
        loading: boolean;
        selectedId?: string;
        onSelect?: (id: string) => void;
    };

const FacilityList = ({ data, loading, _type, selectedId, onSelect }: FacilityListProps) => {
    const renderItem: ListRenderItem<typeof data[number]> = ({ item }) => {
        const isSelected = selectedId === item.id;
        return (
            <TouchableOpacity onPress={() => onSelect?.(item.id)}>
                <View
                    style={tw`
                        flex flex-col gap-1 p-4 bg-white rounded-3xl border-2
                        ${isSelected ? 'border-2 shadow-blue-300 border-[' + PRIMARY_COLOR + ']' : 'border-gray-200 shadow-gray-300'}
                        shadow-md
                    `}
                >
                    <View>
                        {_type === "household" && (
                            <AppText weight="bold" style={tw`text-gray-700 text-lg`}>
                                Household: {item.number}
                            </AppText>
                        )}
                        {_type == 'household' ? (
                            <AppText weight="medium" style={tw`text-gray-700`}>
                                Head of Household: {"headOfHouseholdName" in item ? item.headOfHouseholdName : "N/A"}
                            </AppText>
                        ) : (
                        <AppText weight="medium" style={tw`text-gray-700`}>
                            Name: {"name" in item ? item.name : "N/A"}
                        </AppText>
                    )}
                        <AppText weight="medium" style={tw`text-gray-700`}>
                            Number: {item.number}
                        </AppText>
                        <AppText weight="medium" style={tw`text-gray-700`}>
                            Coordinates: {item.location.latitude}, {item.location.longitude}
                        </AppText>
                    </View>
                    <AppText style={tw`text-gray-500`}>
                        {item.location.village.cell.sector.district.name} &gt;{" "}
                        {item.location.village.cell.sector.name} &gt;{" "}
                        {item.location.village.cell.name} &gt; {item.location.village.name}
                    </AppText>
                </View>
            </TouchableOpacity>
        );
    };

    if (loading) {
        return (
            <View style={tw`my-10`}>
                <ActivityIndicator size="large" color={PRIMARY_COLOR} />
            </View>
        );
    }

    if (data.length === 0) {
        return (
            <View style={tw`mt-8 mb-12`}>
                <AppText weight="medium" style={tw`text-gray-500 text-center`}>
                    No data found
                </AppText>
            </View>
        );
    }

    return (
        <FlatList
            contentContainerStyle={tw`gap-4 mt-8 pb-12`}
            data={data}
            renderItem={renderItem}
            keyExtractor={(item) => item.id.toString()}
        />
    );
};

export default FacilityList;
