import { CleanWaterStorageCapacity, EducationLevel, ExcretaManagement, FacilitySlabConstructionMaterial, Gender, HandWashingFacilityType, HandWashingMaterial, MainWaterSource, PwsNonFunctionalityDuration, PwsNonFunctionalityReason, ToiletFacilityCompleteness, ToiletFacilityType, UnimprovedWaterReason, WasteCollectionFrequency, WasteManagementAfterSeparation, WasteTreatmentType, WasteWaterManagement, WaterAvailability, WaterFetchingTime, WaterSourceDistance } from '@/types/enums';
import * as yup from 'yup';
import { subYears } from 'date-fns'

const today = new Date();
const minDate = subYears(today, 78);
const maxDate = subYears(today, 16);

export const householdSchema = yup.object().shape({
  generalInfo: yup.object().shape({
    headOfHouseholdName: yup
      .string()
      .optional(),
    genderOfHead: yup
      .mixed<Gender>()
      .oneOf(Object.values(Gender) as Gender[], 'Invalid gender')
      .required('Gender is required'),
    dateOfBirthOfHead: yup.date()
      .required('Date of birth is required')
      .min(minDate, `Age must be less than or equal to 78 years`)
      .max(maxDate, `Age must be at least 18 years`),
    educationLevelOfHead: yup
      .string()
      .oneOf(Object.keys(EducationLevel), 'Invalid education level')
      .required('Education level is required'),
    householdSize: yup.number().required('Household size is required'),
    childrenUnder18: yup.number().required('Number of children under 18 is required'),
    personsWithDisabilities: yup.number().required('Number of persons with disabilities is required'),
  }),

  waterSupply: yup.object().shape({
    waterSource: yup
      .string()
      .oneOf(Object.keys(MainWaterSource))
      .required('Water source is required'),
    waterAvailability: yup
      .string()
      .oneOf(Object.keys(WaterAvailability))
      .when('waterSource', {
        is: (ws: string) => ws === 'WATER_TAP_WITHIN_HH',
        then: schema => schema.required('Water availability is required'),
        otherwise: schema => schema.optional().nullable(),
      }),
    availableDays: yup
      .string()
      .when(['waterAvailability', 'waterSource'], {
        is: (wa: string, ws: string) => ws === 'WATER_TAP_WITHIN_HH' && wa === 'SOMETIMES_UNAVAILABLE',
        then: schema => schema.required('Available days is required'),
        otherwise: schema => schema.optional(),
      }).nullable(),
    averageWaterCost: yup
      .number()
      .nullable()
      .when('waterSource', {
        is: (ws: string) => ws === 'WATER_TAP_WITHIN_HH',
        then: schema => schema.required('Average water cost is required'),
        otherwise: schema => schema.optional(),
      }),
    storageCapacity: yup
      .string()
      .oneOf(Object.keys(CleanWaterStorageCapacity))
      .required('Storage capacity is required'),
    distanceToSource: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(WaterSourceDistance), 'Distance to water source must be a valid option')
      .nullable()
      .when('waterSource', {
        is: (ws: string) => ws !== 'WATER_TAP_WITHIN_HH',
        then: schema => schema.required('Distance to water source is required'),
        otherwise: schema => schema.notRequired(),
      }),
    timeToFetch: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(WaterFetchingTime), 'Time to fetch must be a valid option')
      .nullable()
      .when('waterSource', {
        is: (ws: string) => ws !== 'WATER_TAP_WITHIN_HH',
        then: schema => schema.required('Time to fetch is required'),
        otherwise: schema => schema.notRequired(),
      }),
    jerryCanPrice: yup
      .number()
      .transform(value => value === "" || value === null ? null : Number(value))
      .nullable()
      .when('waterSource', {
        is: (ws: string) => ws === 'PUBLIC_WATER_TAP_KIOSK',
        then: schema => schema.required('Jerry can price is required'),
        otherwise: schema => schema.notRequired(),
      }),
    unimprovedReason: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(UnimprovedWaterReason), 'Unimproved reason must be a valid option')
      .nullable()
      .when('waterSource', {
        is: (ws: string) => ['UNIMPROVED_SPRINGS', 'SURFACE_WATER', 'RAIN_WATER'].includes(ws),
        then: schema => schema.required('Unimproved reason is required'),
        otherwise: schema => schema.notRequired(),
      }),
    pwsNonFunctionalityReason: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(PwsNonFunctionalityReason), 'PWS non-functionality reason must be a valid option')
      .nullable()
      .when('unimprovedReason', {
        is: (ur: string) => ur === 'NEARBY_SOURCE_NOT_FUNCTIONING',
        then: schema => schema.required('PWS non-functionality reason is required'),
        otherwise: schema => schema.notRequired(),
      }),
    pwsNonFunctionalityDuration: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(PwsNonFunctionalityDuration), 'PWS non-functionality duration must be a valid option')
      .nullable()
      .when('unimprovedReason', {
        is: (ur: string) => ur === 'NEARBY_SOURCE_NOT_FUNCTIONING',
        then: schema => schema.required('PWS non-functionality duration is required'),
        otherwise: schema => schema.notRequired(),
      }),
  }),

  sanitation: yup.object().shape({
    toiletType: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(ToiletFacilityType), 'Invalid toilet type')
      .nullable()
      .required('Toilet type is required'),
    toiletCompleteness: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(ToiletFacilityCompleteness), 'Invalid toilet completeness')
      .nullable()
      .when('toiletType', {
        is: (tt: string) => [
          'PIT_WITH_SLAB',
          'PIT_WITHOUT_SLAB',
          'VENTILATED_IMPROVED_PIT',
          'COMPOSTING_TOILET',
          'URINE_DIVERSION_DRY_TOILET',
        ].includes(tt),
        then: schema => schema.required('Toilet completeness is required'),
        otherwise: schema => schema.notRequired(),
      }),
    slabConstructionMaterial: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(FacilitySlabConstructionMaterial), 'Invalid slab construction material')
      .nullable()
      .when('toiletType', {
        is: (tt: string) => [
          'PIT_WITH_SLAB',
          'PIT_WITHOUT_SLAB',
          'VENTILATED_IMPROVED_PIT',
          'COMPOSTING_TOILET',
          'URINE_DIVERSION_DRY_TOILET',
        ].includes(tt),
        then: schema => schema.required('Slab construction material is required'),
        otherwise: schema => schema.notRequired(),
      }),
    toiletShared: yup
      .boolean()
      .when('toiletType', {
        is: (tt: string) => [
          'PIT_WITH_SLAB',
          'PIT_WITHOUT_SLAB',
          'VENTILATED_IMPROVED_PIT',
          'COMPOSTING_TOILET',
          'URINE_DIVERSION_DRY_TOILET',
        ].includes(tt),
        then: schema => schema.required('This field is required'),
        otherwise: schema => schema.notRequired(),
      }),
    hasToiletFullInLast2Years: yup.boolean().required('This field is required'),
    excretaManagement: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(ExcretaManagement), 'Invalid excreta management')
      .nullable()
      .when('hasToiletFullInLast2Years', {
        is: true,
        then: schema => schema.required('Excreta management is required'),
        otherwise: schema => schema.notRequired(),
      }),
  }),

  hygiene: yup.object().shape({
    handwashingFacility: yup
      .boolean()
      .when(['../sanitation.toiletType'], {
        is: (toiletType: string) => toiletType !== 'NO_FACILITY',
        then: schema => schema.required('Handwashing facility is required'),
        otherwise: schema => schema.notRequired(),
      }),
    handWashingFacilityType: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(HandWashingFacilityType), 'Invalid handwashing facility type')
      .nullable()
      .when('handwashingFacility', {
        is: true,
        then: schema => schema.required('Handwashing facility type is required'),
        otherwise: schema => schema.notRequired(),
      }),
    handwashingMaterials: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(HandWashingMaterial), 'Invalid handwashing material')
      .nullable()
      .when('handwashingFacility', {
        is: true,
        then: schema => schema.required('Handwashing materials are required'),
        otherwise: schema => schema.notRequired(),
      }),
  }),

  solidWaste: yup.object().shape({
    wasteSeparation: yup.boolean().required('Waste separation is required'),
    wasteManagement: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(WasteManagementAfterSeparation), 'Invalid waste management')
      .nullable()
      .when('wasteSeparation', {
        is: true,
        then: schema => schema.required('Waste management is required'),
        otherwise: schema => schema.notRequired(),
      }),
    treatmentType: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(WasteTreatmentType), 'Invalid treatment type')
      .nullable()
      .required('Treatment type is required'),
    collectionFrequency: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(WasteCollectionFrequency), 'Invalid collection frequency')
      .nullable()
      .required('Collection frequency is required'),
  }),

  liquidWaste: yup.object().shape({
    wasterWaterManagement: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(WasteWaterManagement), 'Invalid waste water management')
      .nullable()
      .required('Waste water management is required'),
  }),
});
