{"version": 3, "file": "waste-collection-company.dto.js", "sourceRoot": "", "sources": ["../../../../../src/data-collection/submission/dto/waste-collection-company.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAC9C,qDAA0G;AAE1G,2CAA2H;AAE3H,MAAa,yCAAyC;IAGpD,WAAW,CAAS;IAIpB,SAAS,CAAS;IAIlB,WAAW,CAAS;IAIpB,YAAY,CAAS;IAIrB,YAAY,CAAS;IAIrB,WAAW,CAAsB;IAKjC,gBAAgB,CAAU;IAI1B,cAAc,CAAS;IAIvB,eAAe,CAAS;IAIxB,aAAa,CAAS;IAKtB,WAAW,CAAe;IAM1B,gBAAgB,CAAY;IAI5B,eAAe,CAAU;IAKzB,kBAAkB,CAAkB;IAMpC,uBAAuB,CAAY;IAInC,gBAAgB,CAAmB;IAKnC,kBAAkB,CAAU;IAI5B,WAAW,CAAU;IAKrB,eAAe,CAAmB;IAKlC,oBAAoB,CAAU;CAC/B;AA1FD,8FA0FC;AAvFC;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;IACvD,IAAA,0BAAQ,GAAE;;8EACS;AAIpB;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACpC,IAAA,0BAAQ,GAAE;;4EACO;AAIlB;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,eAAM,EAAE,CAAC;IAC7B,IAAA,wBAAM,EAAC,eAAM,CAAC;;8EACK;AAIpB;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC;IACzC,IAAA,0BAAQ,GAAE;;+EACU;AAIrB;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IAChD,IAAA,0BAAQ,GAAE;;+EACU;AAIrB;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,4BAAmB,EAAE,CAAC;IAC1C,IAAA,wBAAM,EAAC,4BAAmB,CAAC;;8EACK;AAKjC;IAHC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mFACe;AAI1B;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;IAC5B,IAAA,uBAAK,GAAE;;iFACe;AAIvB;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;IAC5B,IAAA,uBAAK,GAAE;;kFACgB;AAIxB;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;IAC5B,IAAA,uBAAK,GAAE;;gFACc;AAKtB;IAHC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,mBAAU,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAChD,IAAA,yBAAO,GAAE;IACT,IAAA,wBAAM,EAAC,mBAAU,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;8EACT;AAM1B;IAJC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;mFACG;AAI5B;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC9B,IAAA,2BAAS,GAAE;;kFACa;AAKzB;IAHC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,sBAAa,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IACnD,IAAA,yBAAO,GAAE;IACT,IAAA,wBAAM,EAAC,sBAAa,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;qFACF;AAMpC;IAJC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;0FACU;AAInC;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,yBAAgB,EAAE,CAAC;IACvC,IAAA,wBAAM,EAAC,yBAAgB,CAAC;;mFACU;AAKnC;IAHC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;qFACiB;AAI5B;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC9B,IAAA,2BAAS,GAAE;;8EACS;AAKrB;IAHC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,wBAAe,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACvD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,wBAAe,CAAC;;kFACU;AAKlC;IAHC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uFACmB;AAGhC,MAAa,2CAA2C;IAEtD,EAAE,CAAS;IAGX,YAAY,CAAS;IAGrB,WAAW,CAAO;IAGlB,WAAW,CAAS;IAGpB,SAAS,CAAS;IAGlB,WAAW,CAAS;IAGpB,YAAY,CAAS;IAGrB,YAAY,CAAS;IAGrB,WAAW,CAAsB;IAGjC,gBAAgB,CAAU;IAG1B,cAAc,CAAS;IAGvB,eAAe,CAAS;IAGxB,aAAa,CAAS;IAGtB,WAAW,CAAe;IAG1B,gBAAgB,CAAY;IAG5B,eAAe,CAAU;IAGzB,kBAAkB,CAAkB;IAGpC,uBAAuB,CAAY;IAGnC,gBAAgB,CAAmB;IAGnC,kBAAkB,CAAU;IAG5B,WAAW,CAAU;IAGrB,eAAe,CAAmB;IAGlC,oBAAoB,CAAU;CAC/B;AArED,kGAqEC;AAnEC;IADC,IAAA,qBAAW,GAAE;;uEACH;AAGX;IADC,IAAA,qBAAW,GAAE;;iFACO;AAGrB;IADC,IAAA,qBAAW,GAAE;8BACD,IAAI;gFAAC;AAGlB;IADC,IAAA,qBAAW,GAAE;;gFACM;AAGpB;IADC,IAAA,qBAAW,GAAE;;8EACI;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,eAAM,EAAE,CAAC;;gFACV;AAGpB;IADC,IAAA,qBAAW,GAAE;;iFACO;AAGrB;IADC,IAAA,qBAAW,GAAE;;iFACO;AAGrB;IADC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,4BAAmB,EAAE,CAAC;;gFACV;AAGjC;IADC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;;qFACP;AAG1B;IADC,IAAA,qBAAW,GAAE;;mFACS;AAGvB;IADC,IAAA,qBAAW,GAAE;;oFACU;AAGxB;IADC,IAAA,qBAAW,GAAE;;kFACQ;AAGtB;IADC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,mBAAU,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;gFACvB;AAG1B;IADC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;;qFACrB;AAG5B;IADC,IAAA,qBAAW,GAAE;;oFACW;AAGzB;IADC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,sBAAa,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;uFAChB;AAGpC;IADC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;;4FACd;AAGnC;IADC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,yBAAgB,EAAE,CAAC;;qFACL;AAGnC;IADC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;;uFACL;AAG5B;IADC,IAAA,qBAAW,GAAE;;gFACO;AAGrB;IADC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,wBAAe,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;;oFACtB;AAGlC;IADC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;;yFACH"}