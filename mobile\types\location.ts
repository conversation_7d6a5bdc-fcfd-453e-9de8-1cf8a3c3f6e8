import { SettlementType } from "./enums"

export interface Province {
    id: number
    code: number
    name: string
}

export interface District {
    id: number
    code: number
    name: string
    province: Province
}

export interface Sector {
    id: number
    code: number
    name: string
    district: District
}

export interface Cell {
    id: number
    code: number
    name: string
    sector: Sector
}

export interface Village {
    id: number
    code: number
    name: string
    cell: Cell
}

export interface Location {
    id: string
    villageId: number
    village: Village
    latitude?: number
    longitude?: number
    settlementType: SettlementType
}

