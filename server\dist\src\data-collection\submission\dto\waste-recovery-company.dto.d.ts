import { ServiceProviderType, OperationType } from '@prisma/client';
export declare class HandledMaterialDto {
    materialName: string;
    supplier: string;
    quantityPerDay: number;
}
export declare class BusinessSiteDto {
    name: string;
    type: string;
    villageId: number;
    latitude?: number;
    longitude?: number;
}
export declare class BusinessSiteResponseDto {
    id: string;
    name: string;
    type: string;
    locationId: string;
    location: {
        id: string;
        latitude?: number;
        longitude?: number;
        villageId: number;
    };
}
export declare class CreateWasteRecoveryCompanySubmissionDto {
    companyName: string;
    contactPerson: string;
    contactPhone: string;
    contactEmail: string;
    companyType: ServiceProviderType;
    otherCompanyType?: string;
    totalPersonnel: number;
    femalePersonnel: number;
    malePersonnel: number;
    operationType: OperationType;
    handledMaterials: HandledMaterialDto[];
    businessSites: BusinessSiteDto[];
}
export declare class WasteRecoveryCompanySubmissionResponseDto {
    id: string;
    submissionId: string;
    submittedAt: Date;
    companyName: string;
    contactPerson: string;
    contactPhone: string;
    contactEmail: string;
    companyType: ServiceProviderType;
    otherCompanyType?: string;
    totalPersonnel: number;
    femalePersonnel: number;
    malePersonnel: number;
    operationType: OperationType;
    handledMaterials: HandledMaterialDto[];
    businessSites: BusinessSiteResponseDto[];
}
