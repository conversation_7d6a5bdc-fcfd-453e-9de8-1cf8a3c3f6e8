<div class="page-header">
  <h1 class="page-title">All Users</h1>
  <p class="page-subtitle">List of all users in the system with their roles</p>
</div>
<div class="main-content">
  <div class="tab-navigation">
    <div class="tab-buttons">
      <button 
        class="tab-button" 
        [class.active]="activeTab === 'all'"
        (click)="switchTab('all')"
      >
        All Users
      </button>
      <button 
        class="tab-button" 
        [class.active]="activeTab === 'inactive'"
        (click)="switchTab('inactive')"
      >
        Inactive Users
      </button>
    </div>
    
    <div class="tab-actions">
      <p-button 
        label="Add User" 
        icon="pi pi-plus" 
        (onClick)="openNew()" 
        styleClass="add-user-btn"
      />
      <p-button 
        label="Import" 
        icon="pi pi-upload" 
        severity="secondary"
        [outlined]="true"
        (onClick)="openImportDialog()"
        styleClass="import-btn"
      />
      <!-- <p-dropdown
        [(ngModel)]="selectedFilter" 
        [options]="filterOptions" 
        placeholder="Filter by"
        optionLabel="label"
        optionValue="value"
        styleClass="filter-dropdown"
        (onChange)="onFilterChange()"
      /> -->
      <p-button 
        label="Export" 
        icon="pi pi-download" 
        severity="secondary"
        [outlined]="true"
        (onClick)="exportCSV()"
        styleClass="export-btn"
      />
    </div>
  </div>
  
  <div class="content-container">
    <div class="search-bar">
      <p-iconField iconPosition="left" styleClass="search-field">
        <!-- <p-inputIcon styleClass="pi pi-search"></p-inputIcon> -->
        <input 
          type="text" 
          pInputText 
          placeholder="Search..." 
          [(ngModel)]="searchValue"
          (input)="onSearch($event)"
          class="search-input"
        />
      </p-iconField>
      
      <p-button 
        icon="pi pi-filter" 
        severity="secondary" 
        [outlined]="true"
        styleClass="filter-btn"
        (onClick)="toggleFilters()"
      />
    </div>
  
    <!-- Data Table -->
    <p-table
      #dt
      [value]="users"
      [rows]="itemsPerPage"
      [paginator]="true"
      [globalFilterFields]="['firstName', 'lastName', 'email', 'role.name', 'telephoneNumber']"
      [tableStyle]="{ 'min-width': '75rem' }"
      [(selection)]="selectedUsers"
      [rowHover]="true"
      dataKey="id"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      [showCurrentPageReport]="true"
      [loading]="loading"
      [totalRecords]="totalRecords"
      styleClass="users-table"
      [showFirstLastIcon]="false"
    >
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 3rem">
            <p-tableHeaderCheckbox />
          </th>
          <th style="min-width: 4rem">No.</th>
          <th style="min-width: 10rem">Names</th>
          <th style="min-width: 8rem">Role</th>
          <th style="min-width: 12rem">Telephone</th>
          <th style="min-width: 12rem">Location</th>
          <th style="min-width: 8rem">Activity</th>
          <th style="min-width: 12rem">Actions</th>
        </tr>
      </ng-template>
  
      <ng-template pTemplate="body" let-user let-rowIndex="rowIndex">
        <tr>
          <td style="width: 3rem">
            <p-tableCheckbox [value]="user" />
          </td>
          <td>
            <span class="row-number">{{ String(rowIndex + 1).padStart(2, '0') }}</span>
          </td>
          <td>
            <span class="user-name">{{ user.firstName }} {{ user.lastName }}</span>
          </td>
          <td>
            <span class="user-role">{{ user.role?.name }}</span>
          </td>
          <td>
            <span class="user-phone">{{ user.telephoneNumber }}</span>
          </td>
          <td>
            <div *ngIf="user.locations?.length > 0 && user.locations[0]">
              <span class="user-location">
                {{ user.locations[0]?.village?.name }},
                {{ user.locations[0]?.cell?.name }},
                {{ user.locations[0]?.sector?.name }},
                {{ user.locations[0]?.district?.name }},
                {{ user.locations[0]?.province?.name }}
              </span>
            </div>
                    </td>
          <td>
            <p-tag 
              [value]="user.status" 
              [severity]="getStatusSeverity(user.status)"
              styleClass="status-tag"
            />
          </td>
          <td>
            <div class="table-actions">
              <p-button 
                icon="pi pi-pencil" 
                [text]="true"
                [rounded]="true"
                size="small"
                (click)="editUser(user)"
                pTooltip="Edit User"
                styleClass="edit-btn"
              />
              <p-button 
                icon="pi pi-trash" 
                severity="danger"
                [text]="true"
                [rounded]="true"
                size="small"
                (click)="deleteUser(user)"
                pTooltip="Delete User"
                styleClass="delete-btn"
              />
            </div>
          </td>
        </tr>
      </ng-template>
  
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="8" class="empty-state">
            <div class="empty-content">
              <i class="pi pi-users empty-icon"></i>
              <h3>No users found</h3>
              <p>Try adjusting your search or filter criteria</p>
            </div>
          </td>
        </tr>
      </ng-template>
  
      <ng-template pTemplate="paginatorleft">
        <div class="paginator-info">
          <span>Items per page:</span>
          <p-dropdown
            [(ngModel)]="itemsPerPage" 
            [options]="rowsPerPageOptions"
            (onChange)="onPageSizeChange()"
            styleClass="items-per-page-dropdown"
            optionLabel="label"
            optionValue="value"
          />
        </div>
      </ng-template>
    </p-table>
    <div class="bulk-actions-bar" *ngIf="selectedUsers.length > 0">
      <div class="bulk-info">
        <span>{{ selectedUsers.length }} user(s) selected</span>
      </div>
      <div class="bulk-actions">
        <p-button 
          label="Delete Selected" 
          icon="pi pi-trash" 
          severity="danger"
          [outlined]="true"
          (onClick)="deleteSelectedUsers()"
        />
      </div>
    </div>
  </div>
</div>

<p-dialog 
  [(visible)]="userDialog" 
  [style]="{ width: '45%', height: '80vh', background:'white' }" 
  header="{{ dialogTitle }}"
  [modal]="true"
  styleClass="user-dialog"
  [closable]="true"
  [draggable]="false"
  [resizable]="false"
  [appendTo]="'body'"
>
  <span class="p-text-secondary block mb-8">
    Fill in the details to {{ isEditMode ? 'update the' : 'create a new' }} user
  </span>

  <!-- First Name and Last Name -->
  <div class="form-row">
    <div class="form-field">
      <label for="firstName" class="font-semibold">First name <span class="required">*</span></label>
      <input 
        type="text" 
        pInputText 
        id="firstName" 
        [(ngModel)]="user.firstName" 
        required 
        autofocus 
        class="flex-auto"
      />
      <small class="field-error" *ngIf="submitted && !user.firstName">
        First name is required.
      </small>
    </div>
    <div class="form-field">
      <label for="lastName" class="font-semibold">Last name <span class="required">*</span></label>
      <input 
        type="text" 
        pInputText 
        id="lastName" 
        [(ngModel)]="user.lastName" 
        required 
        class="flex-auto"
      />
      <small class="field-error" *ngIf="submitted && !user.lastName">
        Last name is required.
      </small>
    </div>
  </div>

  <!-- Email Address -->
  <div class="form-field">
    <label for="email" class="font-semibold">Email Address <span class="required">*</span></label>
    <input 
      type="email" 
      pInputText 
      id="email" 
      [(ngModel)]="user.email" 
      required 
      class="flex-auto"
    />
    <small class="field-error" *ngIf="submitted && !user.email">
      Email is required.
    </small>
  </div>

  <!-- Phone Number -->
  <div class="form-field">
    <label for="phone" class="font-semibold">Phone Number <span class="required">*</span></label>
    <input 
      type="tel" 
      pInputText 
      id="phone" 
      [(ngModel)]="user.telephoneNumber" 
      required 
      class="flex-auto"
      style="margin-bottom: 10px;"
    />
    <small class="field-error" *ngIf="submitted && !user.telephoneNumber">
      Phone number is required.
    </small>
  </div>

  <div class="form-field">
    <label for="role" class="font-semibold">Role <span class="required">*</span></label>
    <p-dropdown
      [(ngModel)]="user.roleId" 
      inputId="role" 
      [options]="roles" 
      optionLabel="label" 
      optionValue="value"
      placeholder="Select a Role" 
      [loading]="loadingRoles"
      [appendTo]="'body'"
      class="width:full"
    />
    <small class="field-error" *ngIf="submitted && !user.roleId">
      Role is required.
    </small>
  </div>

  <div class="location-section">        
    <div class="form-row">
      <div class="form-field">
        <label for="province" class="font-semibold">Province <span class="required">*</span></label>
        <p-dropdown
          [(ngModel)]="user.provinceId" 
          inputId="province" 
          [options]="provinces" 
          optionLabel="label" 
          optionValue="value"
          [filter]="true"
          [showClear]="true"
          filterBy="label"
          placeholder="Select Province" 
          [loading]="loadingProvinces"
          [appendTo]="'body'"
          class="flex-auto"
          (onChange)="onProvinceChange()"
        />
        <small class="field-error" *ngIf="submitted && !user.provinceId">
          Province is required.
        </small>
      </div>
      
      <div class="form-field" *ngIf="user.provinceId">
        <label for="district" class="font-semibold">District <span class="required">*</span></label>
        <p-dropdown
          [(ngModel)]="user.districtId" 
          inputId="district" 
          [options]="districts" 
          optionLabel="label" 
          optionValue="value"
          [filter]="true"
          [showClear]="true"
          filterBy="label"
          placeholder="Select District" 
          [loading]="loadingDistricts"
          [appendTo]="'body'"
          class="flex-auto"
          (onChange)="onDistrictChange()"
        />
        <small class="field-error" *ngIf="submitted && !user.districtId">
          District is required.
        </small>
      </div>
    </div>
    
    <div class="form-row" *ngIf="user.districtId">
      <div class="form-field">
        <label for="sector" class="font-semibold">Sector <span class="required">*</span></label>
        <p-dropdown
          [(ngModel)]="user.sectorId" 
          inputId="sector" 
          [options]="sectors" 
          optionLabel="label" 
          optionValue="value"
          [filter]="true"
          [showClear]="true"
          filterBy="label"
          placeholder="Select Sector" 
          [loading]="loadingSectors"
          [appendTo]="'body'"
          class="flex-auto"
          (onChange)="onSectorChange()"
        />
        <small class="field-error" *ngIf="submitted && !user.sectorId">
          Sector is required.
        </small>
      </div>
      
      <div class="form-field" *ngIf="user.sectorId">
        <label for="cell" class="font-semibold">Cell <span class="required">*</span></label>
        <p-dropdown
          [(ngModel)]="user.cellId" 
          inputId="cell" 
          [options]="cells" 
          optionLabel="label" 
          optionValue="value"
          [filter]="true"
          [showClear]="true"
          filterBy="label"
          placeholder="Select Cell" 
          [loading]="loadingCells"
          [appendTo]="'body'"
          class="flex-auto"
          (onChange)="onCellChange()"
        />
        <small class="field-error" *ngIf="submitted && !user.cellId">
          Cell is required.
        </small>
      </div>
    </div>
    
    <div class="form-field" *ngIf="user.cellId">
      <label for="village" class="font-semibold">Village <span class="required">*</span></label>
      <p-dropdown
        [(ngModel)]="user.villageId" 
        inputId="village" 
        [options]="villages" 
        optionLabel="label" 
        optionValue="value"
        [filter]="true"
        [showClear]="true"
        filterBy="label"
        placeholder="Select Village" 
        [loading]="loadingVillages"
        [appendTo]="'body'"
        class="flex-auto"
      />
      <small class="field-error" *ngIf="submitted && !user.villageId">
        Village is required.
      </small>
    </div>
  </div>

  <!-- Simple footer buttons -->
  <div class="flex justify-end gap-2 mt-8">
    <p-button 
      label="Cancel" 
      severity="secondary"
      (click)="hideDialog()" 
      styleClass="cancel-btn"
    />
    <p-button 
      [label]="isEditMode ? 'Update User' : 'Create User'"
      (click)="saveUser()"
      [loading]="saving"
      [disabled]="!isFormValid() || saving"
      styleClass="save-btn"
    />
  </div>
</p-dialog>
<p-dialog 
  [(visible)]="showImportDialog" 
  header="Import Users"
  [style]="{ width: '450px', background:'white', padding:'20px' }"
  [modal]="true"
>
  <ng-template pTemplate="content">
    <p-fileUpload 
      mode="basic" 
      name="users"
      accept=".csv,.xlsx"
      maxFileSize="10000000"
      (onSelect)="importUsers($event)"
      chooseLabel="Choose File"
      class="w-full"
    />
    <small class="block mt-2">
      Supported formats: CSV, Excel (.xlsx)
    </small>
  </ng-template>
  
  <ng-template pTemplate="footer">
    <p-button 
      label="Cancel" 
      [text]="true"
      (click)="showImportDialog = false"
    />
  </ng-template>
</p-dialog>

<!-- Confirmation Dialog -->
<p-confirmDialog [style]="{ width: '450px' , background:'white',padding:'20px'}" />

<!-- Toast Messages -->
<p-toast position="top-right" />