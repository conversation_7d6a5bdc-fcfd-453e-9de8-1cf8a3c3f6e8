import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  
  constructor(private authService: AuthService) {}

  intercept(req: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
    const authToken = this.authService.getToken();
        if (req.url.includes('localhost:8080/api/v1') && authToken) {
      const authReq = req.clone({
        headers: req.headers.set('Authorization', `Bearer ${authToken}`)
      });
      console.log('AuthInterceptor: Adding token to request:', req.url);
      
      return next.handle(authReq).pipe(
        catchError((error) => {
          // If we get a 401 error, it means the token is invalid
          if (error.status === 401) {
            console.log('AuthInterceptor: 401 error, logging out user');
            this.authService.logout();
          }
          return throwError(() => error);
        })
      );
    }
    
    // If no token or not an API request, proceed without modification
    return next.handle(req);
  }
}