import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { IndicatorsService } from './indicators.service';
import { ApiBearerAuth } from '@nestjs/swagger';
import { PrivilegeGuard } from 'src/common/guards/privilege.guard';
import { MobileGuard } from 'src/common/guards/mobile.guard';
import { AuthGuard } from 'src/common/guards/auth.guard';
import { IndicatorsRequestDto, GeoMappingRequestDto } from './dto/indicator.dto';
import { Privileges } from 'src/common/decorators/privileges.decorator';
import { Privilege } from '@prisma/client';

@ApiBearerAuth()
@UseGuards(AuthGuard, MobileGuard, PrivilegeGuard)
// @Privileges(Privilege.DATA_REPORTING)
@Controller('indicators')
export class IndicatorsController {

    constructor(private indicatorsService: IndicatorsService) {}

    @Get()
    async getBasicIndicators(@Query() query: IndicatorsRequestDto) {
        return await this.indicatorsService.getBasicIndicators(query);
    }

    @Get('geo-mapping')
    async getGeoIndicatorMapping(@Query() query: GeoMappingRequestDto) {
        return await this.indicatorsService.getGeoIndicatorMapping(query);
    }
}
