<div class="min-h-screen flex">
  <!-- Left Panel -->
  <div class="w-1/2 bg-[#2078FF] flex items-center justify-center">
    <div class="flex flex-col items-center">
      <!-- Inline SVG Logo -->
      <svg width="200" height="245" viewBox="0 0 200 245" fill="none" xmlns="http://www.w3.org/2000/svg" class="mb-8">
        <rect width="200" height="200" rx="20" fill="white"/>
        <path d="M121.562 62.3828C115.772 55.6958 109.256 49.6743 102.133 44.4297C101.502 43.988 100.751 43.751 99.9813 43.751C99.2114 43.751 98.4602 43.988 97.8297 44.4297C90.72 49.6765 84.2159 55.6979 78.4375 62.3828C65.5516 77.1812 58.75 92.7812 58.75 107.5C58.75 118.44 63.096 128.932 70.8318 136.668C78.5677 144.404 89.0598 148.75 100 148.75C110.94 148.75 121.432 144.404 129.168 136.668C136.904 128.932 141.25 118.44 141.25 107.5C141.25 92.7812 134.448 77.1812 121.562 62.3828ZM100 141.25C91.052 141.24 82.4733 137.681 76.1461 131.354C69.8189 125.027 66.2599 116.448 66.25 107.5C66.25 80.6734 92.2516 58.2812 100 52.1875C107.748 58.2812 133.75 80.664 133.75 107.5C133.74 116.448 130.181 125.027 123.854 131.354C117.527 137.681 108.948 141.24 100 141.25ZM126.198 111.878C125.226 117.31 122.613 122.313 118.711 126.214C114.809 130.115 109.805 132.727 104.373 133.698C104.167 133.731 103.959 133.748 103.75 133.75C102.809 133.75 101.903 133.396 101.211 132.759C100.519 132.122 100.092 131.248 100.014 130.31C99.936 129.373 100.213 128.441 100.791 127.698C101.368 126.955 102.204 126.457 103.131 126.302C110.898 124.994 117.489 118.403 118.806 110.622C118.973 109.641 119.522 108.766 120.334 108.191C121.145 107.615 122.152 107.385 123.133 107.552C124.114 107.718 124.988 108.268 125.564 109.079C126.14 109.89 126.37 110.897 126.203 111.878H126.198Z" fill="#2078FF"/>
        <path d="M61.3409 237L54.5682 213.727H60.7841L64.2955 228.977H64.4886L68.5 213.727H73.5682L77.5795 229.011H77.7727L81.2955 213.727H87.5L80.7386 237H75.3182L71.125 222.886H70.9432L66.75 237H61.3409ZM92.3267 237H86.2812L94.1335 213.727H101.622L109.474 237H103.429L97.9631 219.591H97.7812L92.3267 237ZM91.5199 227.841H104.156V232.114H91.5199V227.841ZM123.841 220.705C123.765 219.871 123.428 219.223 122.83 218.761C122.239 218.292 121.394 218.057 120.295 218.057C119.568 218.057 118.962 218.152 118.477 218.341C117.992 218.53 117.629 218.792 117.386 219.125C117.144 219.451 117.019 219.826 117.011 220.25C116.996 220.598 117.064 220.905 117.216 221.17C117.375 221.436 117.602 221.67 117.898 221.875C118.201 222.072 118.564 222.246 118.989 222.398C119.413 222.549 119.89 222.682 120.42 222.795L122.42 223.25C123.572 223.5 124.587 223.833 125.466 224.25C126.352 224.667 127.095 225.163 127.693 225.739C128.299 226.314 128.758 226.977 129.068 227.727C129.379 228.477 129.538 229.318 129.545 230.25C129.538 231.72 129.167 232.981 128.432 234.034C127.697 235.087 126.64 235.894 125.261 236.455C123.89 237.015 122.235 237.295 120.295 237.295C118.348 237.295 116.652 237.004 115.205 236.42C113.758 235.837 112.633 234.951 111.83 233.761C111.027 232.572 110.614 231.068 110.591 229.25H115.977C116.023 230 116.223 230.625 116.58 231.125C116.936 231.625 117.424 232.004 118.045 232.261C118.674 232.519 119.402 232.648 120.227 232.648C120.985 232.648 121.629 232.545 122.159 232.341C122.697 232.136 123.11 231.852 123.398 231.489C123.686 231.125 123.833 230.708 123.841 230.239C123.833 229.799 123.697 229.424 123.432 229.114C123.167 228.795 122.758 228.523 122.205 228.295C121.659 228.061 120.962 227.845 120.114 227.648L117.682 227.08C115.667 226.617 114.08 225.871 112.92 224.841C111.761 223.803 111.186 222.402 111.193 220.636C111.186 219.197 111.572 217.936 112.352 216.852C113.133 215.769 114.212 214.924 115.591 214.318C116.97 213.712 118.542 213.409 120.307 213.409C122.11 213.409 123.674 213.716 125 214.33C126.333 214.936 127.367 215.788 128.102 216.886C128.837 217.985 129.212 219.258 129.227 220.705H123.841ZM132.386 237V213.727H138.011V223.068H147.182V213.727H152.795V237H147.182V227.648H138.011V237H132.386Z" fill="white"/>
      </svg>
      <p class="text-white text-base max-w-xs text-center">
        WASH MIS is a comprehensive system that monitors and manages water supply, sanitation, and hygiene services and makes interventions all over the country.
      </p>
    </div>
  </div>
  <!-- Right Panel -->
  <div class="w-1/2 bg-gray-50 flex items-center justify-center">
    <div class="bg-white rounded-2xl border border-gray-200 shadow-sm p-12 w-full max-w-xl min-h-[450px] flex flex-col justify-center">
      <div class="flex flex-col">
        <svg class="w-10 h-10 mb-6 text-gray-400" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><rect x="7" y="11" width="10" height="8" rx="2"/><path d="M12 15v2"/><path d="M9 11V7a3 3 0 1 1 6 0v4"/></svg>
        <h2 class="text-2xl font-bold text-gray-900 mb-1">Forgot Password?</h2>
        <p class="text-gray-500 mb-8">No worries! We'll send you reset instructions</p>
      </div>
      <form [formGroup]="forgotForm" (ngSubmit)="onSubmit()" class="space-y-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
          <input type="email" formControlName="email" class="w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 " placeholder="Enter your email">
        </div>
        <button type="submit" [disabled]="forgotForm.invalid || loading" class="w-full py-2.5 px-4 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md">
          {{ loading ? 'Sending...' : 'Reset Password' }}
        </button>
      </form>
      <div *ngIf="message" class="mt-4 text-green-600 text-center">{{ message }}</div>
      <div *ngIf="error" class="mt-4 text-red-600 text-center">{{ error }}</div>
      <div class="flex items-center mt-8">
        <a routerLink="/auth/login" class="flex items-center text-base font-medium text-black hover:underline">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7"/>
          </svg>
          Go to login
        </a>
      </div>
    </div>
  </div>
</div>
