import AppText from '@/components/ui/Text';
import * as enums from '@/types/enums';
import { getEnumOptions } from '@/utils/enum';
import { Picker } from '@react-native-picker/picker';
import React from 'react';
import { Controller, useWatch } from 'react-hook-form';
import { View } from 'react-native';
import { TextInput } from 'react-native-paper';
import tw from 'twrnc';

const SolidWasteSection = ({ control, errors }: any) => {

    const wasteSeparation = useWatch({ control, name: 'solidWaste.wasteSeparation' });
    const wasteManagement = useWatch({ control, name: 'solidWaste.wasteManagement' });

    return (
        <>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Do you separate waste?</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="solidWaste.wasteSeparation"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value ? 'yes' : 'no'} onValueChange={v => onChange(v === 'yes')}>
                                <Picker.Item label="Select Option" value="" />
                                <Picker.Item label="Yes" value="yes" />
                                <Picker.Item label="No" value="no" />
                            </Picker>
                        )}
                    />
                </View>
                {errors.solidWaste?.wasteSeparation && <AppText style={tw`text-red-500`}>{errors.solidWaste?.wasteSeparation.message}</AppText>}
            </View>

            { wasteSeparation &&
                <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>How is waste managed after separation?</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="solidWaste.wasteManagement"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={onChange}>
                                <Picker.Item label="Select Management" value="" />
                                {getEnumOptions(enums.WasteManagementAfterSeparation).map(opt => (
                                    <Picker.Item key={opt.value} label={opt.label} value={opt.value as string} />
                                ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.solidWaste?.wasteManagement && <AppText style={tw`text-red-500`}>{errors.solidWaste?.wasteManagement.message}</AppText>}
            </View>}

            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>What type of treatment is applied to the waste?</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="solidWaste.treatmentType"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={onChange}>
                                <Picker.Item label="Select Type" value="" />
                                {getEnumOptions(enums.WasteTreatmentType).map(opt => (
                                    <Picker.Item key={opt.value} label={opt.label} value={opt.value as string} />
                                ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.solidWaste?.treatmentType && <AppText style={tw`text-red-500`}>{errors.solidWaste?.treatmentType.message}</AppText>}
            </View>

            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>How often is waste collected?</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="solidWaste.collectionFrequency"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={onChange}>
                                <Picker.Item label="Select Frequency" value="" />
                                {getEnumOptions(enums.WasteCollectionFrequency).map(opt => (
                                    <Picker.Item key={opt.value} label={opt.label} value={opt.value as string} />
                                ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.solidWaste?.collectionFrequency && <AppText style={tw`text-red-500`}>{errors.solidWaste?.collectionFrequency.message}</AppText>}
            </View>

            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>How much do you pay for waste collection services per month?</AppText>
                <Controller
                    control={control}
                    name="solidWaste.collectionCost"
                    render={({ field: { onChange, onBlur, value } }) => (
                        <TextInput
                            style={tw`bg-white`}
                            mode="outlined"
                            placeholder="Collection Cost"
                            outlineColor="#E5E7EB"
                            inputMode="numeric"
                            value={value}
                            onChangeText={onChange}
                            onBlur={onBlur}
                        />
                    )}
                />
                {errors.solidWaste?.collectionCost && <AppText style={tw`text-red-500`}>{errors.solidWaste?.collectionCost.message}</AppText>}
            </View>
        </>
    )
}

export default SolidWasteSection;