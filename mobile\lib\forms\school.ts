import { CleanWaterStorageCapacity, EducationLevel, ExcretaManagement, FacilitySlabConstructionMaterial, Gender, HandWashingFacilityType, HandWashingMaterial, MainWaterSource, PwsNonFunctionalityDuration, PwsNonFunctionalityReason, SchoolManagement, SchoolType, SchoolTypeDayBoarding, ToiletFacilityCompleteness, ToiletFacilityType, UnimprovedWaterReason, WasteCollectionFrequency, WasteManagementAfterSeparation, WasteTreatmentType, WasteWaterManagement, WaterAvailability, WaterAvailabilityFrequency, WaterFetchingTime, WaterSourceDistance } from '@/types/enums';
import * as yup from 'yup';


export const schoolSchema = yup.object().shape({
  generalInfo: yup.object().shape({
    schoolName: yup.string().required('School name is required'),
    schoolType: yup
      .string()
      .oneOf(Object.keys(SchoolType))
      .required('School type is required'),
    managementType: yup
      .string()
      .oneOf(Object.keys(SchoolManagement))
      .required('School management is required'),
    dayBoarding: yup
      .string()
      .oneOf(Object.keys(SchoolTypeDayBoarding))
      .required('Day/boarding type is required'),
    totalStudents: yup.number().required('Total number of students is required'),
    femaleStudents: yup.number().required('Number of female students is required'),
    maleStudents: yup.number().required('Number of male students is required'),
    studentsWithDisabilities: yup.number().required('Number of students with disabilities is required'),
  }),
  waterSupply: yup.object().shape({
    connectedToPipeline: yup.boolean().required('This field is required'),
    waterAvailability: yup
      .boolean().required('Water availability is required'),
    availableDays: yup
      .string().oneOf(Object.keys(WaterAvailabilityFrequency))
      .when('waterAvailability', {
        is: (wa: boolean) => wa === false,
        then: schema => schema.required('Available days is required'),
        otherwise: schema => schema.notRequired(),
      }),
    storageCapacity: yup
      .string()
      .oneOf(Object.keys(CleanWaterStorageCapacity))
      .required('Storage capacity is required'),
    mainWaterSource: yup
      .string()
      .oneOf(Object.keys(MainWaterSource))
      .when('connectedToPipeline', {
        is: (ctp: boolean) => ctp === false,
        then: schema => schema.required('Water source is required'),
        otherwise: schema => schema.notRequired(),
      }),
    distanceToSource: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(WaterSourceDistance), 'Distance to water source must be a valid option')
      .required('Distance to water source is required'),
  }),

  sanitation: yup.object().shape({
    toiletType: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(ToiletFacilityType), 'Invalid toilet type')
      .nullable()
      .required('Toilet type is required'),
    slabConstructionMaterial: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(FacilitySlabConstructionMaterial), 'Invalid slab construction material')
      .nullable()
      .when('toiletType', {
        is: (tt: string) => [
          'PIT_WITH_SLAB',
          'PIT_WITHOUT_SLAB',
          'VENTILATED_IMPROVED_PIT',
          'COMPOSTING_TOILET',
          'URINE_DIVERSION_DRY_TOILET',
        ].includes(tt),
        then: schema => schema.required('Slab construction material is required'),
        otherwise: schema => schema.notRequired(),
      }),
    totalToilets: yup.number().required('Total number of toilets is required'),
    genderSeparation: yup.boolean().required('This field is required'),
    femaleToilets: yup.number().required('Number of female toilets is required'),
    maleToilets: yup.number().required('Number of male toilets is required'),
    girlsRoom: yup.boolean().required('This field is required'),
    disabilityAccess: yup.boolean().required('This field is required'),
    staffToilets: yup.boolean().required('This field is required'),
    hasToiletFullInLast2Years: yup.boolean().required('This field is required'),
    excretaManagement: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(ExcretaManagement), 'Invalid excreta management')
      .when('hasToiletFullInLast2Years', {
        is: true,
        then: schema => schema.required('Excreta management is required'),
        otherwise: schema => schema.notRequired(),
      }),
  }),

  hygiene: yup.object().shape({
    handwashingFacility: yup.boolean().required('Handwashing facility is required'),
    facilityType: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(HandWashingFacilityType), 'Invalid handwashing facility type')
      .when('handwashingFacility', {
        is: true,
        then: schema => schema.required('Handwashing facility type is required'),
        otherwise: schema => schema.notRequired(),
      }),
    handwashingMaterials: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(HandWashingMaterial), 'Invalid handwashing material')
      .when('handwashingFacility', {
        is: true,
        then: schema => schema.required('Handwashing materials are required'),
        otherwise: schema => schema.notRequired(),
      }),
      handWashingfacilityNearToilet: yup.boolean().required('Handwashing facility near toilet is required'),
      toiletHandWashingFacilityType: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(HandWashingFacilityType), 'Invalid handwashing facility type')
      .when('handWashingfacilityNearToilet', {
        is: true,
        then: schema => schema.required('Handwashing facility type is required'),
        otherwise: schema => schema.notRequired(),
      }),
      toiletHandWashingMaterials: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(HandWashingMaterial), 'Invalid handwashing material')
      .when('handWashingfacilityNearToilet', {
        is: true,
        then: schema => schema.required('Handwashing materials are required'),
        otherwise: schema => schema.notRequired(),
      }),
  }),

  solidWaste: yup.object().shape({
    wasteSeparation: yup.boolean().required('Waste separation is required'),
    wasteManagement: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(WasteManagementAfterSeparation), 'Invalid waste management')
      .nullable()
      .when('wasteSeparation', {
        is: true,
        then: schema => schema.required('Waste management is required'),
        otherwise: schema => schema.notRequired(),
      }),
    treatmentType: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(WasteTreatmentType), 'Invalid treatment type')
      .nullable()
      .when('wasteManagement', {
        is: (wm: string) => wm === 'ON_SITE_TREATMENT',
        then: schema => schema.required('Treatment type is required'),
        otherwise: schema => schema.notRequired(),
      }),
    collectionFrequency: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(WasteCollectionFrequency), 'Invalid collection frequency')
      .nullable()
      .required('Collection frequency is required'),
    collectionCost: yup.number().required('Collection cost is required'),
  }),

  liquidWaste: yup.object().shape({
    liquidWasteManagement: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(WasteWaterManagement), 'Invalid waste water management')
      .nullable()
      .required('Waste water management is required'),
  }),
});