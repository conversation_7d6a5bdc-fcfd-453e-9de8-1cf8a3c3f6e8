import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Controller, useFormContext } from 'react-hook-form';
import { Picker } from '@react-native-picker/picker';
import tw from 'twrnc';
import AppText from '@/components/ui/Text';
import { TextInput, RadioButton } from 'react-native-paper';
import { RecordingMethod } from '@/types/enums';

interface RecordingMonitoringSectionProps {
  control: any;
  errors: any;
  watch: any;
}

const RecordingMonitoringSection = ({ control, errors, watch }: RecordingMonitoringSectionProps) => {
  const { setValue } = useFormContext();
  const weighbridge = watch('recordingMonitoring.weighbridge');
  const recordingMethod = watch('recordingMonitoring.recordingMethod');

  // Helper function to handle weighbridge change
  const handleWeighbridgeChange = (value: boolean) => {
    setValue('recordingMonitoring.weighbridge', value);

    // If weighbridge is true, clear the recording method and other recording method
    if (value === true) {
      setValue('recordingMonitoring.recordingMethod', null);
      setValue('recordingMonitoring.otherRecordingMethod', '');
    }
  };

  // Helper function to handle recording method change
  const handleRecordingMethodChange = (value: RecordingMethod | null) => {
    setValue('recordingMonitoring.recordingMethod', value);

    // If not "OTHER", clear the other recording method field
    if (value !== RecordingMethod.OTHER) {
      setValue('recordingMonitoring.otherRecordingMethod', '');
    }
  };

  return (
    <View style={tw`gap-4`}>
      {/* Weighbridge */}
      <View>
        <AppText style={tw`mb-2`}>
          Does the company have a functional weighbridge in use, recording waste quantities?
        </AppText>
        <Controller
          control={control}
          name="recordingMonitoring.weighbridge"
          render={({ field: { value } }) => (
            <View style={tw`gap-2`}>
              <TouchableOpacity
                style={tw`flex-row items-center gap-2`}
                onPress={() => handleWeighbridgeChange(true)}
              >
                <RadioButton
                  value="true"
                  status={value === true ? 'checked' : 'unchecked'}
                  onPress={() => handleWeighbridgeChange(true)}
                />
                <AppText>Yes</AppText>
              </TouchableOpacity>
              <TouchableOpacity
                style={tw`flex-row items-center gap-2`}
                onPress={() => handleWeighbridgeChange(false)}
              >
                <RadioButton
                  value="false"
                  status={value === false ? 'checked' : 'unchecked'}
                  onPress={() => handleWeighbridgeChange(false)}
                />
                <AppText>No</AppText>
              </TouchableOpacity>
            </View>
          )}
        />
        {errors.recordingMonitoring?.weighbridge && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.recordingMonitoring.weighbridge.message}
          </AppText>
        )}
      </View>

      {/* Recording Method */}
      {weighbridge === false && (
        <View>
          <AppText style={tw`mb-2`}>
            How do you record amount of waste? (for those who responded No to the weighbridge question)
          </AppText>
          <View style={tw`border border-gray-300 rounded-lg`}>
            <Controller
              control={control}
              name="recordingMonitoring.recordingMethod"
              render={({ field: { value } }) => (
                <Picker
                  selectedValue={value}
                  onValueChange={handleRecordingMethodChange}
                >
                  <Picker.Item label="Select recording method" value="" />
                  <Picker.Item
                    label="Paper logbook"
                    value={RecordingMethod.PAPER_LOGBOOK}
                  />
                  <Picker.Item
                    label="Others, specify"
                    value={RecordingMethod.OTHER}
                  />
                </Picker>
              )}
            />
          </View>
          {errors.recordingMonitoring?.recordingMethod && (
            <AppText style={tw`text-red-500 text-sm mt-1`}>
              {errors.recordingMonitoring.recordingMethod.message}
            </AppText>
          )}
        </View>
      )}

      {/* Other Recording Method */}
      {weighbridge === false && recordingMethod === RecordingMethod.OTHER && (
        <View>
          <AppText style={tw`mb-2`}>Please specify other recording method</AppText>
          <Controller
            control={control}
            name="recordingMonitoring.otherRecordingMethod"
            render={({ field: { onChange, value } }) => (
              <TextInput
                mode="outlined"
                value={value || ''}
                onChangeText={onChange}
                placeholder="Specify other recording method"
                error={!!errors.recordingMonitoring?.otherRecordingMethod}
                returnKeyType="next"
                outlineColor="gray"
              />
            )}
          />
          {errors.recordingMonitoring?.otherRecordingMethod && (
            <AppText style={tw`text-red-500 text-sm mt-1`}>
              {errors.recordingMonitoring.otherRecordingMethod.message}
            </AppText>
          )}
        </View>
      )}
    </View>
  );
};

export default RecordingMonitoringSection;
