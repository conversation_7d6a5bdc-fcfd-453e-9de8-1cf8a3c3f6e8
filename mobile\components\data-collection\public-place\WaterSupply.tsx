import AppText from '@/components/ui/Text';
import * as enums from '@/types/enums';
import { getEnumOptions } from '@/utils/enum';
import { Picker } from '@react-native-picker/picker';
import React from 'react';
import { Controller, useWatch } from 'react-hook-form';
import { View } from 'react-native';
import tw from 'twrnc';

const WaterSupplySection = ({ control, errors }: any) => {
    const connectedToPipeline = useWatch({ control, name: 'waterSupply.connectedToPipeline' });
    const waterAvailability = useWatch({ control, name: 'waterSupply.waterAvailability' });

    return (
        <>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Is the public place connected to a pipeline?</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="waterSupply.connectedToPipeline"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value === true ? 'yes' : value === false ? 'no' : ''} onValueChange={v => onChange(v === 'yes')}>
                                <Picker.Item label="Select Option" value="" />
                                <Picker.Item label="Yes" value="yes" />
                                <Picker.Item label="No" value="no" />
                            </Picker>
                        )}
                    />
                </View>
                {errors.waterSupply?.connectedToPipeline && <AppText style={tw`text-red-500`}>{errors.waterSupply?.connectedToPipeline.message}</AppText>}
            </View>

            {connectedToPipeline && <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Is water available for use whenever needed at the public place?</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="waterSupply.waterAvailability"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={onChange}>
                                <Picker.Item label="Select Water Availability" value="" />
                                <Picker.Item label="Users can find water whenever needed" value="ALWAYS_AVAILABLE" />
                                <Picker.Item label="Sometimes users cannot find water to use" value="SOMETIMES_UNAVAILABLE" />
                            </Picker>
                        )}
                    />
                </View>
                {errors.waterSupply?.waterAvailability && <AppText style={tw`text-red-500`}>{errors.waterSupply?.waterAvailability.message}</AppText>}
            </View>}

            {waterAvailability === 'SOMETIMES_UNAVAILABLE' && (
                <View style={tw`mb-4`}>
                    <AppText style={tw`mb-2`}>How many days is water available within the tap?</AppText>
                    <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                        <Controller
                            control={control}
                            name="waterSupply.availableDays"
                            render={({ field: { onChange, value } }) => (
                                <Picker selectedValue={value} onValueChange={onChange}>
                                    <Picker.Item label="Select Frequency" value="" />
                                    {getEnumOptions(enums.WaterAvailabilityFrequency).map(opt => (
                                        <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                    ))}
                                </Picker>
                            )}
                        />
                    </View>
                    {errors.waterSupply?.availableDays && <AppText style={tw`text-red-500`}>{errors.waterSupply?.availableDays.message}</AppText>}
                </View>
            )}

            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Clean water storage capacity at the public place (Tanks, etc)</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="waterSupply.storageCapacity"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={onChange}>
                                <Picker.Item label="Select Storage Capacity" value="" />
                                {getEnumOptions(enums.CleanWaterStorageCapacity).map(opt => (
                                    <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.waterSupply?.storageCapacity && <AppText style={tw`text-red-500`}>{errors.waterSupply?.storageCapacity.message}</AppText>}
            </View>

            {connectedToPipeline === false && (
                <View style={tw`mb-4`}>
                    <AppText style={tw`mb-2`}>What is the public place's main source of water supply?</AppText>
                    <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                        <Controller
                            control={control}
                            name="waterSupply.mainWaterSource"
                            render={({ field: { onChange, value } }) => (
                                <Picker selectedValue={value} onValueChange={onChange}>
                                    <Picker.Item label="Select Water Source" value="" />
                                    {getEnumOptions(enums.MainWaterSource).map(opt => (
                                        <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                    ))}
                                </Picker>
                            )}
                        />
                    </View>
                    {errors.waterSupply?.mainWaterSource && <AppText style={tw`text-red-500`}>{errors.waterSupply?.mainWaterSource.message}</AppText>}
                </View>
            )}

            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Distance between public place and nearby pipeline/system</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="waterSupply.distanceToSource"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={onChange}>
                                <Picker.Item label="Select Distance" value="" />
                                {getEnumOptions(enums.WaterSourceDistance).map(opt => (
                                    <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.waterSupply?.distanceToSource && <AppText style={tw`text-red-500`}>{errors.waterSupply?.distanceToSource.message}</AppText>}
            </View>
        </>
    );
};

export default WaterSupplySection;