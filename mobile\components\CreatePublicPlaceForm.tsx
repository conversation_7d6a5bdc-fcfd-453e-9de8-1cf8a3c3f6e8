
import AppText from "@/components/ui/Text";
import { PRIMARY_COLOR } from "@/constants/colors";
import { getVillage } from "@/services/administrative";
import { Village } from "@/types/administrative";
import { PublicPlaceType, SettlementType } from "@/types/enums";
import { yupResolver } from "@hookform/resolvers/yup";
import { Picker } from "@react-native-picker/picker";
import * as Location from "expo-location";
import { useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { Al<PERSON>, ScrollView, View } from "react-native";
import { ActivityIndicator, Button as PaperButton, TextInput } from "react-native-paper";
import tw from 'twrnc';
import * as yup from "yup";

interface FormValues {
  name: string;
  type: keyof typeof PublicPlaceType;
  latitude?: number;
  longitude?: number;
  settlementType: SettlementType;
}

const schema = yup.object().shape({
  name: yup.string().required("Name is required"),
  type: yup.mixed<keyof typeof PublicPlaceType>().oneOf(Object.keys(PublicPlaceType) as (keyof typeof PublicPlaceType)[]).required("Type is required"),
  latitude: yup.number().required("Latitude is required"),
  longitude: yup.number().required("Longitude is required"),
  settlementType: yup.mixed<SettlementType>().oneOf([SettlementType.RURAL, SettlementType.URBAN]).required("Settlement type is required"),
});

export default function CreatePublicPlaceForm({ onSubmit }: { onSubmit: (data: any) => void }) {
  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<FormValues>({
    defaultValues: {
      name: "",
      type: "MARKET" as keyof typeof PublicPlaceType,
      settlementType: SettlementType.RURAL,
    },
    resolver: yupResolver(schema) as any,
  });

  const { villageId } = useLocalSearchParams();
  const [village, setVillage] = useState<Village | null>(null);

  useEffect(() => {
    const fetchVillage = async () => {
      try {
        const res = await getVillage(villageId.toString());
        setVillage(res.data);
      } catch (error) {
        console.log(error);
        Alert.alert("Error", "Failed to load village");
      }
    };
    if (villageId) fetchVillage();
  }, [villageId]);

  useEffect(() => {
    const getCurrentLocation = async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== "granted") {
          Alert.alert("Permission Denied", "Location permission is required to autofill coordinates.");
          return;
        }

        const location = await Location.getCurrentPositionAsync({});
        setValue("latitude", location.coords.latitude);
        setValue("longitude", location.coords.longitude);
      } catch (error) {
        console.warn("Failed to get location:", error);
      }
    };
    getCurrentLocation();
  }, [setValue]);

  const handleFormSubmit = (data: FormValues) => {
    onSubmit({
      name: data.name,
      type: data.type,
      villageId: Number(`${villageId}`),
      latitude: data.latitude,
      longitude: data.longitude,
      settlementType: data.settlementType,
    });
  };

  return (
    <>
      {village ? (
        <ScrollView style={tw`flex-1 bg-gray-50`} contentContainerStyle={tw`px-4 pt-2 pb-10`}>
          <View style={tw`mt-2 mb-4`}>
            <AppText style={tw`text-gray-600 text-sm`}>
              {village.cell.sector.district.province.name} &gt; {village.cell.sector.district.name} &gt; {village.cell.sector.name} &gt; {village.cell.name} &gt; {village.name}
            </AppText>
          </View>

          {/* Public Place Name */}
          <View style={tw`mb-4`}>
            <AppText style={tw`mb-1 text-base font-medium`}>Public Place Name</AppText>
            <Controller
              control={control}
              name="name"
              render={({ field: { onChange, value } }) => (
                <TextInput
                  placeholder="Public Place Name"
                  value={value}
                  onChangeText={onChange}
                  error={!!errors.name}
                  mode="outlined"
                  style={tw`bg-white`}
                  outlineStyle={tw`rounded-xl`}
                  outlineColor='#E5E7EB'
                />
              )}
            />
            {errors.name && (
              <AppText style={tw`text-red-500 text-sm mt-1`}>
                {errors.name.message}
              </AppText>
            )}
          </View>

          {/* Public Place Type */}
          <View style={tw`mb-4`}>
            <AppText style={tw`mb-1 text-base font-medium`}>Type</AppText>
            <Controller
              control={control}
              name="type"
              render={({ field: { onChange, value } }) => (
                <View style={tw`border border-gray-300 rounded-xl bg-white`}>
                  <Picker
                    selectedValue={value}
                    onValueChange={onChange}
                    style={tw`h-14`}
                  >
                    {(Object.keys(PublicPlaceType) as Array<keyof typeof PublicPlaceType>).map((key) => (
                      <Picker.Item key={key} label={PublicPlaceType[key]} value={key} />
                    ))}
                  </Picker>
                </View>
              )}
            />
            {errors.type && (
              <AppText style={tw`text-red-500 text-sm mt-1`}>
                {errors.type.message}
              </AppText>
            )}
          </View>

          {/* Settlement Type */}
          <View style={tw`mb-4`}>
            <AppText style={tw`mb-1 text-base font-medium`}>Settlement Type</AppText>
            <Controller
              control={control}
              name="settlementType"
              render={({ field: { onChange, value } }) => (
                <View style={tw`border border-gray-300 rounded-xl bg-white`}>
                  <Picker
                    selectedValue={value}
                    onValueChange={onChange}
                    style={tw`h-14`}
                  >
                    <Picker.Item label="Rural" value={SettlementType.RURAL} />
                    <Picker.Item label="Urban" value={SettlementType.URBAN} />
                  </Picker>
                </View>
              )}
            />
            {errors.settlementType && (
              <AppText style={tw`text-red-500 text-sm mt-1`}>
                {errors.settlementType.message}
              </AppText>
            )}
          </View>

          {/* Coordinates Section */}
          <View style={tw`mb-6 p-3 bg-gray-100 rounded-xl`}>
            <AppText style={tw`font-bold mb-2`}>GPS Coordinates:</AppText>
            {/* Latitude Input */}
            <View style={tw`mb-2`}>
              <Controller
                control={control}
                name="latitude"
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    label="Latitude"
                    keyboardType="numeric"
                    value={value?.toString() || ""}
                    onChangeText={(text) => onChange(text ? parseFloat(text) : undefined)}
                    mode="outlined"
                    style={tw`bg-white`}
                    outlineStyle={tw`rounded-xl`}
                    outlineColor='#E5E7EB'
                  />
                )}
              />
              {errors.latitude && (
                <AppText style={tw`text-red-500 text-sm mt-1`}>
                  {errors.latitude.message}
                </AppText>
              )}
            </View>
            {/* Longitude Input */}
            <View style={tw`mb-2`}>
              <Controller
                control={control}
                name="longitude"
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    label="Longitude"
                    keyboardType="numeric"
                    value={value?.toString() || ""}
                    onChangeText={(text) => onChange(text ? parseFloat(text) : undefined)}
                    mode="outlined"
                    style={tw`bg-white`}
                    outlineStyle={tw`rounded-xl`}
                    outlineColor='#E5E7EB'
                  />
                )}
              />
              {errors.longitude && (
                <AppText style={tw`text-red-500 text-sm mt-1`}>
                  {errors.longitude.message}
                </AppText>
              )}
            </View>
            <AppText style={tw`text-gray-400 text-xs mt-1`}>
              Coordinates are auto-filled if location permission is granted, but can be manually edited.
            </AppText>
          </View>

          {/* Submit Button */}
          <PaperButton
            mode="contained"
            onPress={handleSubmit(handleFormSubmit)}
            loading={isSubmitting}
            disabled={isSubmitting}
            style={tw`mt-2 mb-10 rounded-xl`}
            contentStyle={tw`h-12`}
            labelStyle={tw`text-base font-semibold`}
          >
            {isSubmitting ? "Submitting..." : "Submit Public Place"}
          </PaperButton>
        </ScrollView>
      ) : (
        <View style={tw`flex-1 justify-center items-center`}>
          <ActivityIndicator size="large" color={PRIMARY_COLOR} />
          <AppText style={tw`mt-4 text-gray-500`}>Loading village info...</AppText>
        </View>
      )}
    </>
  );
}