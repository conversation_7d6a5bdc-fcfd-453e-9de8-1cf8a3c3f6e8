import AppButton from '@/components/ui/Button';
import AppText from '@/components/ui/Text';
import { AxiosError } from 'axios';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    View
} from 'react-native';
import { TextInput } from 'react-native-paper';
import tw from 'twrnc';
import { useAuth } from '../../../hooks/useAuth';
import { ApiError } from '../../../types/auth';
import { PRIMARY_COLOR } from '@/constants/colors';

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const { login } = useAuth();

  const handleLogin = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert('Error', 'Please enter both email and password');
      return;
    }

    setIsLoading(true);
    try {
      const response = await login(email.trim(), password);

      if (response.requires2FA && response.tempToken) {
        router.push({
          pathname: '/auth/verify2FA',
          params: { tempToken: response.tempToken }
        });
      } else {
        router.replace('/main');
      }

    } catch (error: unknown) {
      const apiError = error as AxiosError<ApiError>;

      if (apiError.response?.status === 401) {
        Alert.alert('Login Failed', 'Invalid credentials');
        return;
      }

      let errorMessage = 'An error occurred during login';
      if (apiError.message) {
        if (Array.isArray(apiError.message)) {
          errorMessage = apiError.message.join(', ');
        } else {
          errorMessage = apiError.message;
        }
      }

      Alert.alert('Login Error', errorMessage);
      console.error('Login error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={tw`flex-1 bg-white`}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={tw`h-full`}
        keyboardShouldPersistTaps="handled"
      >
        <View style={tw`px-5 flex justify-between h-full gap-8`}>
          <View style={tw`flex flex-col gap-12`}>
            <View style={tw`flex gap-2`}>
              <AppText weight='bold' style={tw`text-2xl text-gray-600`}>Sign In</AppText>
              <AppText style={tw``}>
                Fill in your credentials to login in to WASH System
              </AppText>
            </View>
            <View style={tw`flex flex-col gap-4`}>
              <View>
                <AppText weight='medium' style={tw`text-gray-600 mb-2`}>Email </AppText>
                <TextInput
                  placeholder="Email"
                  value={email}
                  onChangeText={setEmail}
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!isLoading}
                  keyboardType='email-address'
                  mode='outlined'
                  left={<TextInput.Icon color={'#9CA3AF'} icon="email" />}
                  style={tw`bg-white`}
                  outlineStyle={tw`rounded-xl`}
                  outlineColor='#E5E7EB'

                />

              </View>
              <View>
                <AppText weight='medium' style={tw`text-gray-600 mb-2`}>Password </AppText>
                <TextInput
                  placeholder="*********"
                  value={password}
                  onChangeText={setPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!isLoading}
                  secureTextEntry={!showPassword}
                  mode='outlined'
                  left={<TextInput.Icon color={'#9CA3AF'} icon="lock" />}
                  right={
                    !showPassword ? (
                      <TextInput.Icon color={'#9CA3AF'} icon="eye-off" onPress={() => setShowPassword(!showPassword)} />
                    ) : (
                      <TextInput.Icon color={'#9CA3AF'} icon="eye" onPress={() => setShowPassword(!showPassword)} />
                    )
                  }
                  style={tw`bg-white`}
                  outlineStyle={tw`rounded-xl`}
                  outlineColor='#E5E7EB'
                />
              </View>
            </View>
          </View>
          <View style={tw`flex flex-col gap-4`}>
            <AppText weight='medium' style={tw`text-center`}>
              By submitting this application you confirm that you are authorized to share this information and agree with our
              <AppText weight='medium' style={tw`text-[${PRIMARY_COLOR}]`}> Terms and Conditions</AppText>
            </AppText>
            <AppButton title="Login" onPress={handleLogin} loading={isLoading} disabled={isLoading} />
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}