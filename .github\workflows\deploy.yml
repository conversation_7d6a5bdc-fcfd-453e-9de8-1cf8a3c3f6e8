name: Deploy to Server

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Setup SSH Agent
        uses: webfactory/ssh-agent@v0.9.1
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Add Server to Known Hosts
        run: ssh-keyscan -H ************** >> ~/.ssh/known_hosts

      - name: Deploy to Remote Server
        run: |
          ssh kubaka@************** << 'EOF'
            cd /home/<USER>/WASH-MIS/WASH-MIS
            git pull origin main --rebase
            cd server
            docker-compose down
            docker-compose up -d --build
          EOF
