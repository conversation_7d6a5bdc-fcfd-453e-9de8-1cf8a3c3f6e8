import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum, IsBoolean, IsInt, IsArray, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { Gender, ServiceProviderType, ClientType, WasteMaterial, WasteDestination, RecordingMethod } from '@prisma/client';

export class CreateWasteCollectionCompanySubmissionDto {
  @ApiProperty({ example: 'Clean City Waste Collection' })
  @IsString()
  companyName: string;

  @ApiProperty({ example: '<PERSON> Doe' })
  @IsString()
  ownerName: string;

  @ApiProperty({ enum: Gender })
  @IsEnum(Gender)
  ownerGender: Gender;

  @ApiProperty({ example: '+250788123456' })
  @IsString()
  contactPhone: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsString()
  contactEmail: string;

  @ApiProperty({ enum: ServiceProviderType })
  @IsEnum(ServiceProviderType)
  companyType: ServiceProviderType;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  otherCompanyType?: string;

  @ApiProperty({ example: 25 })
  @IsInt()
  totalPersonnel: number;

  @ApiProperty({ example: 10 })
  @IsInt()
  femalePersonnel: number;

  @ApiProperty({ example: 15 })
  @IsInt()
  malePersonnel: number;

  @ApiProperty({ enum: ClientType, isArray: true })
  @IsArray()
  @IsEnum(ClientType, { each: true })
  clientTypes: ClientType[];

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  otherClientTypes?: string[];

  @ApiProperty({ example: true })
  @IsBoolean()
  wasteSeparation: boolean;

  @ApiProperty({ enum: WasteMaterial, isArray: true })
  @IsArray()
  @IsEnum(WasteMaterial, { each: true })
  separatedMaterials: WasteMaterial[];

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  otherSeparatedMaterials?: string[];

  @ApiProperty({ enum: WasteDestination })
  @IsEnum(WasteDestination)
  wasteDestination: WasteDestination;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  destinationDetails?: string;

  @ApiProperty({ example: true })
  @IsBoolean()
  weighbridge: boolean;

  @ApiProperty({ enum: RecordingMethod, required: false })
  @IsOptional()
  @IsEnum(RecordingMethod)
  recordingMethod?: RecordingMethod;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  otherRecordingMethod?: string;
}

export class WasteCollectionCompanySubmissionResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  submissionId: string;

  @ApiProperty()
  submittedAt: Date;

  @ApiProperty()
  companyName: string;

  @ApiProperty()
  ownerName: string;

  @ApiProperty({ enum: Gender })
  ownerGender: Gender;

  @ApiProperty()
  contactPhone: string;

  @ApiProperty()
  contactEmail: string;

  @ApiProperty({ enum: ServiceProviderType })
  companyType: ServiceProviderType;

  @ApiProperty({ required: false })
  otherCompanyType?: string;

  @ApiProperty()
  totalPersonnel: number;

  @ApiProperty()
  femalePersonnel: number;

  @ApiProperty()
  malePersonnel: number;

  @ApiProperty({ enum: ClientType, isArray: true })
  clientTypes: ClientType[];

  @ApiProperty({ type: [String], required: false })
  otherClientTypes?: string[];

  @ApiProperty()
  wasteSeparation: boolean;

  @ApiProperty({ enum: WasteMaterial, isArray: true })
  separatedMaterials: WasteMaterial[];

  @ApiProperty({ type: [String], required: false })
  otherSeparatedMaterials?: string[];

  @ApiProperty({ enum: WasteDestination })
  wasteDestination: WasteDestination;

  @ApiProperty({ required: false })
  destinationDetails?: string;

  @ApiProperty()
  weighbridge: boolean;

  @ApiProperty({ enum: RecordingMethod, required: false })
  recordingMethod?: RecordingMethod;

  @ApiProperty({ required: false })
  otherRecordingMethod?: string;
}