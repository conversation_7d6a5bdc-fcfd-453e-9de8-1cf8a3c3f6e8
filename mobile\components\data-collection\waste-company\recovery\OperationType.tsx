import React from 'react';
import { View } from 'react-native';
import { Controller } from 'react-hook-form';
import { Picker } from '@react-native-picker/picker';
import tw from 'twrnc';
import AppText from '@/components/ui/Text';
import { OperationType } from '@/types/enums';

interface OperationTypeSectionProps {
  control: any;
  errors: any;
}

const OperationTypeSection = ({ control, errors }: OperationTypeSectionProps) => {
  return (
    <View style={tw`gap-4`}>
      <View>
        <AppText style={tw`mb-2`}>
          Which category in the recovery value chain below best describes your operation?
        </AppText>
        <View style={tw`border border-gray-300 rounded-lg`}>
          <Controller
            control={control}
            name="operationType.operationType"
            render={({ field: { onChange, value } }) => (
              <Picker
                selectedValue={value}
                onValueChange={onChange}
              >
                <Picker.Item label="Select operation type" value="" />
                <Picker.Item 
                  label="End-of-chain recycler/recoverer facility that handles the final stages of the recycling process" 
                  value={OperationType.END_OF_CHAIN_RECYCLER} 
                />
                <Picker.Item 
                  label="Apex trader (Buying and Selling recycled commodities)" 
                  value={OperationType.APEX_TRADER} 
                />
                <Picker.Item 
                  label="Intermediate trader person or company that buys recyclable materials from collectors or smaller processors and then sells these materials to larger recycling facilities or manufacturers" 
                  value={OperationType.INTERMEDIATE_TRADER} 
                />
              </Picker>
            )}
          />
        </View>
        {errors.operationType?.operationType && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.operationType.operationType.message}
          </AppText>
        )}
      </View>
    </View>
  );
};

export default OperationTypeSection;
