"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WasteCollectionCompanySubmissionResponseDto = exports.CreateWasteCollectionCompanySubmissionDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const client_1 = require("@prisma/client");
class CreateWasteCollectionCompanySubmissionDto {
    companyName;
    ownerName;
    ownerGender;
    contactPhone;
    contactEmail;
    companyType;
    otherCompanyType;
    totalPersonnel;
    femalePersonnel;
    malePersonnel;
    clientTypes;
    otherClientTypes;
    wasteSeparation;
    separatedMaterials;
    otherSeparatedMaterials;
    wasteDestination;
    destinationDetails;
    weighbridge;
    recordingMethod;
    otherRecordingMethod;
}
exports.CreateWasteCollectionCompanySubmissionDto = CreateWasteCollectionCompanySubmissionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Clean City Waste Collection' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWasteCollectionCompanySubmissionDto.prototype, "companyName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'John Doe' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWasteCollectionCompanySubmissionDto.prototype, "ownerName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.Gender }),
    (0, class_validator_1.IsEnum)(client_1.Gender),
    __metadata("design:type", String)
], CreateWasteCollectionCompanySubmissionDto.prototype, "ownerGender", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '+250788123456' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWasteCollectionCompanySubmissionDto.prototype, "contactPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '<EMAIL>' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWasteCollectionCompanySubmissionDto.prototype, "contactEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ServiceProviderType }),
    (0, class_validator_1.IsEnum)(client_1.ServiceProviderType),
    __metadata("design:type", String)
], CreateWasteCollectionCompanySubmissionDto.prototype, "companyType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWasteCollectionCompanySubmissionDto.prototype, "otherCompanyType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 25 }),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateWasteCollectionCompanySubmissionDto.prototype, "totalPersonnel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 10 }),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateWasteCollectionCompanySubmissionDto.prototype, "femalePersonnel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 15 }),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateWasteCollectionCompanySubmissionDto.prototype, "malePersonnel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ClientType, isArray: true }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(client_1.ClientType, { each: true }),
    __metadata("design:type", Array)
], CreateWasteCollectionCompanySubmissionDto.prototype, "clientTypes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateWasteCollectionCompanySubmissionDto.prototype, "otherClientTypes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateWasteCollectionCompanySubmissionDto.prototype, "wasteSeparation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteMaterial, isArray: true }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(client_1.WasteMaterial, { each: true }),
    __metadata("design:type", Array)
], CreateWasteCollectionCompanySubmissionDto.prototype, "separatedMaterials", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateWasteCollectionCompanySubmissionDto.prototype, "otherSeparatedMaterials", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteDestination }),
    (0, class_validator_1.IsEnum)(client_1.WasteDestination),
    __metadata("design:type", String)
], CreateWasteCollectionCompanySubmissionDto.prototype, "wasteDestination", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWasteCollectionCompanySubmissionDto.prototype, "destinationDetails", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateWasteCollectionCompanySubmissionDto.prototype, "weighbridge", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.RecordingMethod, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.RecordingMethod),
    __metadata("design:type", String)
], CreateWasteCollectionCompanySubmissionDto.prototype, "recordingMethod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWasteCollectionCompanySubmissionDto.prototype, "otherRecordingMethod", void 0);
class WasteCollectionCompanySubmissionResponseDto {
    id;
    submissionId;
    submittedAt;
    companyName;
    ownerName;
    ownerGender;
    contactPhone;
    contactEmail;
    companyType;
    otherCompanyType;
    totalPersonnel;
    femalePersonnel;
    malePersonnel;
    clientTypes;
    otherClientTypes;
    wasteSeparation;
    separatedMaterials;
    otherSeparatedMaterials;
    wasteDestination;
    destinationDetails;
    weighbridge;
    recordingMethod;
    otherRecordingMethod;
}
exports.WasteCollectionCompanySubmissionResponseDto = WasteCollectionCompanySubmissionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WasteCollectionCompanySubmissionResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WasteCollectionCompanySubmissionResponseDto.prototype, "submissionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Date)
], WasteCollectionCompanySubmissionResponseDto.prototype, "submittedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WasteCollectionCompanySubmissionResponseDto.prototype, "companyName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WasteCollectionCompanySubmissionResponseDto.prototype, "ownerName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.Gender }),
    __metadata("design:type", String)
], WasteCollectionCompanySubmissionResponseDto.prototype, "ownerGender", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WasteCollectionCompanySubmissionResponseDto.prototype, "contactPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WasteCollectionCompanySubmissionResponseDto.prototype, "contactEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ServiceProviderType }),
    __metadata("design:type", String)
], WasteCollectionCompanySubmissionResponseDto.prototype, "companyType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], WasteCollectionCompanySubmissionResponseDto.prototype, "otherCompanyType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], WasteCollectionCompanySubmissionResponseDto.prototype, "totalPersonnel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], WasteCollectionCompanySubmissionResponseDto.prototype, "femalePersonnel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], WasteCollectionCompanySubmissionResponseDto.prototype, "malePersonnel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ClientType, isArray: true }),
    __metadata("design:type", Array)
], WasteCollectionCompanySubmissionResponseDto.prototype, "clientTypes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String], required: false }),
    __metadata("design:type", Array)
], WasteCollectionCompanySubmissionResponseDto.prototype, "otherClientTypes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Boolean)
], WasteCollectionCompanySubmissionResponseDto.prototype, "wasteSeparation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteMaterial, isArray: true }),
    __metadata("design:type", Array)
], WasteCollectionCompanySubmissionResponseDto.prototype, "separatedMaterials", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String], required: false }),
    __metadata("design:type", Array)
], WasteCollectionCompanySubmissionResponseDto.prototype, "otherSeparatedMaterials", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteDestination }),
    __metadata("design:type", String)
], WasteCollectionCompanySubmissionResponseDto.prototype, "wasteDestination", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], WasteCollectionCompanySubmissionResponseDto.prototype, "destinationDetails", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Boolean)
], WasteCollectionCompanySubmissionResponseDto.prototype, "weighbridge", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.RecordingMethod, required: false }),
    __metadata("design:type", String)
], WasteCollectionCompanySubmissionResponseDto.prototype, "recordingMethod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], WasteCollectionCompanySubmissionResponseDto.prototype, "otherRecordingMethod", void 0);
//# sourceMappingURL=waste-collection-company.dto.js.map