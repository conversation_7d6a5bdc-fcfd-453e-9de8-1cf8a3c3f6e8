import AppText from "@/components/ui/Text";
import { PRIMARY_COLOR } from "@/constants/colors";
import { getVillage } from "@/services/administrative";
import { SettlementType } from "@/types/enums";
import { Village } from "@/types/location";
import { yupResolver } from "@hookform/resolvers/yup";
import { Picker } from "@react-native-picker/picker";
import * as Location from "expo-location";
import { useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { Al<PERSON>, ScrollView, View } from "react-native";
import { ActivityIndicator, Button as PaperButton, TextInput } from "react-native-paper";
import tw from 'twrnc';
import * as yup from "yup";

interface FormValues {
  headOfHouseholdName: string;
  latitude?: number;
  longitude?: number;
  settlementType: SettlementType;
}

const schema = yup.object().shape({
  headOfHouseholdName: yup.string().required("Household head name is required"),
  latitude: yup.number().required("Latitude is required"),
  longitude: yup.number().required("Longitude is required"),
  settlementType: yup.mixed<SettlementType>().oneOf([SettlementType.RURAL, SettlementType.URBAN]).required("Settlement type is required"),
});

export default function CreateHouseholdForm({ onSubmit }: { onSubmit: (data: FormValues & { villageId: string | number }) => void }) {
  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<FormValues>({
    defaultValues: {
      headOfHouseholdName: "",
      settlementType: SettlementType.RURAL
    },
    resolver: yupResolver(schema) as any,
  });

  const { villageId } = useLocalSearchParams();

  const [village, setVillage] = useState<Village | null>(null);

  useEffect(() => {
    const fetchVillage = async () => {
      try {
        const res = await getVillage(villageId.toString());
        setVillage(res.data);
      } catch (error) {
        console.log(error);
        Alert.alert("Error", "Failed to load village");
      }
    };
    fetchVillage();
  }, [villageId]);

  // Get current location
  useEffect(() => {
    const getCurrentLocation = async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== "granted") {
          Alert.alert("Permission Denied", "Location permission is required to autofill coordinates.");
          return;
        }
        const location = await Location.getCurrentPositionAsync({});
        setValue("latitude", location.coords.latitude);
        setValue("longitude", location.coords.longitude);
      } catch (error) {
        console.warn("Failed to get location:", error);
      }
    };
    getCurrentLocation();
  }, [setValue]);

  const handleFormSubmit = (data: FormValues) => {
    onSubmit({ ...data, villageId: Number(`${villageId}`) });
  };

  return (
    <>
      {village ? (
        <ScrollView style={{ flex: 1, padding: 0 }}>

      <View style={tw`mt-2`}>
        {<AppText weight="medium" style={tw`text-gray-600 text-sm`}>{village.cell.sector.district.province.name} &gt; {village.cell.sector.district.name} &gt; {village.cell.sector.name} &gt; {village.cell.name} &gt; {village.name} </AppText>}
      </View>

      {/* Household Head Name */}
      <View style={tw`mt-4`}>
        <AppText style={{ marginBottom: 4 }}>Household Head Name</AppText>
        <Controller
          control={control}
          name="headOfHouseholdName"
          render={({ field: { onChange, value } }) => (
            <TextInput
              placeholder="Full name"
              value={value}
              onChangeText={onChange}
              error={!!errors.headOfHouseholdName}
              mode="outlined"
              style={tw`bg-white`}
              outlineStyle={tw`rounded-xl`}
              outlineColor='#E5E7EB'
            />
          )}
        />
        {errors.headOfHouseholdName && (
          <AppText style={tw`text-red-500 mt-1 text-xs`}>
            {errors.headOfHouseholdName.message}
          </AppText>
        )}
      </View>

      {/* Settlement Type Dropdown */}
      <View style={tw`my-4`}>
        <AppText style={{ marginBottom: 4 }}>Settlement Type</AppText>
        <View style={tw`border border-gray-300 rounded bg-white`}>
          <Controller
            control={control}
            name="settlementType"
            render={({ field: { onChange, value } }) => (
              <Picker
                selectedValue={value}
                onValueChange={onChange}
              >
                <Picker.Item label="Select Settlement Type" value="" />
                <Picker.Item label="Urban" value={SettlementType.URBAN} />
                <Picker.Item label="Rural" value={SettlementType.RURAL} />
              </Picker>
            )}
          />
        </View>
        {errors.settlementType && (
          <AppText style={tw`text-red-500 mt-1 text-xs`}>
            {errors.settlementType.message}
          </AppText>
        )}
      </View>

      {/* Coordinates Section */}
      <View style={{ marginBottom: 20, padding: 12, backgroundColor: "#f5f5f5", borderRadius: 8 }}>
        <AppText style={{ fontWeight: "bold", marginBottom: 4 }}>
          GPS Coordinates:
        </AppText>
        {/* Latitude Input */}
        <View style={{ marginBottom: 8 }}>
          <Controller
            control={control}
            name="latitude"
            render={({ field: { onChange, value } }) => (
              <TextInput
                label="Latitude"
                placeholder="Latitude"
                keyboardType="numeric"
                value={value?.toString() || ""}
                onChangeText={(text) => onChange(text ? parseFloat(text) : undefined)}
                mode="outlined"
                style={tw`bg-white`}
                outlineStyle={tw`rounded-xl`}
                outlineColor='#E5E7EB'
                error={!!errors.latitude}
              />
            )}
          />
          {errors.latitude && (
            <AppText style={tw`text-red-500 mt-1 text-xs`}>
              {errors.latitude.message}
            </AppText>
          )}
        </View>
        {/* Longitude Input */}
        <View style={{ marginBottom: 8 }}>
          <Controller
            control={control}
            name="longitude"
            render={({ field: { onChange, value } }) => (
              <TextInput
                label="Longitude"
                placeholder="Longitude"
                keyboardType="numeric"
                value={value?.toString() || ""}
                onChangeText={(text) => onChange(text ? parseFloat(text) : undefined)}
                mode="outlined"
                style={tw`bg-white`}
                outlineStyle={tw`rounded-xl`}
                outlineColor='#E5E7EB'
                error={!!errors.longitude}
              />
            )}
          />
          {errors.longitude && (
            <AppText style={tw`text-red-500 mt-1 text-xs`}>
              {errors.longitude.message}
            </AppText>
          )}
        </View>
        <AppText style={{ color: "gray", fontSize: 12, marginTop: 4 }}>
          Coordinates are auto-filled if location permission is granted, but can be manually edited.
        </AppText>
      </View>

      {/* Submit Button */}
      <PaperButton
        mode="contained"
        onPress={handleSubmit(handleFormSubmit)}
        loading={isSubmitting}
        disabled={isSubmitting}
        style={{ marginTop: 10, marginBottom: 40 }}
        contentStyle={{ height: 48 }}
      >
        {isSubmitting ? "Submitting..." : "Submit Household"}
      </PaperButton>
    </ScrollView>
      ): 
       <ActivityIndicator size="large" color={PRIMARY_COLOR} />
      }
    </>
  );
}