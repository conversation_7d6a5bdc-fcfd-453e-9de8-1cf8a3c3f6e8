import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, IsString, IsEnum, IsBoolean, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { SchoolType, SchoolManagement, SchoolTypeDayBoarding, WaterAvailabilityFrequency, CleanWaterStorageCapacity, MainWaterSource, WaterSourceDistance, ToiletFacilityType, FacilitySlabConstructionMaterial, ExcretaManagement, HandWashingFacilityType, HandWashingMaterial, WasteManagementAfterSeparation, WasteTreatmentType, WasteCollectionFrequency, WasteWaterManagement } from '@prisma/client';
import { BaseCreateSubmissionDto, BaseSubmissionResponseDto } from './base-submission.dto';

export class SchoolGeneralInfoDto {
  @ApiProperty()
  @IsString()
  schoolName: string;

  @ApiProperty({ enum: SchoolType })
  @IsEnum(SchoolType)
  schoolType: SchoolType;

  @ApiProperty({ enum: SchoolManagement })
  @IsEnum(SchoolManagement)
  managementType: SchoolManagement;

  @ApiProperty({ enum: SchoolTypeDayBoarding })
  @IsEnum(SchoolTypeDayBoarding)
  dayBoarding: SchoolTypeDayBoarding;

  @ApiProperty()
  @IsInt()
  totalStudents: number;

  @ApiProperty()
  @IsInt()
  femaleStudents: number;

  @ApiProperty()
  @IsInt()
  maleStudents: number;

  @ApiProperty()
  @IsInt()
  studentsWithDisabilities: number;
}

export class SchoolWaterSupplyDto {
  @ApiProperty()
  @IsBoolean()
  connectedToPipeline: boolean;

  @ApiProperty()
  @IsBoolean()
  waterAvailability: boolean;

  @ApiProperty({ enum: WaterAvailabilityFrequency, required: false })
  @IsOptional()
  @IsEnum(WaterAvailabilityFrequency)
  availableDays?: WaterAvailabilityFrequency;

  @ApiProperty({ enum: CleanWaterStorageCapacity })
  @IsEnum(CleanWaterStorageCapacity)
  storageCapacity: CleanWaterStorageCapacity;

  @ApiProperty({ enum: MainWaterSource, required: false })
  @IsOptional()
  @IsEnum(MainWaterSource)
  mainWaterSource?: MainWaterSource;

  @ApiProperty({ enum: WaterSourceDistance })
  @IsEnum(WaterSourceDistance)
  distanceToSource: WaterSourceDistance;
}

export class SchoolSanitationDto {
  @ApiProperty({ enum: ToiletFacilityType })
  @IsEnum(ToiletFacilityType)
  toiletType: ToiletFacilityType;

  @ApiProperty({ enum: FacilitySlabConstructionMaterial })
  @IsEnum(FacilitySlabConstructionMaterial)
  @IsOptional()
  slabConstructionMaterial?: FacilitySlabConstructionMaterial;

  @ApiProperty()
  @IsInt()
  totalToilets: number;

  @ApiProperty()
  @IsBoolean()
  genderSeparation: boolean;

  @ApiProperty()
  @IsInt()
  femaleToilets: number;

  @ApiProperty()
  @IsInt()
  maleToilets: number;

  @ApiProperty()
  @IsBoolean()
  girlsRoom: boolean;

  @ApiProperty()
  @IsBoolean()
  disabilityAccess: boolean;

  @ApiProperty()
  @IsBoolean()
  staffToilets: boolean;

  @ApiProperty()
  @IsBoolean()
  hasToiletFullInLast2Years: boolean;

  @ApiProperty({ enum: ExcretaManagement, required: false })
  @IsOptional()
  @IsEnum(ExcretaManagement)
  excretaManagement?: ExcretaManagement;
}

export class SchoolHygieneDto {
  @ApiProperty()
  @IsBoolean()
  handwashingFacility: boolean;

  @ApiProperty({ enum: HandWashingFacilityType, required: false })
  @IsOptional()
  @IsEnum(HandWashingFacilityType)
  facilityType?: HandWashingFacilityType;

  @ApiProperty({ enum: HandWashingMaterial, required: false })
  @IsOptional()
  @IsEnum(HandWashingMaterial)
  handwashingMaterials?: HandWashingMaterial;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  handWashingfacilityNearToilet?: boolean;

  @ApiProperty({ enum: HandWashingFacilityType, required: false })
  @IsOptional()
  @IsEnum(HandWashingFacilityType)
  toiletHandWashingFacilityType?: HandWashingFacilityType;

  @ApiProperty({ enum: HandWashingMaterial, required: false })
  @IsOptional()
  @IsEnum(HandWashingMaterial)
  toiletHandWashingMaterials?: HandWashingMaterial;
}

export class SchoolSolidWasteManagementDto {
  @ApiProperty()
  @IsBoolean()
  wasteSeparation: boolean;

  @ApiProperty({ enum: WasteManagementAfterSeparation, required: false })
  @IsOptional()
  @IsEnum(WasteManagementAfterSeparation)
  wasteManagement?: WasteManagementAfterSeparation;

  @ApiProperty({ enum: WasteTreatmentType, required: false })
  @IsOptional()
  @IsEnum(WasteTreatmentType)
  treatmentType?: WasteTreatmentType;

  @ApiProperty({ enum: WasteCollectionFrequency, required: false })
  @IsOptional()
  @IsEnum(WasteCollectionFrequency)
  collectionFrequency?: WasteCollectionFrequency;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  collectionCost?: number;
}

export class SchoolLiquidWasteManagementDto {
  @ApiProperty({ enum: WasteWaterManagement })
  @IsEnum(WasteWaterManagement)
  liquidWasteManagement: WasteWaterManagement;
}

export class CreateSchoolSubmissionDto extends BaseCreateSubmissionDto {
  @ApiProperty({ type: SchoolGeneralInfoDto })
  @ValidateNested()
  @Type(() => SchoolGeneralInfoDto)
  generalInfo: SchoolGeneralInfoDto;

  @ApiProperty({ type: SchoolWaterSupplyDto })
  @ValidateNested()
  @Type(() => SchoolWaterSupplyDto)
  waterSupply: SchoolWaterSupplyDto;

  @ApiProperty({ type: SchoolSanitationDto })
  @ValidateNested()
  @Type(() => SchoolSanitationDto)
  sanitation: SchoolSanitationDto;

  @ApiProperty({ type: SchoolHygieneDto })
  @ValidateNested()
  @Type(() => SchoolHygieneDto)
  hygiene: SchoolHygieneDto;

  @ApiProperty({ type: SchoolSolidWasteManagementDto })
  @ValidateNested()
  @Type(() => SchoolSolidWasteManagementDto)
  solidWaste: SchoolSolidWasteManagementDto;

  @ApiProperty({ type: SchoolLiquidWasteManagementDto })
  @ValidateNested()
  @Type(() => SchoolLiquidWasteManagementDto)
  liquidWaste: SchoolLiquidWasteManagementDto;
}

export class SchoolSubmissionResponseDto extends BaseSubmissionResponseDto {
  @ApiProperty({ type: SchoolGeneralInfoDto })
  generalInfo: SchoolGeneralInfoDto;

  @ApiProperty({ type: SchoolWaterSupplyDto })
  waterSupply: SchoolWaterSupplyDto;

  @ApiProperty({ type: SchoolSanitationDto })
  sanitation: SchoolSanitationDto;

  @ApiProperty({ type: SchoolHygieneDto })
  hygiene: SchoolHygieneDto;

  @ApiProperty({ type: SchoolSolidWasteManagementDto })
  solidWaste: SchoolSolidWasteManagementDto;

  @ApiProperty({ type: SchoolLiquidWasteManagementDto })
  liquidWaste: SchoolLiquidWasteManagementDto;
}
