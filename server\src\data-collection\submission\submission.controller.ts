import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { SubmissionService } from './submission.service';
import { CreateHouseholdSubmissionDto } from './dto/household.dto';
import { CreateSchoolSubmissionDto } from './dto/school.dto';
import { CreateHealthFacilitySubmissionDto } from './dto/health-facility.dto';
import { CurrentUser } from 'src/common/decorators/current-user.decorator';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from 'src/common/guards/auth.guard';
import { MobileGuard } from 'src/common/guards/mobile.guard';
import { PrivilegeGuard } from 'src/common/guards/privilege.guard';
import { CreatePublicPlaceSubmissionDto, PublicPlaceSubmissionResponseDto } from './dto/public-place.dto';
import { CreateWasteCollectionCompanySubmissionDto, WasteCollectionCompanySubmissionResponseDto } from './dto/waste-collection-company.dto';
import { CreateWasteRecoveryCompanySubmissionDto, WasteRecoveryCompanySubmissionResponseDto } from './dto/waste-recovery-company.dto';
import { CreateWasteDisposalCompanySubmissionDto, WasteDisposalCompanySubmissionResponseDto } from './dto/waste-disposal-company.dto';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Privileges } from 'src/common/decorators/privileges.decorator';
import { Privilege } from '@prisma/client';

@ApiTags('Submission')
@UseGuards(AuthGuard, MobileGuard, PrivilegeGuard)
@ApiBearerAuth()
@Controller('submission')
export class SubmissionController {
  constructor(private readonly submissionService: SubmissionService) {}

  @Post('household')
  createHousehold(@Body() dto: CreateHouseholdSubmissionDto, @CurrentUser() user: any) {
    return this.submissionService.createHouseholdSubmission(dto, user.id);
  }

  @Post('school')
  createSchool(@Body() dto: CreateSchoolSubmissionDto, @CurrentUser() user: any) {
    return this.submissionService.createSchoolSubmission(dto, user.id);
  }

  @Post('health-facility')
  createHealthFacility(@Body() dto: CreateHealthFacilitySubmissionDto, @CurrentUser() user: any) {
    return this.submissionService.createHealthFacilitySubmission(dto, user.id);
  }

  @Post('public-place')
  createPublicPlace(@Body() dto: CreatePublicPlaceSubmissionDto, @CurrentUser() user: any) {
    return this.submissionService.createPublicPlaceSubmission(dto, user.id);
  }

  @Post('waste-collection-company')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Create waste collection company submission' })
  @ApiResponse({
    status: 201,
    description: 'Waste collection company submission created successfully',
    type: WasteCollectionCompanySubmissionResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  createWasteCollectionCompany(
    @Body() dto: CreateWasteCollectionCompanySubmissionDto,
    @CurrentUser() user: any,
  ) {
    return this.submissionService.createWasteCollectionCompanySubmission(dto, user.id);
  }

  @Post('waste-recovery-company')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Create waste recovery company submission' })
  @ApiResponse({
    status: 201,
    description: 'Waste recovery company submission created successfully',
    type: WasteRecoveryCompanySubmissionResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  createWasteRecoveryCompany(
    @Body() dto: CreateWasteRecoveryCompanySubmissionDto,
    @CurrentUser() user: any,
  ) {
    return this.submissionService.createWasteRecoveryCompanySubmission(dto, user.id);
  }

  @Post('waste-disposal-company')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Create waste disposal company submission' })
  @ApiResponse({
    status: 201,
    description: 'Waste disposal company submission created successfully',
    type: WasteDisposalCompanySubmissionResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  createWasteDisposalCompany(
    @Body() dto: CreateWasteDisposalCompanySubmissionDto,
    @CurrentUser() user: any,
  ) {
    return this.submissionService.createWasteDisposalCompanySubmission(dto, user.id);
  }
}
