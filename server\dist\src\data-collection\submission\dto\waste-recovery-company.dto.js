"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WasteRecoveryCompanySubmissionResponseDto = exports.CreateWasteRecoveryCompanySubmissionDto = exports.BusinessSiteResponseDto = exports.BusinessSiteDto = exports.HandledMaterialDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const client_1 = require("@prisma/client");
class HandledMaterialDto {
    materialName;
    supplier;
    quantityPerDay;
}
exports.HandledMaterialDto = HandledMaterialDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Plastic bottles' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HandledMaterialDto.prototype, "materialName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Local suppliers' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HandledMaterialDto.prototype, "supplier", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 500.5 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], HandledMaterialDto.prototype, "quantityPerDay", void 0);
class BusinessSiteDto {
    name;
    type;
    villageId;
    latitude;
    longitude;
}
exports.BusinessSiteDto = BusinessSiteDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Main Processing Site' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BusinessSiteDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Processing facility' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BusinessSiteDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 123 }),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], BusinessSiteDto.prototype, "villageId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: -1.9441, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], BusinessSiteDto.prototype, "latitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 30.0619, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], BusinessSiteDto.prototype, "longitude", void 0);
class BusinessSiteResponseDto {
    id;
    name;
    type;
    locationId;
    location;
}
exports.BusinessSiteResponseDto = BusinessSiteResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], BusinessSiteResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], BusinessSiteResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], BusinessSiteResponseDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], BusinessSiteResponseDto.prototype, "locationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Object)
], BusinessSiteResponseDto.prototype, "location", void 0);
class CreateWasteRecoveryCompanySubmissionDto {
    companyName;
    contactPerson;
    contactPhone;
    contactEmail;
    companyType;
    otherCompanyType;
    totalPersonnel;
    femalePersonnel;
    malePersonnel;
    operationType;
    handledMaterials;
    businessSites;
}
exports.CreateWasteRecoveryCompanySubmissionDto = CreateWasteRecoveryCompanySubmissionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'EcoRecycle Recovery Ltd' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWasteRecoveryCompanySubmissionDto.prototype, "companyName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Jane Smith' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWasteRecoveryCompanySubmissionDto.prototype, "contactPerson", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '+250788654321' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWasteRecoveryCompanySubmissionDto.prototype, "contactPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '<EMAIL>' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWasteRecoveryCompanySubmissionDto.prototype, "contactEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ServiceProviderType }),
    (0, class_validator_1.IsEnum)(client_1.ServiceProviderType),
    __metadata("design:type", String)
], CreateWasteRecoveryCompanySubmissionDto.prototype, "companyType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWasteRecoveryCompanySubmissionDto.prototype, "otherCompanyType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 30 }),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateWasteRecoveryCompanySubmissionDto.prototype, "totalPersonnel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 12 }),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateWasteRecoveryCompanySubmissionDto.prototype, "femalePersonnel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 18 }),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateWasteRecoveryCompanySubmissionDto.prototype, "malePersonnel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.OperationType }),
    (0, class_validator_1.IsEnum)(client_1.OperationType),
    __metadata("design:type", String)
], CreateWasteRecoveryCompanySubmissionDto.prototype, "operationType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [HandledMaterialDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => HandledMaterialDto),
    __metadata("design:type", Array)
], CreateWasteRecoveryCompanySubmissionDto.prototype, "handledMaterials", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [BusinessSiteDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => BusinessSiteDto),
    __metadata("design:type", Array)
], CreateWasteRecoveryCompanySubmissionDto.prototype, "businessSites", void 0);
class WasteRecoveryCompanySubmissionResponseDto {
    id;
    submissionId;
    submittedAt;
    companyName;
    contactPerson;
    contactPhone;
    contactEmail;
    companyType;
    otherCompanyType;
    totalPersonnel;
    femalePersonnel;
    malePersonnel;
    operationType;
    handledMaterials;
    businessSites;
}
exports.WasteRecoveryCompanySubmissionResponseDto = WasteRecoveryCompanySubmissionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WasteRecoveryCompanySubmissionResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WasteRecoveryCompanySubmissionResponseDto.prototype, "submissionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Date)
], WasteRecoveryCompanySubmissionResponseDto.prototype, "submittedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WasteRecoveryCompanySubmissionResponseDto.prototype, "companyName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WasteRecoveryCompanySubmissionResponseDto.prototype, "contactPerson", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WasteRecoveryCompanySubmissionResponseDto.prototype, "contactPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WasteRecoveryCompanySubmissionResponseDto.prototype, "contactEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ServiceProviderType }),
    __metadata("design:type", String)
], WasteRecoveryCompanySubmissionResponseDto.prototype, "companyType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], WasteRecoveryCompanySubmissionResponseDto.prototype, "otherCompanyType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], WasteRecoveryCompanySubmissionResponseDto.prototype, "totalPersonnel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], WasteRecoveryCompanySubmissionResponseDto.prototype, "femalePersonnel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], WasteRecoveryCompanySubmissionResponseDto.prototype, "malePersonnel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.OperationType }),
    __metadata("design:type", String)
], WasteRecoveryCompanySubmissionResponseDto.prototype, "operationType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [HandledMaterialDto] }),
    __metadata("design:type", Array)
], WasteRecoveryCompanySubmissionResponseDto.prototype, "handledMaterials", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [BusinessSiteResponseDto] }),
    __metadata("design:type", Array)
], WasteRecoveryCompanySubmissionResponseDto.prototype, "businessSites", void 0);
//# sourceMappingURL=waste-recovery-company.dto.js.map