import { FacilityType } from "@prisma/client";
export declare class IndicatorsRequestDto {
    level: "COUNTRY" | "PROVINCE" | "DISTRICT" | "SECTOR" | "CELL" | "VILLAGE";
    levelId?: string;
    startDate: string;
    endDate: string;
    facilityType: FacilityType;
}
export declare class IndicatorsResponseDto {
    level: string;
    levelId: string;
    startDate: string;
    endDate: string;
    facilityType: FacilityType;
    indicators: any[];
}
export declare class GeoMappingRequestDto {
    level: "PROVINCE" | "DISTRICT" | "SECTOR" | "CELL" | "VILLAGE";
    facilityType: FacilityType;
    startDate: string;
    endDate: string;
}
export declare class GeoMappingLocationData {
    id: number;
    name: string;
    latitude: number;
    longitude: number;
    indicators: any[];
}
export declare class GeoMappingResponseDto {
    level: string;
    startDate: string;
    endDate: string;
    facilityType: FacilityType;
    locations: GeoMappingLocationData[];
}
