import { CreateHouseholdInput, CreateHouseholdResponse, Household, HouseholdsListResponse, PaginationQuery } from '@/types/facility';
import axios from '../../utils/axios';

export const getAllHouseHolds = (query?: PaginationQuery) => {
    return axios.get<HouseholdsListResponse>(`/facilities/households`, { params: query });
};

export const createHousehold = (data: CreateHouseholdInput) => {
    return axios.post<CreateHouseholdResponse>(`/facilities/households`, data);
};

export const getHouseHold = (facilityId: string) => {
    return axios.get<Household>(`/facilities/households/${facilityId}`);
};

export const submitHouseholdForm = (facilityId: string, data: any) => {
    return axios.post(`/submission/household`, {
        facilityId,
        facilityType: "HOUSEHOLD",
        ...data
    });
};