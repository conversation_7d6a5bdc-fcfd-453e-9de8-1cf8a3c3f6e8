{"version": 3, "file": "waste-recovery-company.dto.js", "sourceRoot": "", "sources": ["../../../../../src/data-collection/submission/dto/waste-recovery-company.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAC9C,qDAAyG;AACzG,yDAAyC;AACzC,2CAAoE;AAEpE,MAAa,kBAAkB;IAG7B,YAAY,CAAS;IAIrB,QAAQ,CAAS;IAIjB,cAAc,CAAS;CACxB;AAZD,gDAYC;AATC;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IAC3C,IAAA,0BAAQ,GAAE;;wDACU;AAIrB;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IAC3C,IAAA,0BAAQ,GAAE;;oDACM;AAIjB;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IAC/B,IAAA,0BAAQ,GAAE;;0DACY;AAGzB,MAAa,eAAe;IAG1B,IAAI,CAAS;IAIb,IAAI,CAAS;IAIb,SAAS,CAAS;IAKlB,QAAQ,CAAU;IAKlB,SAAS,CAAU;CACpB;AAtBD,0CAsBC;AAnBC;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IAChD,IAAA,0BAAQ,GAAE;;6CACE;AAIb;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC;IAC/C,IAAA,0BAAQ,GAAE;;6CACE;AAIb;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;IAC7B,IAAA,uBAAK,GAAE;;kDACU;AAKlB;IAHC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAClD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;iDACO;AAKlB;IAHC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAClD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;kDACQ;AAGrB,MAAa,uBAAuB;IAElC,EAAE,CAAS;IAGX,IAAI,CAAS;IAGb,IAAI,CAAS;IAGb,UAAU,CAAS;IAGnB,QAAQ,CAKN;CACH;AApBD,0DAoBC;AAlBC;IADC,IAAA,qBAAW,GAAE;;mDACH;AAGX;IADC,IAAA,qBAAW,GAAE;;qDACD;AAGb;IADC,IAAA,qBAAW,GAAE;;qDACD;AAGb;IADC,IAAA,qBAAW,GAAE;;2DACK;AAGnB;IADC,IAAA,qBAAW,GAAE;;yDAMZ;AAGJ,MAAa,uCAAuC;IAGlD,WAAW,CAAS;IAIpB,aAAa,CAAS;IAItB,YAAY,CAAS;IAIrB,YAAY,CAAS;IAIrB,WAAW,CAAsB;IAKjC,gBAAgB,CAAU;IAI1B,cAAc,CAAS;IAIvB,eAAe,CAAS;IAIxB,aAAa,CAAS;IAItB,aAAa,CAAgB;IAM7B,gBAAgB,CAAuB;IAMvC,aAAa,CAAoB;CAClC;AArDD,0FAqDC;AAlDC;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC;IACnD,IAAA,0BAAQ,GAAE;;4EACS;AAIpB;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;IACtC,IAAA,0BAAQ,GAAE;;8EACW;AAItB;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC;IACzC,IAAA,0BAAQ,GAAE;;6EACU;AAIrB;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC;IACjD,IAAA,0BAAQ,GAAE;;6EACU;AAIrB;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,4BAAmB,EAAE,CAAC;IAC1C,IAAA,wBAAM,EAAC,4BAAmB,CAAC;;4EACK;AAKjC;IAHC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;iFACe;AAI1B;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;IAC5B,IAAA,uBAAK,GAAE;;+EACe;AAIvB;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;IAC5B,IAAA,uBAAK,GAAE;;gFACgB;AAIxB;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;IAC5B,IAAA,uBAAK,GAAE;;8EACc;AAItB;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,sBAAa,EAAE,CAAC;IACpC,IAAA,wBAAM,EAAC,sBAAa,CAAC;;8EACO;AAM7B;IAJC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,CAAC,kBAAkB,CAAC,EAAE,CAAC;IAC3C,IAAA,yBAAO,GAAE;IACT,IAAA,gCAAc,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IAC9B,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC;;iFACQ;AAMvC;IAJC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC;IACxC,IAAA,yBAAO,GAAE;IACT,IAAA,gCAAc,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IAC9B,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,eAAe,CAAC;;8EACK;AAGnC,MAAa,yCAAyC;IAEpD,EAAE,CAAS;IAGX,YAAY,CAAS;IAGrB,WAAW,CAAO;IAGlB,WAAW,CAAS;IAGpB,aAAa,CAAS;IAGtB,YAAY,CAAS;IAGrB,YAAY,CAAS;IAGrB,WAAW,CAAsB;IAGjC,gBAAgB,CAAU;IAG1B,cAAc,CAAS;IAGvB,eAAe,CAAS;IAGxB,aAAa,CAAS;IAGtB,aAAa,CAAgB;IAG7B,gBAAgB,CAAuB;IAGvC,aAAa,CAA4B;CAC1C;AA7CD,8FA6CC;AA3CC;IADC,IAAA,qBAAW,GAAE;;qEACH;AAGX;IADC,IAAA,qBAAW,GAAE;;+EACO;AAGrB;IADC,IAAA,qBAAW,GAAE;8BACD,IAAI;8EAAC;AAGlB;IADC,IAAA,qBAAW,GAAE;;8EACM;AAGpB;IADC,IAAA,qBAAW,GAAE;;gFACQ;AAGtB;IADC,IAAA,qBAAW,GAAE;;+EACO;AAGrB;IADC,IAAA,qBAAW,GAAE;;+EACO;AAGrB;IADC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,4BAAmB,EAAE,CAAC;;8EACV;AAGjC;IADC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;;mFACP;AAG1B;IADC,IAAA,qBAAW,GAAE;;iFACS;AAGvB;IADC,IAAA,qBAAW,GAAE;;kFACU;AAGxB;IADC,IAAA,qBAAW,GAAE;;gFACQ;AAGtB;IADC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,sBAAa,EAAE,CAAC;;gFACR;AAG7B;IADC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,CAAC,kBAAkB,CAAC,EAAE,CAAC;;mFACL;AAGvC;IADC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC;;gFACR"}