import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum, IsBoolean, IsInt, IsArray, IsOptional, IsNumber } from 'class-validator';
import { ServiceProviderType, ClientType, CompactionFrequency, TruckFrequency, RecordingMethod } from '@prisma/client';

export class CreateWasteDisposalCompanySubmissionDto {
  @ApiProperty({ example: 'SafeDispose Waste Management' })
  @IsString()
  companyName: string;

  @ApiProperty({ example: 123 })
  @IsInt()
  facilityVillageId: number;

  @ApiProperty({ example: -1.9441, required: false })
  @IsOptional()
  @IsNumber()
  facilityLatitude?: number;

  @ApiProperty({ example: 30.0619, required: false })
  @IsOptional()
  @IsNumber()
  facilityLongitude?: number;

  @ApiProperty({ example: 'Robert Johnson' })
  @IsString()
  contactPerson: string;

  @ApiProperty({ example: '+250788987654' })
  @IsString()
  contactPhone: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsString()
  contactEmail: string;

  @ApiProperty({ enum: ServiceProviderType })
  @IsEnum(ServiceProviderType)
  companyType: ServiceProviderType;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  otherCompanyType?: string;

  @ApiProperty({ example: 40 })
  @IsInt()
  totalPersonnel: number;

  @ApiProperty({ example: 15 })
  @IsInt()
  femalePersonnel: number;

  @ApiProperty({ example: 25 })
  @IsInt()
  malePersonnel: number;

  @ApiProperty({ enum: ClientType, isArray: true })
  @IsArray()
  @IsEnum(ClientType, { each: true })
  clientTypes: ClientType[];

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  otherClientTypes?: string[];

  @ApiProperty({ example: true })
  @IsBoolean()
  boundaryControl: boolean;

  @ApiProperty({ example: true })
  @IsBoolean()
  wasteDepositControl: boolean;

  @ApiProperty({ enum: CompactionFrequency })
  @IsEnum(CompactionFrequency)
  compactionFrequency: CompactionFrequency;

  @ApiProperty({ example: false })
  @IsBoolean()
  wasteBurning: boolean;

  @ApiProperty({ example: true })
  @IsBoolean()
  weighbridge: boolean;

  @ApiProperty({ example: 1500.75 })
  @IsNumber()
  wasteAmount: number;

  @ApiProperty({ enum: TruckFrequency })
  @IsEnum(TruckFrequency)
  truckFrequency: TruckFrequency;

  @ApiProperty({ enum: RecordingMethod, required: false })
  @IsOptional()
  @IsEnum(RecordingMethod)
  recordingMethod?: RecordingMethod;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  otherRecordingMethod?: string;
}

export class WasteDisposalCompanySubmissionResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  submissionId: string;

  @ApiProperty()
  submittedAt: Date;

  @ApiProperty()
  companyName: string;

  @ApiProperty()
  facilityLocationId: string;

  @ApiProperty({ required: false })
  facilityLatitude?: number;

  @ApiProperty({ required: false })
  facilityLongitude?: number;

  @ApiProperty()
  contactPerson: string;

  @ApiProperty()
  contactPhone: string;

  @ApiProperty()
  contactEmail: string;

  @ApiProperty({ enum: ServiceProviderType })
  companyType: ServiceProviderType;

  @ApiProperty({ required: false })
  otherCompanyType?: string;

  @ApiProperty()
  totalPersonnel: number;

  @ApiProperty()
  femalePersonnel: number;

  @ApiProperty()
  malePersonnel: number;

  @ApiProperty({ enum: ClientType, isArray: true })
  clientTypes: ClientType[];

  @ApiProperty({ type: [String], required: false })
  otherClientTypes?: string[];

  @ApiProperty()
  boundaryControl: boolean;

  @ApiProperty()
  wasteDepositControl: boolean;

  @ApiProperty({ enum: CompactionFrequency })
  compactionFrequency: CompactionFrequency;

  @ApiProperty()
  wasteBurning: boolean;

  @ApiProperty()
  weighbridge: boolean;

  @ApiProperty()
  wasteAmount: number;

  @ApiProperty({ enum: TruckFrequency })
  truckFrequency: TruckFrequency;

  @ApiProperty({ enum: RecordingMethod, required: false })
  recordingMethod?: RecordingMethod;

  @ApiProperty({ required: false })
  otherRecordingMethod?: string;
}