"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const prisma_module_1 = require("./prisma/prisma.module");
const config_1 = require("@nestjs/config");
const email_module_1 = require("./email/email.module");
const auth_module_1 = require("./auth/auth.module");
const users_module_1 = require("./users/users.module");
const monitoring_module_1 = require("./monitoring/monitoring.module");
const roles_module_1 = require("./roles/roles.module");
const provinces_module_1 = require("./provinces/provinces.module");
const districts_module_1 = require("./districts/districts.module");
const sectors_module_1 = require("./sectors/sectors.module");
const cells_module_1 = require("./cells/cells.module");
const villages_module_1 = require("./villages/villages.module");
const facilities_module_1 = require("./facilities/facilities.module");
const locations_module_1 = require("./locations/locations.module");
const submission_module_1 = require("./data-collection/submission/submission.module");
const indicators_module_1 = require("./reporting/indicators/indicators.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                cache: true,
                isGlobal: true
            }),
            prisma_module_1.PrismaModule,
            email_module_1.EmailModule,
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
            monitoring_module_1.MonitoringModule,
            roles_module_1.RolesModule,
            provinces_module_1.ProvincesModule,
            districts_module_1.DistrictsModule,
            sectors_module_1.SectorsModule,
            cells_module_1.CellsModule,
            villages_module_1.VillagesModule,
            facilities_module_1.FacilitiesModule,
            locations_module_1.LocationsModule,
            submission_module_1.SubmissionModule,
            indicators_module_1.IndicatorsModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [
            app_service_1.AppService,
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map