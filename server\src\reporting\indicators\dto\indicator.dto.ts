import { ApiProperty } from "@nestjs/swagger";
import { FacilityType } from "@prisma/client";
import { IsEnum, IsOptional, IsString } from "class-validator";

export class IndicatorsRequestDto {
    @ApiProperty({
        description: "Administrative level to filter data by",
        enum: ["COUNTRY", "PROVINCE", "DISTRICT", "SECTOR", "CELL", "VILLAGE"],
    })
    @IsString()
    level: "COUNTRY" | "PROVINCE" | "DISTRICT" | "SECTOR" | "CELL" | "VILLAGE";

    @ApiProperty({ description: "ID of the selected administrative level", required: false })
    @IsString()
    @IsOptional()
    levelId?: string;

    @ApiProperty({ description: "Start date in YYYY-MM-DD format" })
    @IsString()
    startDate: string;

    @ApiProperty({ description: "End date in YYYY-MM-DD format" })
    @IsString()
    endDate: string;

    @ApiProperty({ enum: FacilityType, description: "Type of facility to filter indicators" })
    @IsEnum(FacilityType)
    facilityType: FacilityType;
}

export class IndicatorsResponseDto {
    @ApiProperty({ description: "Administrative level queried" })
    level: string;

    @ApiProperty({ description: "ID of the administrative level" })
    levelId: string;

    @ApiProperty({ description: "Start date of the data period" })
    startDate: string;

    @ApiProperty({ description: "End date of the data period" })
    endDate: string;

    @ApiProperty({ enum: FacilityType, description: "Facility type used for filtering" })
    facilityType: FacilityType;

    @ApiProperty({
        description: "List of calculated indicators",
        type: [Object],
    })
    indicators: any[];
}

export class GeoMappingRequestDto {
    @ApiProperty({
        description: "Administrative level to aggregate data by",
        enum: ["PROVINCE", "DISTRICT", "SECTOR", "CELL", "VILLAGE"],
    })
    @IsString()
    level: "PROVINCE" | "DISTRICT" | "SECTOR" | "CELL" | "VILLAGE";

    @ApiProperty({ enum: FacilityType, description: "Type of facility to filter indicators" })
    @IsEnum(FacilityType)
    facilityType: FacilityType;

    @ApiProperty({ description: "Start date in YYYY-MM-DD format" })
    @IsString()
    startDate: string;

    @ApiProperty({ description: "End date in YYYY-MM-DD format" })
    @IsString()
    endDate: string;
}

export class GeoMappingLocationData {
    @ApiProperty({ description: "Location ID" })
    id: number;

    @ApiProperty({ description: "Location name" })
    name: string;

    @ApiProperty({ description: "Average latitude coordinate" })
    latitude: number;

    @ApiProperty({ description: "Average longitude coordinate" })
    longitude: number;

    @ApiProperty({
        description: "List of calculated indicators for this location",
        type: [Object],
    })
    indicators: any[];
}

export class GeoMappingResponseDto {
    @ApiProperty({ description: "Administrative level queried" })
    level: string;

    @ApiProperty({ description: "Start date of the data period" })
    startDate: string;

    @ApiProperty({ description: "End date of the data period" })
    endDate: string;

    @ApiProperty({ enum: FacilityType, description: "Facility type used for filtering" })
    facilityType: FacilityType;

    @ApiProperty({
        description: "List of locations with their indicators and coordinates",
        type: [GeoMappingLocationData],
    })
    locations: GeoMappingLocationData[];
}
