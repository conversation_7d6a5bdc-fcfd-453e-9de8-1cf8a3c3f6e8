import AppButton from '@/components/ui/Button';
import AppText from '@/components/ui/Text';
import { PRIMARY_COLOR } from '@/constants/colors';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  View
} from 'react-native';
import { OtpInput } from "react-native-otp-entry";
import tw from 'twrnc';
import { useAuth } from '../../../hooks/useAuth';
import { ApiError } from '../../../types/auth';

export default function Verify2FAScreen() {
  const [totpCode, setTotpCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const otpInputRef = useRef<any>(null);
  const { tempToken } = useLocalSearchParams<{ tempToken: string }>();

  const { verify2FA } = useAuth();

  useEffect(() => {
    if (!tempToken) {
      router.replace('/auth/login');
    }
  }, [tempToken]);

  const clearOTPInput = () => {
    setTotpCode('');
    otpInputRef.current?.clear();
  };

  const handleTextChange = (text: string) => {
    setTotpCode(text);
  };

  const handleVerify = async () => {
    if (!totpCode.trim() || totpCode.length !== 6) {
      Alert.alert('Error', 'Please enter a valid 6-digit code');
      return;
    }

    if (!tempToken) {
      Alert.alert('Error', 'No temporary token found. Please login again.');
      router.replace('/auth/login');
      return;
    }

    setIsLoading(true);
    try {
      await verify2FA(totpCode.trim(), tempToken);
      router.replace('/main');
    } catch (error) {
      const apiError = error as ApiError;
      let errorMessage = 'An error occurred during verification';
      if (apiError.message) {
        if (Array.isArray(apiError.message)) {
          errorMessage = apiError.message.join(', ');
        } else {
          errorMessage = apiError.message;
        }
      }
      Alert.alert('Verification Error', errorMessage);
      clearOTPInput();
    } finally {
      setIsLoading(false);
    }
  };




  return (
    <KeyboardAvoidingView
      style={tw`flex-1 bg-white`}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={tw`h-full`}
        keyboardShouldPersistTaps="handled"
      >
        <View style={tw`px-5 flex h-full gap-8`}>
          <View style={tw`flex flex-col gap-12`}>
            <View style={tw`flex gap-2`}>
              <AppText weight='bold' style={tw`text-2xl text-gray-600`}>Verification Code</AppText>
              <AppText style={tw``}>
                Enter your 6-digit verification code from your authenticator app
              </AppText>
            </View>
          </View>
          <View style={tw`flex flex-col gap-4`}>
            <OtpInput
              numberOfDigits={6}
              onTextChange={(text) => handleTextChange(text)}
              autoFocus={true}
              focusColor={PRIMARY_COLOR}
            />

            <AppButton title="Verify" onPress={handleVerify} loading={isLoading} disabled={isLoading} />
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}