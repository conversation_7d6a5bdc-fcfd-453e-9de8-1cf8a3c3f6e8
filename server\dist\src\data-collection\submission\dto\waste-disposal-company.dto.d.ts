import { ServiceProviderType, ClientType, CompactionFrequency, TruckFrequency, RecordingMethod } from '@prisma/client';
export declare class CreateWasteDisposalCompanySubmissionDto {
    companyName: string;
    facilityVillageId: number;
    facilityLatitude?: number;
    facilityLongitude?: number;
    contactPerson: string;
    contactPhone: string;
    contactEmail: string;
    companyType: ServiceProviderType;
    otherCompanyType?: string;
    totalPersonnel: number;
    femalePersonnel: number;
    malePersonnel: number;
    clientTypes: ClientType[];
    otherClientTypes?: string[];
    boundaryControl: boolean;
    wasteDepositControl: boolean;
    compactionFrequency: CompactionFrequency;
    wasteBurning: boolean;
    weighbridge: boolean;
    wasteAmount: number;
    truckFrequency: TruckFrequency;
    recordingMethod?: RecordingMethod;
    otherRecordingMethod?: string;
}
export declare class WasteDisposalCompanySubmissionResponseDto {
    id: string;
    submissionId: string;
    submittedAt: Date;
    companyName: string;
    facilityLocationId: string;
    facilityLatitude?: number;
    facilityLongitude?: number;
    contactPerson: string;
    contactPhone: string;
    contactEmail: string;
    companyType: ServiceProviderType;
    otherCompanyType?: string;
    totalPersonnel: number;
    femalePersonnel: number;
    malePersonnel: number;
    clientTypes: ClientType[];
    otherClientTypes?: string[];
    boundaryControl: boolean;
    wasteDepositControl: boolean;
    compactionFrequency: CompactionFrequency;
    wasteBurning: boolean;
    weighbridge: boolean;
    wasteAmount: number;
    truckFrequency: TruckFrequency;
    recordingMethod?: RecordingMethod;
    otherRecordingMethod?: string;
}
