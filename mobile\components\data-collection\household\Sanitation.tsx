import AppText from '@/components/ui/Text';
import * as enums from '@/types/enums';
import { getEnumOptions, isEnumKey } from '@/utils/enum';
import { Picker } from '@react-native-picker/picker';
import React, { useEffect } from 'react';
import { Controller, useWatch } from 'react-hook-form';
import { View } from 'react-native';
import tw from 'twrnc';

const SanitationSection = ({ control, errors }: any) => {

    const toiletType: enums.ToiletFacilityType = useWatch({ control, name: 'sanitation.toiletType' });
    const hasToiletFullInLast2Years = useWatch({ control, name: 'sanitation.hasToiletFullInLast2Years' });

    return (

        <>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Toilet facility mainly used by the household</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="sanitation.toiletType"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={onChange}>
                                <Picker.Item label="Select Toilet Type" value="" />
                                {getEnumOptions(enums.ToiletFacilityType).map(opt => (
                                    <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.sanitation?.toiletType && <AppText style={tw`text-red-500`}>{errors.sanitation?.toiletType.message}</AppText>}
            </View>

            {(isEnumKey(enums.ToiletFacilityType, 'PIT_WITH_SLAB', toiletType)
                || isEnumKey(enums.ToiletFacilityType, 'PIT_WITHOUT_SLAB', toiletType)
                || isEnumKey(enums.ToiletFacilityType, 'VENTILATED_IMPROVED_PIT', toiletType)
                || isEnumKey(enums.ToiletFacilityType, 'COMPOSTING_TOILET', toiletType)
                || isEnumKey(enums.ToiletFacilityType, 'URINE_DIVERSION_DRY_TOILET', toiletType))
                &&
                <View style={tw`mb-4`}>
                    <AppText style={tw`mb-2`}>Completeness of the toilet facility (super structure)</AppText>
                    <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                        <Controller
                            control={control}
                            name="sanitation.toiletCompleteness"
                            render={({ field: { onChange, value } }) => (
                                <Picker selectedValue={value} onValueChange={onChange}>
                                    <Picker.Item label="Select Completeness" value="" />
                                    {getEnumOptions(enums.ToiletFacilityCompleteness).map(opt => (
                                        <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                    ))}
                                </Picker>
                            )}
                        />
                    </View>
                    {errors.sanitation?.toiletCompleteness && <AppText style={tw`text-red-500`}>{errors.sanitation?.toiletCompleteness.message}</AppText>}
                </View>}

            {(isEnumKey(enums.ToiletFacilityType, 'PIT_WITH_SLAB', toiletType)
                || isEnumKey(enums.ToiletFacilityType, 'PIT_WITHOUT_SLAB', toiletType)
                || isEnumKey(enums.ToiletFacilityType, 'VENTILATED_IMPROVED_PIT', toiletType)
                || isEnumKey(enums.ToiletFacilityType, 'COMPOSTING_TOILET', toiletType)
                || isEnumKey(enums.ToiletFacilityType, 'URINE_DIVERSION_DRY_TOILET', toiletType))
                && <View style={tw`mb-4`}>
                    <AppText style={tw`mb-2`}>Slab Construction Material</AppText>
                    <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                        <Controller
                            control={control}
                            name="sanitation.slabConstructionMaterial"
                            render={({ field: { onChange, value } }) => (
                                <Picker selectedValue={value} onValueChange={onChange}>
                                    <Picker.Item label="Select Material" value="" />
                                    {getEnumOptions(enums.FacilitySlabConstructionMaterial).map(opt => (
                                        <Picker.Item key={opt.value} label={opt.label} value={opt.value as string} />
                                    ))}
                                </Picker>
                            )}
                        />
                    </View>
                    {errors.sanitation?.slabConstructionMaterial && <AppText style={tw`text-red-500`}>{errors.sanitation?.slabConstructionMaterial.message}</AppText>}
                </View>}

            {(isEnumKey(enums.ToiletFacilityType, 'PIT_WITH_SLAB', toiletType)
                || isEnumKey(enums.ToiletFacilityType, 'PIT_WITHOUT_SLAB', toiletType)
                || isEnumKey(enums.ToiletFacilityType, 'VENTILATED_IMPROVED_PIT', toiletType)
                || isEnumKey(enums.ToiletFacilityType, 'COMPOSTING_TOILET', toiletType)
                || isEnumKey(enums.ToiletFacilityType, 'URINE_DIVERSION_DRY_TOILET', toiletType))
                && <View style={tw`mb-4`}>
                    <AppText style={tw`mb-2`}>Does the household share the toilet with any other household</AppText>
                    <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                        <Controller
                            control={control}
                            name="sanitation.toiletShared"
                            render={({ field: { onChange, value } }) => (
                                <Picker selectedValue={value ? 'yes' : 'no'} onValueChange={v => onChange(v === 'yes')}>
                                    <Picker.Item label="Select Option" value="" />
                                    <Picker.Item label="Yes" value="yes" />
                                    <Picker.Item label="No" value="no" />
                                </Picker>
                            )}
                        />
                    </View>
                    {errors.sanitation?.toiletShared && <AppText style={tw`text-red-500`}>{errors.sanitation?.toiletShared.message}</AppText>}
                </View>}

            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Did the toilet get full in the last 2 years?</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="sanitation.hasToiletFullInLast2Years"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value ? 'yes' : 'no'} onValueChange={v => onChange(v === 'yes')}>
                                <Picker.Item label="Select Option" value="" />
                                <Picker.Item label="Yes" value="yes" />
                                <Picker.Item label="No" value="no" />
                            </Picker>
                        )}
                    />
                </View>
                {errors.sanitation?.hasToiletFullInLast2Years && <AppText style={tw`text-red-500`}>{errors.sanitation?.hasToiletFullInLast2Years.message}</AppText>}
            </View>

            {hasToiletFullInLast2Years &&
                <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>How was the excreta managed when the toilet got full?</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="sanitation.excretaManagement"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={onChange}>
                                <Picker.Item label="Select Management" value="" />
                                {getEnumOptions(enums.ExcretaManagement).map(opt => (
                                    <Picker.Item key={opt.value} label={opt.label} value={opt.value as string} />
                                ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.sanitation?.excretaManagement && <AppText style={tw`text-red-500`}>{errors.sanitation?.excretaManagement.message}</AppText>}
            </View>}
        </>
    )
}

export default SanitationSection;