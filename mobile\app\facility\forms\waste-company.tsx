import React, { useEffect, useState } from "react";

import AppText from "@/components/ui/Text";
import tw from "twrnc";
import { Alert, View } from "react-native";
import { useLocalSearchParams, router } from "expo-router";
import { getVillage } from "@/services/administrative";
import { Village } from "@/types/administrative";
import { PRIMARY_COLOR } from "@/constants/colors";
import WasteCollectionCompanyForm from "@/components/data-collection/waste-company/Collection";
import WasteRecoveryCompanyForm from "@/components/data-collection/waste-company/Recovery";
import WasteDisposalCompanyForm from "@/components/data-collection/waste-company/Disposal";
import {
    submitWasteCollectionCompany,
    submitWasteRecoveryCompany,
    submitWasteDisposalCompany
} from "@/services/facility/waste-company";

const WasteCompanyForm = () => {

    const { type, villageId } = useLocalSearchParams();
    const [village, setVillage] = useState<Village | null>(null);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        const fetchVillage = async () => {
            try {
                const res = await getVillage(villageId.toString());
                setVillage(res.data);
            } catch (error) {
                console.log(error);
                Alert.alert("Error", "Failed to load village");
            }
        };
        if (villageId) fetchVillage();
    }, []);

    // Helper function to map display values to enum values
    const mapDisplayToEnum = (displayValue: any, enumType: string): string => {
        const mappings: { [key: string]: { [key: string]: string } } = {
            gender: {
                '0': 'MALE',        // Handle numeric enum values
                '1': 'FEMALE',      // Handle numeric enum values
                'Male': 'MALE',     // Handle string values
                'Female': 'FEMALE', // Handle string values
                'Other': 'OTHER'
            },
            serviceProvider: {
                'municipal/public operator': 'MUNICIPAL_PUBLIC_OPERATOR',
                'Municipal/public operator (waste management department in the city)': 'MUNICIPAL_PUBLIC_OPERATOR',
                'Private service contractor': 'PRIVATE_SERVICE_CONTRACTOR',
                'NGO/CSOs': 'NGO_CSOS',
                'Other (please specify)': 'OTHER',
                'Other': 'OTHER'
            },
            clientType: {
                'Households': 'HOUSEHOLDS',
                'Markets': 'MARKETS',
                'Schools': 'SCHOOLS',
                'Health facilities': 'HEALTH_FACILITIES',
                'Industries/Factories': 'INDUSTRIES_FACTORIES',
                'Businesses/restaurants/Hotels/(street) vendors': 'BUSINESSES_RESTAURANTS_HOTELS_VENDORS',
                'Administrative buildings': 'ADMINISTRATIVE_BUILDINGS',
                'Others (please specify)': 'OTHERS',
                'Others': 'OTHERS',  // ✅ Add mapping for "Others" without "(please specify)"
                // Handle specialized operators as "OTHERS" for backend compatibility
                'SPECIALIZED_WASTE_COLLECTION_OPERATORS': 'OTHERS',
                'SPECIALIZED_WASTE_RECOVERY_OPERATORS': 'OTHERS'
            },
            wasteMaterial: {
                'Biodegradable': 'BIODEGRADABLE',
                'Non-biodegradable': 'NON_BIODEGRADABLE'
            },
            wasteDestination: {
                'Dumpsite/landfill': 'DUMPSITE_LANDFILL',
                'Material recovery/recycling facility': 'MATERIAL_RECOVERY_FACILITY',
                'Others': 'OTHERS'
            },
            recordingMethod: {
                'paper logbook': 'PAPER_LOGBOOK',  // Match the enum value
                'Paper logbook': 'PAPER_LOGBOOK',  // Handle display case
                'others': 'OTHER',                 // Match the enum value
                'Others, specify': 'OTHER'         // Handle display case
            },
            operationType: {
                // Handle enum values from frontend
                'End-of-chain recycler/recoverer facility ': 'END_OF_CHAIN_RECYCLER',
                'Apex trader (Buying and Selling recycled commodities,': 'APEX_TRADER',
                'Intermediate trader person or company': 'INTERMEDIATE_TRADER',
                // Handle display strings from form
                'End-of-chain recycler/recoverer facility that handles the final stages of the recycling process': 'END_OF_CHAIN_RECYCLER',
                'Apex trader (Buying and Selling recycled commodities)': 'APEX_TRADER',
                'Intermediate trader person or company that buys recyclable materials from collectors or smaller processors and then sells these materials to larger recycling facilities or manufacturers': 'INTERMEDIATE_TRADER'
            },
            compactionFrequency: {
                'Daily': 'DAILY',
                'Weekly': 'WEEKLY',
                'Monthly': 'MONTHLY',
                'Annually': 'ANNUALLY',
                'Emergency only': 'EMERGENCY_ONLY',
                'Never': 'NEVER'
            },
            truckFrequency: {
                'Daily': 'DAILY',
                'Weekly': 'WEEKLY',
                'Other': 'OTHER'
            }
        };

        return mappings[enumType]?.[displayValue] || displayValue;
    };

    // Helper function to map arrays of display values to enum values
    const mapArrayToEnums = (displayArray: string[], enumType: string): string[] => {
        return displayArray?.map(value => mapDisplayToEnum(value, enumType)) || [];
    };

    // Helper function to transform form data to API format
    const transformFormData = (formData: any, companyType: string) => {
        // Transform nested form structure to flat structure expected by API
        const transformedData: any = {};

        // Handle different company types
        if (companyType === 'collection') {
            // Collection company specific transformations
            transformedData.companyName = formData.basicInfo?.companyName;
            transformedData.ownerName = formData.basicInfo?.ownerName;
            transformedData.ownerGender = mapDisplayToEnum(String(formData.basicInfo?.ownerGender), 'gender');
            transformedData.contactPhone = formData.contactDetails?.contactPhone;
            transformedData.contactEmail = formData.contactDetails?.contactEmail;
            transformedData.companyType = mapDisplayToEnum(formData.managementCapacity?.companyType, 'serviceProvider');
            transformedData.otherCompanyType = formData.managementCapacity?.otherCompanyType;
            transformedData.totalPersonnel = formData.managementCapacity?.totalPersonnel;
            transformedData.femalePersonnel = formData.managementCapacity?.femalePersonnel;
            transformedData.malePersonnel = formData.managementCapacity?.malePersonnel;
            transformedData.clientTypes = mapArrayToEnums(formData.clientCoverage?.clientTypes || [], 'clientType');

            transformedData.otherClientTypes = formData.clientCoverage?.otherClientTypes || [];
            transformedData.wasteSeparation = formData.technicalCapacity?.wasteSeparation;
            transformedData.separatedMaterials = mapArrayToEnums(formData.technicalCapacity?.separatedMaterials || [], 'wasteMaterial');
            transformedData.otherSeparatedMaterials = formData.technicalCapacity?.otherSeparatedMaterials || [];
            transformedData.wasteDestination = mapDisplayToEnum(formData.technicalCapacity?.wasteDestination, 'wasteDestination');
            transformedData.destinationDetails = formData.technicalCapacity?.destinationDetails;
            transformedData.weighbridge = formData.recordingMonitoring?.weighbridge;
            transformedData.recordingMethod = mapDisplayToEnum(formData.recordingMonitoring?.recordingMethod, 'recordingMethod');
            transformedData.otherRecordingMethod = formData.recordingMonitoring?.otherRecordingMethod;
        } else if (companyType === 'recovery') {
            // Recovery company specific transformations
            transformedData.companyName = formData.basicInfo?.companyName;
            transformedData.contactPerson = formData.contactDetails?.contactPerson;
            transformedData.contactPhone = formData.contactDetails?.contactPhone;
            transformedData.contactEmail = formData.contactDetails?.contactEmail;
            transformedData.companyType = mapDisplayToEnum(formData.managementCapacity?.companyType, 'serviceProvider');
            transformedData.otherCompanyType = formData.managementCapacity?.otherCompanyType;
            transformedData.totalPersonnel = formData.managementCapacity?.totalPersonnel;
            transformedData.femalePersonnel = formData.managementCapacity?.femalePersonnel;
            transformedData.malePersonnel = formData.managementCapacity?.malePersonnel;
            transformedData.operationType = mapDisplayToEnum(formData.operationType?.operationType, 'operationType');

            // Transform handled materials
            transformedData.handledMaterials = formData.handledMaterials?.materials?.map((material: any) => ({
                materialName: material.materialName,
                supplier: material.supplier,
                quantityPerDay: Number(material.quantityPerDay), // Ensure it's a number
            })) || [];

            // Transform business sites
            transformedData.businessSites = formData.businessSites?.sites?.map((site: any) => ({
                name: site.name,
                type: site.type,
                villageId: Number(site.villageId), // Ensure it's a number
                latitude: site.latitude ? Number(site.latitude) : undefined,
                longitude: site.longitude ? Number(site.longitude) : undefined,
            })) || [];
        } else if (companyType === 'disposal') {
            // Disposal company specific transformations
            transformedData.companyName = formData.basicInfo?.companyName;
            transformedData.facilityVillageId = formData.basicInfo?.facilityVillageId;
            transformedData.facilityLatitude = formData.basicInfo?.facilityLatitude;
            transformedData.facilityLongitude = formData.basicInfo?.facilityLongitude;
            transformedData.contactPerson = formData.contactDetails?.contactPerson;
            transformedData.contactPhone = formData.contactDetails?.contactPhone;
            transformedData.contactEmail = formData.contactDetails?.contactEmail;
            transformedData.companyType = mapDisplayToEnum(formData.managementCapacity?.companyType, 'serviceProvider');
            transformedData.otherCompanyType = formData.managementCapacity?.otherCompanyType;
            transformedData.totalPersonnel = formData.managementCapacity?.totalPersonnel;
            transformedData.femalePersonnel = formData.managementCapacity?.femalePersonnel;
            transformedData.malePersonnel = formData.managementCapacity?.malePersonnel;
            // Handle client types and specialized operators
            const clientTypes = formData.marketCoverage?.clientTypes || [];
            const specializedOperators: string[] = [];
            const regularClientTypes: string[] = [];

            // Separate specialized operators from regular client types
            clientTypes.forEach((clientType: string) => {
                if (clientType === 'SPECIALIZED_WASTE_COLLECTION_OPERATORS') {
                    specializedOperators.push('Specialized waste collection operators');
                } else if (clientType === 'SPECIALIZED_WASTE_RECOVERY_OPERATORS') {
                    specializedOperators.push('Specialized waste recovery operators');
                } else {
                    regularClientTypes.push(clientType);
                }
            });

            // If we have specialized operators, add OTHERS to client types
            if (specializedOperators.length > 0) {
                regularClientTypes.push('OTHERS');
            }

            transformedData.clientTypes = mapArrayToEnums(regularClientTypes, 'clientType');
            transformedData.otherClientTypes = [
                ...specializedOperators,
                ...(formData.marketCoverage?.otherClientTypes || [])
            ];
            transformedData.boundaryControl = formData.operationalPractices?.boundaryControl;
            transformedData.wasteDepositControl = formData.operationalPractices?.wasteDepositControl;
            transformedData.compactionFrequency = mapDisplayToEnum(formData.operationalPractices?.compactionFrequency, 'compactionFrequency');
            transformedData.wasteBurning = formData.operationalPractices?.wasteBurning;
            transformedData.weighbridge = formData.operationalPractices?.weighbridge;
            transformedData.wasteAmount = formData.operationalPractices?.wasteAmount;
            transformedData.truckFrequency = mapDisplayToEnum(formData.operationalPractices?.truckFrequency, 'truckFrequency');
            transformedData.recordingMethod = mapDisplayToEnum(formData.operationalPractices?.recordingMethod, 'recordingMethod');
            transformedData.otherRecordingMethod = formData.operationalPractices?.otherRecordingMethod;
        }

        return transformedData;
    };

    // Waste Collection Company submission
    const handleWasteCollectionSubmission = async (data: any) => {
        setLoading(true);
        try {
            const transformedData = transformFormData(data, 'collection');
            console.log('Submitting waste collection company:', transformedData);

            const response = await submitWasteCollectionCompany(transformedData);
            console.log('Submission successful:', response);

            Alert.alert(
                "Success",
                "Waste collection company data submitted successfully",
                [{ text: 'OK', onPress: () => router.replace('/main') }]
            );
        } catch (error: any) {
            console.error('Submission error:', error);
            let errorMessage = 'Failed to submit waste collection company data';

            if (error?.message) {
                if (Array.isArray(error.message)) {
                    // Handle array of validation errors
                    errorMessage = error.message.join('\n• ');
                    errorMessage = '• ' + errorMessage; // Add bullet to first item
                } else {
                    errorMessage = error.message;
                }
            }

            Alert.alert("Validation Error", errorMessage);
        } finally {
            setLoading(false);
        }
    };

    // Waste Recovery Company submission
    const handleWasteRecoverySubmission = async (data: any) => {
        setLoading(true);
        try {
            const transformedData = transformFormData(data, 'recovery');
            console.log('Submitting waste recovery company:', transformedData);

            const response = await submitWasteRecoveryCompany(transformedData);
            console.log('Submission successful:', response);

            Alert.alert(
                "Success",
                "Waste recovery company data submitted successfully",
                [{ text: 'OK', onPress: () => router.replace('/main') }]
            );
        } catch (error: any) {
            console.error('Submission error:', error);
            let errorMessage = 'Failed to submit waste recovery company data';

            if (error?.message) {
                if (Array.isArray(error.message)) {
                    // Handle array of validation errors
                    errorMessage = error.message.join('\n• ');
                    errorMessage = '• ' + errorMessage; // Add bullet to first item
                } else {
                    errorMessage = error.message;
                }
            }

            Alert.alert("Validation Error", errorMessage);
        } finally {
            setLoading(false);
        }
    };

    // Waste Disposal Company submission
    const handleWasteDisposalSubmission = async (data: any) => {
        setLoading(true);
        try {
            const transformedData = transformFormData(data, 'disposal');
            console.log('Submitting waste disposal company:', transformedData);

            const response = await submitWasteDisposalCompany(transformedData);
            console.log('Submission successful:', response);

            Alert.alert(
                "Success",
                "Waste disposal company data submitted successfully",
                [{ text: 'OK', onPress: () => router.replace('/main') }]
            );
        } catch (error: any) {
            console.error('Submission error:', error);
            let errorMessage = 'Failed to submit waste disposal company data';

            if (error?.message) {
                if (Array.isArray(error.message)) {
                    // Handle array of validation errors
                    errorMessage = error.message.join('\n• ');
                    errorMessage = '• ' + errorMessage; // Add bullet to first item
                } else {
                    errorMessage = error.message;
                }
            }

            Alert.alert("Validation Error", errorMessage);
        } finally {
            setLoading(false);
        }
    };

   switch (type) {
        case "Waste Collection & Transport Company":
            return <WasteCollectionCompanyForm onSubmit={handleWasteCollectionSubmission} />;
        case "Waste Recovery Company":
            return <WasteRecoveryCompanyForm onSubmit={handleWasteRecoverySubmission} />;
        case "Waste Disposal Company":
            return <WasteDisposalCompanyForm onSubmit={handleWasteDisposalSubmission} />;
        default:
            return null;
    }
};

export default WasteCompanyForm;
