"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeoMappingResponseDto = exports.GeoMappingLocationData = exports.GeoMappingRequestDto = exports.IndicatorsResponseDto = exports.IndicatorsRequestDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const client_1 = require("@prisma/client");
const class_validator_1 = require("class-validator");
class IndicatorsRequestDto {
    level;
    levelId;
    startDate;
    endDate;
    facilityType;
}
exports.IndicatorsRequestDto = IndicatorsRequestDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Administrative level to filter data by",
        enum: ["COUNTRY", "PROVINCE", "DISTRICT", "SECTOR", "CELL", "VILLAGE"],
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], IndicatorsRequestDto.prototype, "level", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "ID of the selected administrative level", required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], IndicatorsRequestDto.prototype, "levelId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Start date in YYYY-MM-DD format" }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], IndicatorsRequestDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "End date in YYYY-MM-DD format" }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], IndicatorsRequestDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.FacilityType, description: "Type of facility to filter indicators" }),
    (0, class_validator_1.IsEnum)(client_1.FacilityType),
    __metadata("design:type", String)
], IndicatorsRequestDto.prototype, "facilityType", void 0);
class IndicatorsResponseDto {
    level;
    levelId;
    startDate;
    endDate;
    facilityType;
    indicators;
}
exports.IndicatorsResponseDto = IndicatorsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Administrative level queried" }),
    __metadata("design:type", String)
], IndicatorsResponseDto.prototype, "level", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "ID of the administrative level" }),
    __metadata("design:type", String)
], IndicatorsResponseDto.prototype, "levelId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Start date of the data period" }),
    __metadata("design:type", String)
], IndicatorsResponseDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "End date of the data period" }),
    __metadata("design:type", String)
], IndicatorsResponseDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.FacilityType, description: "Facility type used for filtering" }),
    __metadata("design:type", String)
], IndicatorsResponseDto.prototype, "facilityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "List of calculated indicators",
        type: [Object],
    }),
    __metadata("design:type", Array)
], IndicatorsResponseDto.prototype, "indicators", void 0);
class GeoMappingRequestDto {
    level;
    facilityType;
    startDate;
    endDate;
}
exports.GeoMappingRequestDto = GeoMappingRequestDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Administrative level to aggregate data by",
        enum: ["PROVINCE", "DISTRICT", "SECTOR", "CELL", "VILLAGE"],
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GeoMappingRequestDto.prototype, "level", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.FacilityType, description: "Type of facility to filter indicators" }),
    (0, class_validator_1.IsEnum)(client_1.FacilityType),
    __metadata("design:type", String)
], GeoMappingRequestDto.prototype, "facilityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Start date in YYYY-MM-DD format" }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GeoMappingRequestDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "End date in YYYY-MM-DD format" }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GeoMappingRequestDto.prototype, "endDate", void 0);
class GeoMappingLocationData {
    id;
    name;
    latitude;
    longitude;
    indicators;
}
exports.GeoMappingLocationData = GeoMappingLocationData;
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Location ID" }),
    __metadata("design:type", Number)
], GeoMappingLocationData.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Location name" }),
    __metadata("design:type", String)
], GeoMappingLocationData.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Average latitude coordinate" }),
    __metadata("design:type", Number)
], GeoMappingLocationData.prototype, "latitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Average longitude coordinate" }),
    __metadata("design:type", Number)
], GeoMappingLocationData.prototype, "longitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "List of calculated indicators for this location",
        type: [Object],
    }),
    __metadata("design:type", Array)
], GeoMappingLocationData.prototype, "indicators", void 0);
class GeoMappingResponseDto {
    level;
    startDate;
    endDate;
    facilityType;
    locations;
}
exports.GeoMappingResponseDto = GeoMappingResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Administrative level queried" }),
    __metadata("design:type", String)
], GeoMappingResponseDto.prototype, "level", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Start date of the data period" }),
    __metadata("design:type", String)
], GeoMappingResponseDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "End date of the data period" }),
    __metadata("design:type", String)
], GeoMappingResponseDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.FacilityType, description: "Facility type used for filtering" }),
    __metadata("design:type", String)
], GeoMappingResponseDto.prototype, "facilityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "List of locations with their indicators and coordinates",
        type: [GeoMappingLocationData],
    }),
    __metadata("design:type", Array)
], GeoMappingResponseDto.prototype, "locations", void 0);
//# sourceMappingURL=indicator.dto.js.map