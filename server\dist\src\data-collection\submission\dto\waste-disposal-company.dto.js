"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WasteDisposalCompanySubmissionResponseDto = exports.CreateWasteDisposalCompanySubmissionDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const client_1 = require("@prisma/client");
class CreateWasteDisposalCompanySubmissionDto {
    companyName;
    facilityVillageId;
    facilityLatitude;
    facilityLongitude;
    contactPerson;
    contactPhone;
    contactEmail;
    companyType;
    otherCompanyType;
    totalPersonnel;
    femalePersonnel;
    malePersonnel;
    clientTypes;
    otherClientTypes;
    boundaryControl;
    wasteDepositControl;
    compactionFrequency;
    wasteBurning;
    weighbridge;
    wasteAmount;
    truckFrequency;
    recordingMethod;
    otherRecordingMethod;
}
exports.CreateWasteDisposalCompanySubmissionDto = CreateWasteDisposalCompanySubmissionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'SafeDispose Waste Management' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWasteDisposalCompanySubmissionDto.prototype, "companyName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 123 }),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateWasteDisposalCompanySubmissionDto.prototype, "facilityVillageId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: -1.9441, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateWasteDisposalCompanySubmissionDto.prototype, "facilityLatitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 30.0619, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateWasteDisposalCompanySubmissionDto.prototype, "facilityLongitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Robert Johnson' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWasteDisposalCompanySubmissionDto.prototype, "contactPerson", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '+250788987654' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWasteDisposalCompanySubmissionDto.prototype, "contactPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '<EMAIL>' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWasteDisposalCompanySubmissionDto.prototype, "contactEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ServiceProviderType }),
    (0, class_validator_1.IsEnum)(client_1.ServiceProviderType),
    __metadata("design:type", String)
], CreateWasteDisposalCompanySubmissionDto.prototype, "companyType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWasteDisposalCompanySubmissionDto.prototype, "otherCompanyType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 40 }),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateWasteDisposalCompanySubmissionDto.prototype, "totalPersonnel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 15 }),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateWasteDisposalCompanySubmissionDto.prototype, "femalePersonnel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 25 }),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateWasteDisposalCompanySubmissionDto.prototype, "malePersonnel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ClientType, isArray: true }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(client_1.ClientType, { each: true }),
    __metadata("design:type", Array)
], CreateWasteDisposalCompanySubmissionDto.prototype, "clientTypes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateWasteDisposalCompanySubmissionDto.prototype, "otherClientTypes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateWasteDisposalCompanySubmissionDto.prototype, "boundaryControl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateWasteDisposalCompanySubmissionDto.prototype, "wasteDepositControl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.CompactionFrequency }),
    (0, class_validator_1.IsEnum)(client_1.CompactionFrequency),
    __metadata("design:type", String)
], CreateWasteDisposalCompanySubmissionDto.prototype, "compactionFrequency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: false }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateWasteDisposalCompanySubmissionDto.prototype, "wasteBurning", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateWasteDisposalCompanySubmissionDto.prototype, "weighbridge", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 1500.75 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateWasteDisposalCompanySubmissionDto.prototype, "wasteAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.TruckFrequency }),
    (0, class_validator_1.IsEnum)(client_1.TruckFrequency),
    __metadata("design:type", String)
], CreateWasteDisposalCompanySubmissionDto.prototype, "truckFrequency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.RecordingMethod, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.RecordingMethod),
    __metadata("design:type", String)
], CreateWasteDisposalCompanySubmissionDto.prototype, "recordingMethod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWasteDisposalCompanySubmissionDto.prototype, "otherRecordingMethod", void 0);
class WasteDisposalCompanySubmissionResponseDto {
    id;
    submissionId;
    submittedAt;
    companyName;
    facilityLocationId;
    facilityLatitude;
    facilityLongitude;
    contactPerson;
    contactPhone;
    contactEmail;
    companyType;
    otherCompanyType;
    totalPersonnel;
    femalePersonnel;
    malePersonnel;
    clientTypes;
    otherClientTypes;
    boundaryControl;
    wasteDepositControl;
    compactionFrequency;
    wasteBurning;
    weighbridge;
    wasteAmount;
    truckFrequency;
    recordingMethod;
    otherRecordingMethod;
}
exports.WasteDisposalCompanySubmissionResponseDto = WasteDisposalCompanySubmissionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WasteDisposalCompanySubmissionResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WasteDisposalCompanySubmissionResponseDto.prototype, "submissionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Date)
], WasteDisposalCompanySubmissionResponseDto.prototype, "submittedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WasteDisposalCompanySubmissionResponseDto.prototype, "companyName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WasteDisposalCompanySubmissionResponseDto.prototype, "facilityLocationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Number)
], WasteDisposalCompanySubmissionResponseDto.prototype, "facilityLatitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Number)
], WasteDisposalCompanySubmissionResponseDto.prototype, "facilityLongitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WasteDisposalCompanySubmissionResponseDto.prototype, "contactPerson", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WasteDisposalCompanySubmissionResponseDto.prototype, "contactPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WasteDisposalCompanySubmissionResponseDto.prototype, "contactEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ServiceProviderType }),
    __metadata("design:type", String)
], WasteDisposalCompanySubmissionResponseDto.prototype, "companyType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], WasteDisposalCompanySubmissionResponseDto.prototype, "otherCompanyType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], WasteDisposalCompanySubmissionResponseDto.prototype, "totalPersonnel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], WasteDisposalCompanySubmissionResponseDto.prototype, "femalePersonnel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], WasteDisposalCompanySubmissionResponseDto.prototype, "malePersonnel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ClientType, isArray: true }),
    __metadata("design:type", Array)
], WasteDisposalCompanySubmissionResponseDto.prototype, "clientTypes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String], required: false }),
    __metadata("design:type", Array)
], WasteDisposalCompanySubmissionResponseDto.prototype, "otherClientTypes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Boolean)
], WasteDisposalCompanySubmissionResponseDto.prototype, "boundaryControl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Boolean)
], WasteDisposalCompanySubmissionResponseDto.prototype, "wasteDepositControl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.CompactionFrequency }),
    __metadata("design:type", String)
], WasteDisposalCompanySubmissionResponseDto.prototype, "compactionFrequency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Boolean)
], WasteDisposalCompanySubmissionResponseDto.prototype, "wasteBurning", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Boolean)
], WasteDisposalCompanySubmissionResponseDto.prototype, "weighbridge", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], WasteDisposalCompanySubmissionResponseDto.prototype, "wasteAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.TruckFrequency }),
    __metadata("design:type", String)
], WasteDisposalCompanySubmissionResponseDto.prototype, "truckFrequency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.RecordingMethod, required: false }),
    __metadata("design:type", String)
], WasteDisposalCompanySubmissionResponseDto.prototype, "recordingMethod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], WasteDisposalCompanySubmissionResponseDto.prototype, "otherRecordingMethod", void 0);
//# sourceMappingURL=waste-disposal-company.dto.js.map