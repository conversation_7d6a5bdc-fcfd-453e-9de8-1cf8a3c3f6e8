import AppText from '@/components/ui/Text';
import * as enums from '@/types/enums';
import { getEnumOptions } from '@/utils/enum';
import { Picker } from '@react-native-picker/picker';
import React from 'react';
import { Controller } from 'react-hook-form';
import { View } from 'react-native';
import tw from 'twrnc';

const GeneralInfoSection = ({ control, errors }: any) => {
    return (
        <>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Public Place Category</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="generalInfo.category"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={onChange}>
                                <Picker.Item label="Select Category" value="" />
                                {getEnumOptions(enums.MarketCategory).map(opt => (
                                    <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.generalInfo?.category && <AppText style={tw`text-red-500`}>{errors.generalInfo?.category.message}</AppText>}
            </View>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Public Place Opening Days</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="generalInfo.openingDays"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={onChange}>
                                <Picker.Item label="Select Opening Days" value="" />
                                {getEnumOptions(enums.MarketOpeningDays).map(opt => (
                                    <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.generalInfo?.openingDays && <AppText style={tw`text-red-500`}>{errors.generalInfo?.openingDays.message}</AppText>}
            </View>
        </>
    );
}

export default GeneralInfoSection;