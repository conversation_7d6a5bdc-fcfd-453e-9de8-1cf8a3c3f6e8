import AppText from '@/components/ui/Text';
import * as enums from '@/types/enums';
import { getEnumOptions } from '@/utils/enum';
import { Picker } from '@react-native-picker/picker';
import React, { useEffect } from 'react';
import { Controller, useFormContext, useWatch } from 'react-hook-form';
import { View } from 'react-native';
import { TextInput } from 'react-native-paper';
import tw from 'twrnc';

const GeneralInfoSection = ({ control, errors }: any) => {
    
    const totalStudents = useWatch({ control, name: 'generalInfo.totalStudents' });
    const femaleStudents = useWatch({ control, name: 'generalInfo.femaleStudents' });
    const maleStudents = useWatch({ control, name: 'generalInfo.maleStudents' });

    const { setValue } = useFormContext();

    return (
        <>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>School Name</AppText>
                <Controller
                    control={control}
                    name="generalInfo.schoolName"
                    render={({ field: { onChange, onBlur, value } }) => (
                        <TextInput
                            style={tw`bg-white`}
                            mode="outlined"
                            placeholder="School Name"
                            outlineColor="#E5E7EB"
                            value={value}
                            onChangeText={onChange}
                            onBlur={onBlur}
                        />
                    )}
                />
                {errors.generalInfo?.schoolName && <AppText style={tw`text-red-500`}>{errors.generalInfo?.schoolName.message}</AppText>}
            </View>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Type of the school</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="generalInfo.schoolType"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={onChange}>
                                <Picker.Item label="Select School Type" value="" />
                                {getEnumOptions(enums.SchoolType).map(opt => (
                                    <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.generalInfo?.schoolType && <AppText style={tw`text-red-500`}>{errors.generalInfo?.schoolType.message}</AppText>}
            </View>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>School Management</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="generalInfo.managementType"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={onChange}>
                                <Picker.Item label="Select Management Type" value="" />
                                {getEnumOptions(enums.SchoolManagement).map(opt => (
                                    <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.generalInfo?.managementType && <AppText style={tw`text-red-500`}>{errors.generalInfo?.managementType.message}</AppText>}
            </View>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Is the school day or boarding one?</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="generalInfo.dayBoarding"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={onChange}>
                                <Picker.Item label="Select Type" value="" />
                                {getEnumOptions(enums.SchoolTypeDayBoarding).map(opt => (
                                    <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.generalInfo?.dayBoarding && <AppText style={tw`text-red-500`}>{errors.generalInfo?.dayBoarding.message}</AppText>}
            </View>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Total number of students</AppText>
                <Controller
                    control={control}
                    name="generalInfo.totalStudents"
                    render={({ field: { onChange, onBlur, value } }) => (
                        <TextInput
                            style={tw`bg-white`}
                            mode="outlined"
                            placeholder="Total Students"
                            outlineColor="#E5E7EB"
                            inputMode="numeric"
                            value={value}
                            onChangeText={onChange}
                            onBlur={onBlur}
                        />
                    )}
                />
                {errors.generalInfo?.totalStudents && <AppText style={tw`text-red-500`}>{errors.generalInfo?.totalStudents.message}</AppText>}
            </View>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Number of female students</AppText>
                <Controller
                    control={control}
                    name="generalInfo.femaleStudents"
                    render={({ field: { onChange, onBlur, value } }) => (
                        <TextInput
                            style={tw`bg-white`}
                            mode="outlined"
                            placeholder="Female Students"
                            outlineColor="#E5E7EB"
                            inputMode="numeric"
                            value={value}
                            onChangeText={onChange}
                            onBlur={onBlur}
                        />
                    )}
                />
                {errors.generalInfo?.femaleStudents && <AppText style={tw`text-red-500`}>{errors.generalInfo?.femaleStudents.message}</AppText>}
            </View>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Number of male students</AppText>
                <Controller
                    control={control}
                    name="generalInfo.maleStudents"
                    render={({ field: { onChange, onBlur, value } }) => (
                        <TextInput
                            style={tw`bg-white`}
                            mode="outlined"
                            placeholder="Male Students"
                            outlineColor="#E5E7EB"
                            inputMode="numeric"
                            value={value}
                            onChangeText={onChange}
                            onBlur={onBlur}
                        />
                    )}
                />
                {errors.generalInfo?.maleStudents && <AppText style={tw`text-red-500`}>{errors.generalInfo?.maleStudents.message}</AppText>}
            </View>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Number of students living with disabilities</AppText>
                <Controller
                    control={control}
                    name="generalInfo.studentsWithDisabilities"
                    render={({ field: { onChange, onBlur, value } }) => (
                        <TextInput
                            style={tw`bg-white`}
                            mode="outlined"
                            placeholder="Students with Disabilities"
                            outlineColor="#E5E7EB"
                            inputMode="numeric"
                            value={value}
                            onChangeText={onChange}
                            onBlur={onBlur}
                        />
                    )}
                />
                {errors.generalInfo?.studentsWithDisabilities && <AppText style={tw`text-red-500`}>{errors.generalInfo?.studentsWithDisabilities.message}</AppText>}
            </View>
        </>
    );
}

export default GeneralInfoSection;