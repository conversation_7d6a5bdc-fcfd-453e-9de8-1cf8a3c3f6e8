import AppText from '@/components/ui/Text';
import { PRIMARY_COLOR } from '@/constants/colors';
import * as enums from '@/types/enums';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import React, { useState } from 'react';
import { Controller } from 'react-hook-form';
import { View } from 'react-native';
import { Button, TextInput } from 'react-native-paper';
import tw from 'twrnc';

const GeneralInfoSection = ({ control, errors }: any) => {
    const [show, setShow] = useState(false);
    const [pickerDate, setPickerDate] = useState<Date | undefined>(undefined);

    return (
        <>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Household Head Name</AppText>
                <Controller
                    control={control}
                    name="generalInfo.headOfHouseholdName"
                    render={({ field: { onChange, onBlur, value } }) => (
                        <TextInput
                            style={tw`bg-white`}
                            mode="outlined"
                            placeholder="Full name"
                            outlineColor='#E5E7EB'
                            value={value} onChangeText={onChange} onBlur={onBlur}
                        />
                    )}
                />
                {errors.generalInfo?.headOfHouseholdName && <AppText style={tw`text-red-500`}>{errors.generalInfo?.headOfHouseholdName.message}</AppText>}
            </View>

            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Gender of the household head</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="generalInfo.genderOfHead"
                        render={({ field: { onChange, value } }) => (
                            <Picker
                                selectedValue={value}
                                onValueChange={val => onChange(val)}
                            >
                                <Picker.Item label="Select Gender" value="" />
                                {Object.keys(enums.Gender)
                                    .filter(key => isNaN(Number(key)))
                                    .map(key => (
                                        <Picker.Item key={key} label={key.charAt(0) + key.slice(1).toLowerCase()} value={key} />
                                    ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.generalInfo?.genderOfHead && <AppText style={tw`text-red-500`}>{errors.generalInfo?.genderOfHead.message}</AppText>}

            </View>

            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Date of Birth of the household head</AppText>
                <Controller
                    control={control}
                    name="generalInfo.dateOfBirthOfHead"
                    render={({ field: { onChange, value } }) => {
                        let displayDate = value;
                        let parsedDate: Date | undefined = undefined;
                        if (value) {
                            parsedDate = new Date(value);
                            if (!isNaN(parsedDate.getTime())) {
                                displayDate = parsedDate.toLocaleDateString();
                            }
                        }
                        return (
                            <>
                                <Button mode="contained" onPress={() => setShow(true)} style={tw`mb-2 rounded-xl py-1 bg-[${PRIMARY_COLOR}]`}>
                                    {displayDate ? displayDate : 'Select Date'}
                                </Button>
                                {show && (
                                    <DateTimePicker
                                        testID="dateTimePicker"
                                        value={parsedDate || new Date()}
                                        mode="date"
                                        is24Hour={true}
                                        display="default"
                                        onChange={(event, selectedDate) => {
                                            setShow(false);
                                            if (selectedDate) {
                                                setPickerDate(selectedDate);
                                                onChange(selectedDate.toISOString());
                                            }
                                        }}
                                    />
                                )}
                            </>
                        );
                    }}
                />
                {errors.generalInfo?.dateOfBirthOfHead && <AppText style={tw`text-red-500`}>{errors.generalInfo?.dateOfBirthOfHead.message}</AppText>}
            </View>

            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Education Level of the household head</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="generalInfo.educationLevelOfHead"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={val => onChange(val)}>
                                <Picker.Item label="Select Education Level" value="" />
                                {Object.keys(enums.EducationLevel)
                                    .map(key => (
                                        <Picker.Item key={key} label={key.charAt(0) + key.slice(1).toLowerCase()} value={key} />
                                    ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.generalInfo?.educationLevelOfHead && <AppText style={tw`text-red-500`}>{errors.generalInfo?.educationLevelOfHead.message}</AppText>}
            </View>

            <View style={tw`mb-2`}>
                <AppText style={tw`mb-2`}>Total number of people living in the household</AppText>
                <Controller
                    control={control}
                    name="generalInfo.householdSize"
                    render={({ field: { onChange, onBlur, value } }) => (
                        <TextInput
                            style={tw`bg-white`}
                            mode="outlined"
                            placeholder="Household Size"
                            outlineColor='#E5E7EB'
                            inputMode='numeric'
                            value={value} onChangeText={onChange} onBlur={onBlur} />
                    )}
                />
                {errors.generalInfo?.householdSize && <AppText style={tw`text-red-500`}>{errors.generalInfo?.householdSize.message}</AppText>}
            </View>

            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Number of children (under 18) living in the household </AppText>
                <Controller
                    control={control}
                    name="generalInfo.childrenUnder18"
                    render={({ field: { onChange, onBlur, value } }) => (
                        <TextInput style={tw`bg-white`}
                            mode="outlined"
                            placeholder="Children Under 18"
                            outlineColor='#E5E7EB'
                            inputMode='numeric'
                            value={value} onChangeText={onChange} onBlur={onBlur} />
                    )}
                />
                {errors.generalInfo?.childrenUnder18 && <AppText style={tw`text-red-500`}>{errors.generalInfo?.childrenUnder18.message}</AppText>}
            </View>

            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Number of persons with disabilities living in the household</AppText>
                <Controller
                    control={control}
                    name="generalInfo.personsWithDisabilities"
                    render={({ field: { onChange, onBlur, value } }) => (
                        <TextInput style={tw`bg-white`}
                            mode="outlined"
                            placeholder="Persons With Disabilities"
                            outlineColor='#E5E7EB'
                            inputMode='numeric'
                            value={value} onChangeText={onChange} onBlur={onBlur} />
                    )}
                />
                {errors.generalInfo?.personsWithDisabilities && <AppText style={tw`text-red-500`}>{errors.generalInfo?.personsWithDisabilities.message}</AppText>}
            </View>
        </>
    );
};

export default GeneralInfoSection;