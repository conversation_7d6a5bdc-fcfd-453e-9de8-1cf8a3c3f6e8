import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TooltipModule } from 'primeng/tooltip';

interface User {
  name: string;
  role: string;
  avatar?: string;
}

@Component({
  selector: 'app-topbar',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    TooltipModule
  ],
  templateUrl: './topbar.component.html',
  styleUrls: ['./topbar.component.css']
})
export class TopbarComponent implements OnInit {
  @Input() isSidebarCollapsed = false;

  // Current user data
  currentUser: User = {
    name: '<PERSON>',
    role: 'System Administrator',
    // avatar: '/images/user-avatar.jpg'
  };

  notificationCount = 3;

  constructor(private router: Router) {}

  ngOnInit() {
    // Initialize component, load user data, etc.
    this.loadUserData();
    this.loadNotifications();
  }

  toggleUserMenu() {
    console.log('User menu toggled');

    this.router.navigate(['/profile']);
  }

  // Load current user data
  private loadUserData() {
 
  }

  // Load notifications count
  private loadNotifications() {
  
  }

  // Handle notification click
  onNotificationClick() {
    console.log('Notifications clicked');
    this.router.navigate(['/notifications']);
  }
}
