import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';
import { Router } from '@angular/router';
import { throwError } from 'rxjs';

// Import your models
import { AuthResponse } from '../models/auth-response';
import { LoginRequest } from '../models/login-request';
import { User } from '../models/user';
import { ResetPasswordRequest } from '../models/reset-password-request';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private apiUrl = 'http://localhost:8080/api/v1/auth'; 
  private currentUserSubject: BehaviorSubject<User | null> = new BehaviorSubject<User | null>(null);
  public currentUser: Observable<User | null>;

  constructor(
    private http: HttpClient,
    private router: Router
  ) {
    // Get user from localStorage on service initialization
    this.initializeAuth();
    this.currentUser = this.currentUserSubject.asObservable();
  }

  private initializeAuth(): void {
    const storedUser = localStorage.getItem('currentUser');
    const token = localStorage.getItem('token');
    
    // Only initialize with user if both user and valid token exist
    if (storedUser && token && this.isTokenValid(token)) {
      try {
        const user = JSON.parse(storedUser);
        this.currentUserSubject.next(user);
      } catch (error) {
        console.error('Error parsing stored user:', error);
        this.clearAuthData();
        this.currentUserSubject.next(null);
      }
    } else {
      this.clearAuthData();
      this.currentUserSubject.next(null);
    }
  }

  private isTokenValid(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp > currentTime;
    } catch (error) {
      return false;
    }
  }

  private clearAuthData(): void {
    localStorage.removeItem('currentUser');
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
  }

  public get currentUserValue(): User | null {
    return this.currentUserSubject.value;
  }

  // Updated login method with improved error handling
  login(loginRequest: LoginRequest): Observable<AuthResponse> {
    console.log('AuthService: Attempting login for:', loginRequest.email);
    
    return this.http.post<AuthResponse>(`${this.apiUrl}/login`, loginRequest)
      .pipe(
        tap(response => {
          console.log('AuthService: Login response received:', response);
        }),
        map(response => {
          // Map accessToken to token for consistency
          if (response && (response as any).accessToken) {
            (response as any).token = (response as any).accessToken;
          }
          if (response && response.token && !response.requires2FA) {
            console.log('AuthService: Login successful, storing auth data');
            this.setAuthData(response);
          } else if (response.requires2FA) {
            console.log('AuthService: 2FA required, not storing auth data yet');
          }
          return response;
        }),
        catchError(error => {
          console.error('AuthService: Login error:', error);
          return throwError(() => error);
        })
      );
  }

  // New method to properly set authentication data
  private setAuthData(response: AuthResponse): void {
    if (response.user) {
      localStorage.setItem('currentUser', JSON.stringify(response.user));
      this.currentUserSubject.next(response.user);
    }
    
    if (response.token) {
      localStorage.setItem('token', response.token);
    }
    
    if (response.refreshToken) {
      localStorage.setItem('refreshToken', response.refreshToken);
    }
    
    console.log('AuthService: Auth data stored successfully');
    console.log('AuthService: Current user set to:', response.user);
    console.log('AuthService: Token stored:', !!response.token);
  }

  // Updated 2FA verification with proper auth data handling
  verify2FA(payload: { tempToken?: string; totpCode: string }): Observable<any> {
    console.log('AuthService: Verifying 2FA with payload:', payload);
    
    return this.http.post<any>(`${this.apiUrl}/verify-2fa`, payload)
      .pipe(
        tap(response => {
          console.log('AuthService: 2FA verification response:', response);
        }),
        map(response => {
          // Map accessToken to token for consistency
          if (response && response.accessToken) {
            response.token = response.accessToken;
          }
          // Store user details and jwt token after successful 2FA
          if (response && response.token) {
            console.log('AuthService: 2FA successful, storing auth data');
            this.setAuthData(response);
          }
          return response;
        }),
        catchError(error => {
          console.error('AuthService: 2FA verification error:', error);
          return throwError(() => error);
        })
      );
  }

  // Updated logout method
  logout(): void {
    console.log('AuthService: Logging out user');
    this.clearAuthData();
    this.currentUserSubject.next(null);
    this.router.navigate(['/login']);
  }

  // Updated authentication check with better logging
  isAuthenticated(): boolean {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('currentUser');
    
    console.log('AuthService: Checking authentication');
    console.log('AuthService: Token exists:', !!token);
    console.log('AuthService: User exists:', !!user);
    
    if (!token || !user) {
      console.log('AuthService: Not authenticated - missing token or user');
      return false;
    }
    
    // Check if token is expired
    const isValidToken = this.isTokenValid(token);
    console.log('AuthService: Token is valid:', isValidToken);
    
    if (!isValidToken) {
      console.log('AuthService: Token expired, clearing auth data');
      this.clearAuthData();
      this.currentUserSubject.next(null);
      return false;
    }
    
    // Ensure currentUserSubject is synced with localStorage
    if (!this.currentUserSubject.value && user) {
      try {
        const parsedUser = JSON.parse(user);
        this.currentUserSubject.next(parsedUser);
        console.log('AuthService: Synced user from localStorage');
      } catch (error) {
        console.error('AuthService: Error parsing stored user:', error);
        this.clearAuthData();
        return false;
      }
    }
    
    console.log('AuthService: User is authenticated');
    return true;
  }

  // Get authentication token
  getToken(): string | null {
    return localStorage.getItem('token');
  }

  // Forgot password
  forgotPassword(email: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/forgot-password`, { email });
  }

  // Reset password
  resetPassword(resetRequest: ResetPasswordRequest): Observable<any> {
    return this.http.post(`${this.apiUrl}/reset-password`, resetRequest);
  }

  // Set password (for first-time users)
  setPassword(token: string, password: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/set-password`, { token, password });
  }

  // Verify email
  verifyEmail(token: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/verify-email`, { token });
  }

  // Updated refresh token method
  refreshToken(): Observable<AuthResponse> {
    const refreshToken = localStorage.getItem('refreshToken');
    return this.http.post<AuthResponse>(`${this.apiUrl}/refresh`, { refreshToken })
      .pipe(
        map(response => {
          if (response && response.token) {
            localStorage.setItem('token', response.token);
            if (response.refreshToken) {
              localStorage.setItem('refreshToken', response.refreshToken);
            }
            // Update user data if provided
            if (response.user) {
              localStorage.setItem('currentUser', JSON.stringify(response.user));
              this.currentUserSubject.next(response.user);
            }
          }
          return response;
        }),
        catchError(error => {
          console.error('AuthService: Token refresh failed:', error);
          this.logout(); // Force logout on refresh failure
          return throwError(() => error);
        })
      );
  }

  // Get user profile
  getUserProfile(): Observable<User> {
    return this.http.get<User>('http://localhost:8080/api/v1/users/me', {
      headers: this.getAuthHeaders()
    });
  }

  // Update user profile
  updateProfile(user: Partial<User>): Observable<User> {
    return this.http.put<User>(`${this.apiUrl}/profile`, user, {
      headers: this.getAuthHeaders()
    });
  }

  // Change password
  changePassword(currentPassword: string, newPassword: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/change-password`, {
      currentPassword,
      newPassword
    }, {
      headers: this.getAuthHeaders()
    });
  }

  // Updated HTTP Headers with authentication
  private getAuthHeaders(): HttpHeaders {
    const token = this.getToken();
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    if (token) {
      return headers.set('Authorization', `Bearer ${token}`);
    }

    return headers;
  }

  // Check if user has specific role
  hasRole(role: string): boolean {
    const currentUser = this.currentUserValue;
    return currentUser ? currentUser.roles?.includes(role) || false : false;
  }

  // Check if user has any of the specified roles
  hasAnyRole(roles: string[]): boolean {
    const currentUser = this.currentUserValue;
    if (!currentUser || !currentUser.roles) {
      return false;
    }
    return roles.some(role => currentUser.roles!.includes(role));
  }

  // Updated 2FA setup method with better error handling
  get2FASetup(): Observable<any> {
    console.log('AuthService: Getting 2FA setup');
    console.log('AuthService: API URL:', `${this.apiUrl}/2fa/setup`);
    console.log('AuthService: Auth token exists:', !!this.getToken());
    console.log('AuthService: Is authenticated:', this.isAuthenticated());
    
    // Check authentication before making the request
    if (!this.isAuthenticated()) {
      console.error('AuthService: User not authenticated for 2FA setup');
      return throwError(() => new Error('User not authenticated'));
    }
    
    return this.http.get<any>(`${this.apiUrl}/2fa/setup`, {
      headers: this.getAuthHeaders()
    }).pipe(
      tap(response => {
        console.log('AuthService: 2FA setup response:', response);
      }),
      catchError(error => {
        console.error('AuthService: 2FA setup error details:', error);
        console.error('AuthService: Error status:', error.status);
        console.error('AuthService: Error message:', error.message);
        
        // Handle 401 errors by forcing logout
        if (error.status === 401) {
          console.log('AuthService: 401 error, logging out user');
          this.logout();
        }
        
        return throwError(() => error);
      })
    );
  }

  // Updated 2FA enable method
  enable2FA(payload: { totpCode: string }): Observable<any> {
    console.log('AuthService: Enabling 2FA with payload:', payload);
    
    if (!this.isAuthenticated()) {
      console.error('AuthService: User not authenticated for 2FA enable');
      return throwError(() => new Error('User not authenticated'));
    }
    
    return this.http.post<any>(`${this.apiUrl}/2fa/enable`, payload, {
      headers: this.getAuthHeaders()
    }).pipe(
      tap(response => {
        console.log('AuthService: Enable 2FA response:', response);
        
        // Update user data if 2FA status changed
        if (response && response.user) {
          localStorage.setItem('currentUser', JSON.stringify(response.user));
          this.currentUserSubject.next(response.user);
        }
      }),
      catchError(error => {
        console.error('AuthService: Enable 2FA error:', error);
        
        // Handle 401 errors by forcing logout
        if (error.status === 401) {
          console.log('AuthService: 401 error, logging out user');
          this.logout();
        }
        
        return throwError(() => error);
      })
    );
  }

  // Method to manually check and refresh authentication state
  checkAuthState(): void {
    console.log('AuthService: Manual auth state check');
    const isAuth = this.isAuthenticated();
    console.log('AuthService: Auth state result:', isAuth);
    
    if (!isAuth && this.currentUserSubject.value) {
      console.log('AuthService: Clearing stale user data');
      this.currentUserSubject.next(null);
    }
  }

  loginWith2FA(email: string, password: string, totpCode: string): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(`${this.apiUrl}/login-2fa`, { email, password, totpCode })
      .pipe(
        tap(response => {
          if (response && response.accessToken) {
            response.token = response.accessToken;
          }
          if (response && response.token) {
            this.setAuthData(response);
          }
        }),
        catchError(error => throwError(() => error))
      );
  }

  setSession(response: any) {
    if (response.accessToken) {
      localStorage.setItem('token', response.accessToken);
    }
    if (response.refreshToken) {
      localStorage.setItem('refreshToken', response.refreshToken);
    }
    if (response.user) {
      localStorage.setItem('currentUser', JSON.stringify(response.user));
      this.currentUserSubject.next(response.user);
    }
  }
}