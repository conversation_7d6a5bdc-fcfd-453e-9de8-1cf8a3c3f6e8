import { IndicatorsService } from './indicators.service';
import { IndicatorsRequestDto, GeoMappingRequestDto } from './dto/indicator.dto';
export declare class IndicatorsController {
    private indicatorsService;
    constructor(indicatorsService: IndicatorsService);
    getBasicIndicators(query: IndicatorsRequestDto): Promise<import("./dto/indicator.dto").IndicatorsResponseDto>;
    getGeoIndicatorMapping(query: GeoMappingRequestDto): Promise<import("./dto/indicator.dto").GeoMappingResponseDto>;
}
