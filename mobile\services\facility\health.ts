import { CreateHealthFacilityInput, CreateHealthFacilityResponse, HealthFacilitiesListResponse, HealthFacility, PaginationQuery } from "@/types/facility";
import axios from "../../utils/axios";

export const getAllHealtFacilities = (query?: PaginationQuery) => {
    return axios.get<HealthFacilitiesListResponse>(`/facilities/health-facilities`, { params: query });
};

export const createHealthFacility = (data: CreateHealthFacilityInput) => {
    return axios.post<CreateHealthFacilityResponse>(`/facilities/health-facilities`, data);
};

export const getHealthFacility = (id: string) => {
    return axios.get<HealthFacility>(`/facilities/health-facilities/${id}`);
}

export const submitHealthFacilityForm = (facilityId: string, data: any) => {
    return axios.post(`/submission/health-facility`, {
        facilityId,
        facilityType: "HEALTH_FACILITY",
        ...data
    });
};