"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PublicPlaceSubmissionResponseDto = exports.CreatePublicPlaceSubmissionDto = exports.PublicPlaceLiquidWasteManagementDto = exports.PublicPlaceSolidWasteManagementDto = exports.PublicPlaceHygieneDto = exports.PublicPlaceSanitationDto = exports.PublicPlaceWaterSupplyDto = exports.PublicPlaceGeneralInfoDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const client_1 = require("@prisma/client");
const base_submission_dto_1 = require("./base-submission.dto");
class PublicPlaceGeneralInfoDto {
    category;
    openingDays;
}
exports.PublicPlaceGeneralInfoDto = PublicPlaceGeneralInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.PublicPlaceCategory }),
    (0, class_validator_1.IsEnum)(client_1.PublicPlaceCategory),
    __metadata("design:type", String)
], PublicPlaceGeneralInfoDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.PublicPlaceOpeningDays }),
    (0, class_validator_1.IsEnum)(client_1.PublicPlaceOpeningDays),
    __metadata("design:type", String)
], PublicPlaceGeneralInfoDto.prototype, "openingDays", void 0);
class PublicPlaceWaterSupplyDto {
    connectedToPipeline;
    waterAvailability;
    availableDays;
    storageCapacity;
    mainWaterSource;
    distanceToSource;
}
exports.PublicPlaceWaterSupplyDto = PublicPlaceWaterSupplyDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PublicPlaceWaterSupplyDto.prototype, "connectedToPipeline", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WaterAvailability }),
    (0, class_validator_1.IsEnum)(client_1.WaterAvailability),
    __metadata("design:type", String)
], PublicPlaceWaterSupplyDto.prototype, "waterAvailability", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WaterAvailabilityFrequency, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.WaterAvailabilityFrequency),
    __metadata("design:type", String)
], PublicPlaceWaterSupplyDto.prototype, "availableDays", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.CleanWaterStorageCapacity }),
    (0, class_validator_1.IsEnum)(client_1.CleanWaterStorageCapacity),
    __metadata("design:type", String)
], PublicPlaceWaterSupplyDto.prototype, "storageCapacity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.MainWaterSource, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.MainWaterSource),
    __metadata("design:type", String)
], PublicPlaceWaterSupplyDto.prototype, "mainWaterSource", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WaterSourceDistance }),
    (0, class_validator_1.IsEnum)(client_1.WaterSourceDistance),
    __metadata("design:type", String)
], PublicPlaceWaterSupplyDto.prototype, "distanceToSource", void 0);
class PublicPlaceSanitationDto {
    toiletType;
    slabConstructionMaterial;
    totalToilets;
    genderSeparation;
    femaleToilets;
    maleToilets;
    disabilityAccess;
    hasToiletFullInLast2Years;
    excretaManagement;
}
exports.PublicPlaceSanitationDto = PublicPlaceSanitationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ToiletFacilityType }),
    (0, class_validator_1.IsEnum)(client_1.ToiletFacilityType),
    __metadata("design:type", String)
], PublicPlaceSanitationDto.prototype, "toiletType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.FacilitySlabConstructionMaterial }),
    (0, class_validator_1.IsEnum)(client_1.FacilitySlabConstructionMaterial),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], PublicPlaceSanitationDto.prototype, "slabConstructionMaterial", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], PublicPlaceSanitationDto.prototype, "totalToilets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PublicPlaceSanitationDto.prototype, "genderSeparation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], PublicPlaceSanitationDto.prototype, "femaleToilets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], PublicPlaceSanitationDto.prototype, "maleToilets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PublicPlaceSanitationDto.prototype, "disabilityAccess", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PublicPlaceSanitationDto.prototype, "hasToiletFullInLast2Years", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ExcretaManagement, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.ExcretaManagement),
    __metadata("design:type", String)
], PublicPlaceSanitationDto.prototype, "excretaManagement", void 0);
class PublicPlaceHygieneDto {
    handwashingFacility;
    facilityType;
    handwashingMaterials;
    handWashingfacilityNearToilet;
    toiletHandWashingFacilityType;
    toiletHandWashingMaterials;
}
exports.PublicPlaceHygieneDto = PublicPlaceHygieneDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PublicPlaceHygieneDto.prototype, "handwashingFacility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.HandWashingFacilityType, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.HandWashingFacilityType),
    __metadata("design:type", String)
], PublicPlaceHygieneDto.prototype, "facilityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.PublicPlaceHandWashingMaterial, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.PublicPlaceHandWashingMaterial),
    __metadata("design:type", String)
], PublicPlaceHygieneDto.prototype, "handwashingMaterials", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PublicPlaceHygieneDto.prototype, "handWashingfacilityNearToilet", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.HandWashingFacilityType, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.HandWashingFacilityType),
    __metadata("design:type", String)
], PublicPlaceHygieneDto.prototype, "toiletHandWashingFacilityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.HandWashingMaterial, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.HandWashingMaterial),
    __metadata("design:type", String)
], PublicPlaceHygieneDto.prototype, "toiletHandWashingMaterials", void 0);
class PublicPlaceSolidWasteManagementDto {
    wasteSeparation;
    wasteManagement;
    treatmentType;
    collectionFrequency;
    collectionCost;
}
exports.PublicPlaceSolidWasteManagementDto = PublicPlaceSolidWasteManagementDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PublicPlaceSolidWasteManagementDto.prototype, "wasteSeparation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteManagementAfterSeparation, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.WasteManagementAfterSeparation),
    __metadata("design:type", String)
], PublicPlaceSolidWasteManagementDto.prototype, "wasteManagement", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteTreatmentType, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.WasteTreatmentType),
    __metadata("design:type", String)
], PublicPlaceSolidWasteManagementDto.prototype, "treatmentType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteCollectionFrequency, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.WasteCollectionFrequency),
    __metadata("design:type", String)
], PublicPlaceSolidWasteManagementDto.prototype, "collectionFrequency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], PublicPlaceSolidWasteManagementDto.prototype, "collectionCost", void 0);
class PublicPlaceLiquidWasteManagementDto {
    liquidWasteManagement;
}
exports.PublicPlaceLiquidWasteManagementDto = PublicPlaceLiquidWasteManagementDto;
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteWaterManagement }),
    (0, class_validator_1.IsEnum)(client_1.WasteWaterManagement),
    __metadata("design:type", String)
], PublicPlaceLiquidWasteManagementDto.prototype, "liquidWasteManagement", void 0);
class CreatePublicPlaceSubmissionDto extends base_submission_dto_1.BaseCreateSubmissionDto {
    generalInfo;
    waterSupply;
    sanitation;
    hygiene;
    solidWaste;
    liquidWaste;
}
exports.CreatePublicPlaceSubmissionDto = CreatePublicPlaceSubmissionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceGeneralInfoDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => PublicPlaceGeneralInfoDto),
    __metadata("design:type", PublicPlaceGeneralInfoDto)
], CreatePublicPlaceSubmissionDto.prototype, "generalInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceWaterSupplyDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => PublicPlaceWaterSupplyDto),
    __metadata("design:type", PublicPlaceWaterSupplyDto)
], CreatePublicPlaceSubmissionDto.prototype, "waterSupply", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceSanitationDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => PublicPlaceSanitationDto),
    __metadata("design:type", PublicPlaceSanitationDto)
], CreatePublicPlaceSubmissionDto.prototype, "sanitation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceHygieneDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => PublicPlaceHygieneDto),
    __metadata("design:type", PublicPlaceHygieneDto)
], CreatePublicPlaceSubmissionDto.prototype, "hygiene", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceSolidWasteManagementDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => PublicPlaceSolidWasteManagementDto),
    __metadata("design:type", PublicPlaceSolidWasteManagementDto)
], CreatePublicPlaceSubmissionDto.prototype, "solidWaste", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceLiquidWasteManagementDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => PublicPlaceLiquidWasteManagementDto),
    __metadata("design:type", PublicPlaceLiquidWasteManagementDto)
], CreatePublicPlaceSubmissionDto.prototype, "liquidWaste", void 0);
class PublicPlaceSubmissionResponseDto extends base_submission_dto_1.BaseSubmissionResponseDto {
    generalInfo;
    waterSupply;
    sanitation;
    hygiene;
    solidWaste;
    liquidWaste;
}
exports.PublicPlaceSubmissionResponseDto = PublicPlaceSubmissionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceGeneralInfoDto }),
    __metadata("design:type", PublicPlaceGeneralInfoDto)
], PublicPlaceSubmissionResponseDto.prototype, "generalInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceWaterSupplyDto }),
    __metadata("design:type", PublicPlaceWaterSupplyDto)
], PublicPlaceSubmissionResponseDto.prototype, "waterSupply", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceSanitationDto }),
    __metadata("design:type", PublicPlaceSanitationDto)
], PublicPlaceSubmissionResponseDto.prototype, "sanitation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceHygieneDto }),
    __metadata("design:type", PublicPlaceHygieneDto)
], PublicPlaceSubmissionResponseDto.prototype, "hygiene", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceSolidWasteManagementDto }),
    __metadata("design:type", PublicPlaceSolidWasteManagementDto)
], PublicPlaceSubmissionResponseDto.prototype, "solidWaste", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceLiquidWasteManagementDto }),
    __metadata("design:type", PublicPlaceLiquidWasteManagementDto)
], PublicPlaceSubmissionResponseDto.prototype, "liquidWaste", void 0);
//# sourceMappingURL=public-place.dto.js.map