export type Province = {
  id: number;
  code: number;
  name: string;
  districtCount: number;
  createdAt: string;
  updatedAt: string;
};

export type ProvincesListResponse = {
  provinces: Province[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};

export type ProvinceSummary = {
  id: number;
  name: string;
  code: number;
};

export type District = {
  id: number;
  code: number;
  name: string;
  province: ProvinceSummary;
  sectorCount: number;
  createdAt: string;
  updatedAt: string;
};

export type DistrictsListResponse = {
  districts: District[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};

export type DistrictSummary = {
  id: number;
  name: string;
  code: number;
  province: ProvinceSummary;
};

export type Sector = {
  id: number;
  code: number;
  name: string;
  district: DistrictSummary;
  cellCount: number;
  createdAt: string;
  updatedAt: string;
};

export type SectorsListResponse = {
  sectors: Sector[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};

export type SectorSummary = {
  id: number;
  name: string;
  code: number;
  district: DistrictSummary;
};

export type Cell = {
  id: number;
  code: number;
  name: string;
  sector: SectorSummary;
  villageCount: number;
  createdAt: string;
  updatedAt: string;
};

export type CellsListResponse = {
  cells: Cell[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};

export type CellSummary = {
  id: number;
  name: string;
  code: number;
  sector: SectorSummary;
};

export type Village = {
  id: number;
  code: number;
  name: string;
  cell: CellSummary;
  createdAt: string;
  updatedAt: string;
};

export type VillagesListResponse = {
  villages: Village[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};