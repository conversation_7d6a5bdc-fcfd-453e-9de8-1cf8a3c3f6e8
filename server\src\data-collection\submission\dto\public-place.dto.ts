import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, IsString, IsEnum, IsBoolean, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { PublicPlaceCategory, PublicPlaceOpeningDays, WaterAvailability, WaterAvailabilityFrequency, CleanWaterStorageCapacity, MainWaterSource, WaterSourceDistance, ToiletFacilityType, FacilitySlabConstructionMaterial, ExcretaManagement, HandWashingFacilityType, HandWashingMaterial, PublicPlaceHandWashingMaterial, WasteManagementAfterSeparation, WasteTreatmentType, WasteCollectionFrequency, WasteWaterManagement } from '@prisma/client';
import { BaseCreateSubmissionDto, BaseSubmissionResponseDto } from './base-submission.dto';

export class PublicPlaceGeneralInfoDto {
  @ApiProperty({ enum: PublicPlaceCategory })
  @IsEnum(PublicPlaceCategory)
  category: PublicPlaceCategory;

  @ApiProperty({ enum: PublicPlaceOpeningDays })
  @IsEnum(PublicPlaceOpeningDays)
  openingDays: PublicPlaceOpeningDays;
}

export class PublicPlaceWaterSupplyDto {
  @ApiProperty()
  @IsBoolean()
  connectedToPipeline: boolean;

  @ApiProperty({ enum: WaterAvailability })
  @IsEnum(WaterAvailability)
  waterAvailability: WaterAvailability;

  @ApiProperty({ enum: WaterAvailabilityFrequency, required: false })
  @IsOptional()
  @IsEnum(WaterAvailabilityFrequency)
  availableDays?: WaterAvailabilityFrequency;

  @ApiProperty({ enum: CleanWaterStorageCapacity })
  @IsEnum(CleanWaterStorageCapacity)
  storageCapacity: CleanWaterStorageCapacity;

  @ApiProperty({ enum: MainWaterSource, required: false })
  @IsOptional()
  @IsEnum(MainWaterSource)
  mainWaterSource?: MainWaterSource;

  @ApiProperty({ enum: WaterSourceDistance })
  @IsEnum(WaterSourceDistance)
  distanceToSource: WaterSourceDistance;
}

export class PublicPlaceSanitationDto {
  @ApiProperty({ enum: ToiletFacilityType })
  @IsEnum(ToiletFacilityType)
  toiletType: ToiletFacilityType;

  @ApiProperty({ enum: FacilitySlabConstructionMaterial })
  @IsEnum(FacilitySlabConstructionMaterial)
  @IsOptional()
  slabConstructionMaterial?: FacilitySlabConstructionMaterial;

  @ApiProperty()
  @IsInt()
  totalToilets: number;

  @ApiProperty()
  @IsBoolean()
  genderSeparation: boolean;

  @ApiProperty()
  @IsInt()
  femaleToilets: number;

  @ApiProperty()
  @IsInt()
  maleToilets: number;

  @ApiProperty()
  @IsBoolean()
  disabilityAccess: boolean;

  @ApiProperty()
  @IsBoolean()
  hasToiletFullInLast2Years: boolean;

  @ApiProperty({ enum: ExcretaManagement, required: false })
  @IsOptional()
  @IsEnum(ExcretaManagement)
  excretaManagement?: ExcretaManagement;
}

export class PublicPlaceHygieneDto {
  @ApiProperty()
  @IsBoolean()
  handwashingFacility: boolean;

  @ApiProperty({ enum: HandWashingFacilityType, required: false })
  @IsOptional()
  @IsEnum(HandWashingFacilityType)
  facilityType?: HandWashingFacilityType;

  @ApiProperty({ enum: PublicPlaceHandWashingMaterial, required: false })
  @IsOptional()
  @IsEnum(PublicPlaceHandWashingMaterial)
  handwashingMaterials?: PublicPlaceHandWashingMaterial;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  handWashingfacilityNearToilet?: boolean;

  @ApiProperty({ enum: HandWashingFacilityType, required: false })
  @IsOptional()
  @IsEnum(HandWashingFacilityType)
  toiletHandWashingFacilityType?: HandWashingFacilityType;

  @ApiProperty({ enum: HandWashingMaterial, required: false })
  @IsOptional()
  @IsEnum(HandWashingMaterial)
  toiletHandWashingMaterials?: HandWashingMaterial;
}

export class PublicPlaceSolidWasteManagementDto {
  @ApiProperty()
  @IsBoolean()
  wasteSeparation: boolean;

  @ApiProperty({ enum: WasteManagementAfterSeparation, required: false })
  @IsOptional()
  @IsEnum(WasteManagementAfterSeparation)
  wasteManagement?: WasteManagementAfterSeparation;

  @ApiProperty({ enum: WasteTreatmentType, required: false })
  @IsOptional()
  @IsEnum(WasteTreatmentType)
  treatmentType?: WasteTreatmentType;

  @ApiProperty({ enum: WasteCollectionFrequency, required: false })
  @IsOptional()
  @IsEnum(WasteCollectionFrequency)
  collectionFrequency?: WasteCollectionFrequency;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  collectionCost?: number;
}

export class PublicPlaceLiquidWasteManagementDto {
  @ApiProperty({ enum: WasteWaterManagement })
  @IsEnum(WasteWaterManagement)
  liquidWasteManagement: WasteWaterManagement;
}

export class CreatePublicPlaceSubmissionDto extends BaseCreateSubmissionDto {
  @ApiProperty({ type: PublicPlaceGeneralInfoDto })
  @ValidateNested()
  @Type(() => PublicPlaceGeneralInfoDto)
  generalInfo: PublicPlaceGeneralInfoDto;

  @ApiProperty({ type: PublicPlaceWaterSupplyDto })
  @ValidateNested()
  @Type(() => PublicPlaceWaterSupplyDto)
  waterSupply: PublicPlaceWaterSupplyDto;

  @ApiProperty({ type: PublicPlaceSanitationDto })
  @ValidateNested()
  @Type(() => PublicPlaceSanitationDto)
  sanitation: PublicPlaceSanitationDto;

  @ApiProperty({ type: PublicPlaceHygieneDto })
  @ValidateNested()
  @Type(() => PublicPlaceHygieneDto)
  hygiene: PublicPlaceHygieneDto;

  @ApiProperty({ type: PublicPlaceSolidWasteManagementDto })
  @ValidateNested()
  @Type(() => PublicPlaceSolidWasteManagementDto)
  solidWaste: PublicPlaceSolidWasteManagementDto;

  @ApiProperty({ type: PublicPlaceLiquidWasteManagementDto })
  @ValidateNested()
  @Type(() => PublicPlaceLiquidWasteManagementDto)
  liquidWaste: PublicPlaceLiquidWasteManagementDto;
}

export class PublicPlaceSubmissionResponseDto extends BaseSubmissionResponseDto {
  @ApiProperty({ type: PublicPlaceGeneralInfoDto })
  generalInfo: PublicPlaceGeneralInfoDto;

  @ApiProperty({ type: PublicPlaceWaterSupplyDto })
  waterSupply: PublicPlaceWaterSupplyDto;

  @ApiProperty({ type: PublicPlaceSanitationDto })
  sanitation: PublicPlaceSanitationDto;

  @ApiProperty({ type: PublicPlaceHygieneDto })
  hygiene: PublicPlaceHygieneDto;

  @ApiProperty({ type: PublicPlaceSolidWasteManagementDto })
  solidWaste: PublicPlaceSolidWasteManagementDto;

  @ApiProperty({ type: PublicPlaceLiquidWasteManagementDto })
  liquidWaste: PublicPlaceLiquidWasteManagementDto;
}
