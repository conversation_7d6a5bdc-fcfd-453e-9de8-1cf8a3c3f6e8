import AppText from "@/components/ui/Text";
import { Ionicons } from "@expo/vector-icons";
import { Stack, useNavigation } from "expo-router";
import React from "react";
import { Pressable, View } from "react-native";
import tw from "twrnc";

export default function FacilityLayout() {
    const navigation = useNavigation()

    return (
        <Stack screenOptions={{
            headerShown: true,
            header: () => (
                <Pressable onPress={() => navigation.goBack()}>
                    <View style={tw`flex-row items-center p-4 pt-8 gap-2 bg-white`}>
                        <Ionicons name="chevron-back" size={24} color="gray" />
                        <AppText weight="semibold" style={tw`text-gray-500 text-lg`}>Back</AppText>
                    </View>
                </Pressable>
            )
        }}>
        </Stack>
    )
}