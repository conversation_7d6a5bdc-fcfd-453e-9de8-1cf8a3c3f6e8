export enum SettlementType {
    RUR<PERSON>,
    URBAN
}

export enum PublicPlaceType {
    MARKET = "Market",
    PUBLIC_TRANSPORT_STOP = "Public transport stop",
    OTHER = "Other",
}

export enum EducationLevel {
    NONE = "None",
    PRIMARY = "primary",
    O_LEVEL = "O Level",
    A_LEVEL = "A Level",
    TERTIARY = "Tertiary",
}

export enum Gender {
    MALE,
    FEMALE,
}

export enum AgeGroup {
    BELOW_18 = "Below 18",
    BETWEEN_18_AND_30 = "Between 18 and 30",
    BETWEEN_31_AND_50 = "Between 31 and 50",
    ABOVE_50 = "Above 50",
}

export enum MainWaterSource {
    WATER_TAP_WITHIN_HH = "Water tap within the HH/Facility",
    PUBLIC_WATER_TAP_KIOSK = "Public water tap/kiosk",
    PROTECTED_IMPROVED_SPRINGS = "Protected/improved springs",
    BOREHOLE = "Borehole",
    RAIN_WATER = "Rain water",
    UNIMPROVED_SPRINGS = "Unimproved springs",
    SURFACE_WATER = "Surface water (Lake, rivers, etc.)",
    FROM_NEIGHBOR = "From neighbor",
}

export enum WaterAvailability {
    ALWAYS_AVAILABLE = "HH members can find water whenever needed",
    SOMETIMES_UNAVAILABLE = "Sometimes HH members cannot find water to use",
}

export enum WaterAvailabilityFrequency {
    ONCE_PER_WEEK = "Once per week",
    TWICE_A_WEEK = "Twice a week",
    THREE_DAYS_A_WEEK = "3 days a week",
    ONCE_PER_MONTH = "Once a month",
    MORE_THAN_ONE_MONTH = "Once in a period more than a month",
    NONE = "none of the above",
}

export enum CleanWaterStorageCapacity {
    LITERS_1_TO_500 = "1 - 500 Liters",
    LITERS_501_TO_2M3 = "501 liters - 2 m3",
    M3_2_TO_5 = "2m3 - 5m3",
    M3_5_TO_10 = "5m3 - 10m3",
    ABOVE_10M3 = "Above 10m3",
}

export enum WaterSourceDistance {
    METERS_1_TO_200 = "1m - 200m",
    METERS_201_TO_500 = "201m - 500m",
    METERS_501_TO_1KM = "501m - 1km",
    ABOVE_1KM = "Above 1km",
}

export enum WaterFetchingTime {
    MINUTES_1_TO_30 = "1min - 30min",
    MINUTES_31_TO_60 = "31 min - 1hour",
    ABOVE_ONE_HOUR = "Above one hour",
}

export enum UnimprovedWaterReason {
    IMPROVED_SOURCE_FAR = "Improved water source is far",
    NEARBY_SOURCE_NOT_FUNCTIONING = "The nearby public water source does not function",
    OTHER = "Other",
}

export enum PwsNonFunctionalityReason {
    NEVER_FUNCTIONED = "PWS did never function at all",
    WATER_VANISHED = "Water vanished from the PWS",
    DAMAGED = "Damaged",
}

export enum PwsNonFunctionalityDuration {
    ONE_WEEK_TO_ONE_MONTH = "Between 1 week and a month",
    ONE_TO_THREE_MONTHS = "1 month - 3 months",
    ABOVE_THREE_MONTHS = "Above 3 months",
}

export enum ToiletFacilityType {
    FLUSH_TO_SEWER_SYSTEM = "Flush to piped sewer system",
    FLUSH_TO_SEPTIC_TANK = "Flush to septic tank",
    FLUSH_TO_PIT = "Flush to pit",
    VENTILATED_IMPROVED_PIT = "Ventilated improved pit latrine",
    PIT_WITH_SLAB = "Pit latrine with slab",
    PIT_WITHOUT_SLAB = "Pit latrine without slab",
    COMPOSTING_TOILET = "Composting toilet (Ecosan,",
    URINE_DIVERSION_DRY_TOILET = "Urine diversion dry toilet",
    NO_FACILITY = "No facility/bush/field",
    OTHER = "Other (specify,:",
}

export enum ToiletFacilityCompleteness {
    ROOF_AND_CLOSING_DOOR = "Has roof and closing door",
    ROOF_NO_CLOSING_DOOR = "Has roof but without closing door",
    CLOSING_DOOR_NO_ROOF = "Has closing door, but no roof",
    INCOMPLETE_WALLS = "Has not complete walls",
    NO_WALLS = "Has no walls",
}

export enum FacilitySlabConstructionMaterial {
    DRY_TREES_OR_UNORDERED_WOOD = "Dry trees or unordered wood ",
    WOOD_WELL_ORDERED = "Wood well ordered (without holes other than the drop hole,",
    SOLID_LAND_WITH_HOLES = "Solid land with holes (besides the drop hole,",
    SOLID_LAND_NO_HOLES = "Solid land floor without holes",
    CONCRETE_WITH_CEMENT_FLOOR = "Concrete with cement floor",
    CONCRETE_WITH_TILES_FLOOR = "Concrete with tiles floor",
}

export enum ExcretaManagement {
    EMPTIED_BY_SPECIALIZED_COMPANY = "Excreta was emptied and transported by a specialized company",
    FILLED_WITH_LAND_AND_LEFT = "The toilet was filled with land, ashes, etc. and was left",
    LEFT_WITHOUT_FILLING = "The toilet was left without filling it with land, ashes, etc.",
    FILLED_AND_COMPOSTED = "The toilet was filled with land, ashes, etc. then composted",
    DIRECTED_TO_ANOTHER_PIT = "The toilet was directed (discharged, into another pit",
}

export enum HandWashingFacilityType {
    MOBILE = "Mobile facility",
    FIXED = "Fixed facility",
}

export enum HandWashingMaterial {
    WATER_SOAP_AND_SANITIZER = "Water, soap and sanitizer",
    WATER_AND_SOAP = "Water and soap",
    SANITIZER = "Sanitizer",
    ONLY_WATER = "Only water",
    NONE = "None of them",
}

export enum MarketHandWashingMaterial {
    WATER_SOAP_SANITIZER = "Water, soap and sanitizer",
    WATER_AND_SOAP = "Water and soap",
    SANITIZER_ONLY = "Sanitizer",
    ONLY_WATER = "Only water",
    NEITHER_WATER_NOR_SOAP = "Neither water nor soap",
}

export enum WasteWaterManagement {
    DEDICATED_PIT_COVERED = "Channeled to a dedicated pit which is covered",
    DEDICATED_PIT_NOT_COVERED = "Channeled to a dedicated pit which is not covered",
    SEPTIC_TANK = "Channeled to septic tank",
    SEWERS = "Channeled to sewers",
    OPEN_TRENCHES_OR_DRAINS = "Flows in open trenches/drains",
    FLOWS_ON_GROUND = "Flows on ground",
    RECYCLED = "They get recycled",
}

export enum WasteManagementAfterSeparation {
    TRANSPORTED_BY_COMPANIES = "Transported by waste collection companies",
    ON_SITE_TREATMENT = "on site treatment",
    BOTH = "Both (a, and (b,",
}

export enum WasteTreatmentType {
    COMPOSTING = "Composting (fertilizer,",
    BURIED_UNDER_GROUND = "Buried under ground.",
    BURNT = "Burnt.",
    SORT_AND_SELL = "Sort materials and sell to specialized companies",
    AUTOCLAVING = "Autoclaving",
    INCINERATION = "Incineration",
    MICROWAVE_TREATMENT = "Microwave treatment",
    CHEMICAL_DISINFECTION = "Chemical disinfection",
}

export enum WasteCollectionFrequency {
    DAILY = "Daily",
    TWO_TIMES_WEEK = "2 times a week",
    THREE_TIMES_WEEK = "3 times a week",
    WEEKLY = "Weekly",
    OTHER = "Other: (please specify,",
}

export enum SchoolType {
    ECD = "Early Childhood Development (ECD,",
    PRE_PRIMARY = "Pre-primary",
    PRIMARY = "Primary",
    SECONDARY = "Secondary",
    COMBINED_PRE_PRIMARY_PRIMARY = "Combined pre-primary & primary",
    COMBINED_PRIMARY_SECONDARY = "Combined primary & secondary",
    COMBINED_ALL_LEVELS = "Combined pre-primary, primary & secondary",
    HIGHER_EDUCATION = "higher education",
}

export enum SchoolManagement {
    PUBLIC = "Public School",
    PRIVATE = "Private school",
    FAITH_BASED = "Faith-based",
    FAITH_BASED_CO_FINANCED = "Faith-based co-financed by Government",
    PRIVATE_CO_FINANCED = "Private co-financed by Government",
}

export enum SchoolTypeDayBoarding {
    DAY_SCHOOL = "Day school",
    BOARDING_SCHOOL = "Boarding school",
    DAY_AND_BOARDING = "Day and boarding school",
}

export enum HealthFacilityType {
    REFERRAL_HOSPITAL = "Referral hospital",
    PROVINCIAL_HOSPITAL = "Provincial hospital",
    DISTRICT_HOSPITAL = "District hospital",
    HEALTH_CENTER = "Health Center",
    HEALTH_POST = "health post",
    POLYCLINIC = "Polyclinic",
    CLINIC = "Clinic",
    DISPENSARY = "Dispensary",
    OTHERS = "Others",
}

export enum HealthFacilityManagement {
    PUBLIC = "Public",
    PRIVATE = "Private",
    FAITH_BASED = "Faith-based",
    PRIVATE_SUPPORTED_BY_GOVT = "Private HF supported by Govt",
    FAITH_BASED_SUPPORTED_BY_GOVT = "Faith-based HF supported by Govt",
}

export enum DailyPatientVolume {
    ONE_TO_FIFTY = "1-50",
    FIFTY_ONE_TO_ONE_HUNDRED = "51-100",
    ONE_HUNDRED_TO_THREE_HUNDRED = "100-300",
    ABOVE_THREE_HUNDRED = "above 300",
}

export enum MarketCategory {
    FULLY_CONSTRUCTED = "fully constructed",
    PARTIALLY_CONSTRUCTED = "partially constructed",
    NOT_CONSTRUCTED = "Not constructed",
}

export enum MarketOpeningDays {
    ONCE_A_WEEK = "Once a week",
    TWICE_A_WEEK = "Twice a week",
    THREE_DAYS_A_WEEK = "three days a week",
    EVERY_DAY = "Every day",
}

export enum ServiceProviderType {
    MUNICIPAL_PUBLIC_OPERATOR = "municipal/public operator",
    PRIVATE_SERVICE_CONTRACTOR = "Private service contractor",
    NGO_CSOS = "NGO/CSOs",
    OTHER = "Other",
}

export enum ClientType {
    HOUSEHOLDS = "Households",
    MARKETS = "Markets",
    SCHOOLS = "Schools",
    HEALTH_FACILITIES = "Health facilities",
    INDUSTRIES_FACTORIES = "Industries/Factories",
    BUSINESSES_RESTAURANTS_HOTELS_VENDORS = "Businesses/restaurants/ Hotels/(street, vendors",
    ADMINISTRATIVE_BUILDINGS = "Administrative buildings",
    OTHERS = "Others",
}

export enum WasteMaterial {
    BIODEGRADABLE = "Biodegradable",
    NON_BIODEGRADABLE = "Non-biodegradable",
}

export enum WasteDestination {
    DUMPSITE_LANDFILL = "Dumpsite/landfill",
    MATERIAL_RECOVERY_FACILITY = "Material recovery/recycling facility",
    OTHERS = "Others",
}

export enum RecordingMethod {
    PAPER_LOGBOOK = "paper logbook",
    OTHER = "others",
}

export enum OperationType {
    END_OF_CHAIN_RECYCLER = "End-of-chain recycler/recoverer facility ",
    APEX_TRADER = "Apex trader (Buying and Selling recycled commodities,",
    INTERMEDIATE_TRADER = "Intermediate trader person or company",
}

export enum CompactionFrequency {
    DAILY = "Daily",
    WEEKLY = "Weekly",
    MONTHLY = "Monthly",
    ANNUALLY = "Annually",
    EMERGENCY_ONLY = "Emergency only",
    NEVER = "Never",
}

export enum TruckFrequency {
    DAILY = "Daily",
    WEEKLY = "Weekly",
    OTHER = "Other",
}