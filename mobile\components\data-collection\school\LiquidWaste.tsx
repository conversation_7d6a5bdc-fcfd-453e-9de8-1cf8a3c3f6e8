import AppText from '@/components/ui/Text';
import * as enums from '@/types/enums';
import { getEnumOptions } from '@/utils/enum';
import { Picker } from '@react-native-picker/picker';
import React from 'react';
import { Controller } from 'react-hook-form';
import { View } from 'react-native';
import tw from 'twrnc';

const LiquidWasteSection = ({ control, errors }: any) => (
    <>
        <View style={tw`mb-4`}>
            <AppText style={tw`mb-2`}>How is waste water from the school managed?</AppText>
            <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                <Controller
                    control={control}
                    name="liquidWaste.liquidWasteManagement"
                    render={({ field: { onChange, value } }) => (
                        <Picker selectedValue={value} onValueChange={onChange}>
                            <Picker.Item label="Select Management" value="" />
                            {getEnumOptions(enums.WasteWaterManagement).map(opt => (
                                <Picker.Item key={opt.value} label={opt.label} value={opt.value as string} />
                            ))}
                        </Picker>
                    )}
                />
            </View>
            {errors.liquidWaste?.liquidWasteManagement && <AppText style={tw`text-red-500`}>{errors.liquidWaste?.liquidWasteManagement.message}</AppText>}
        </View>
    </>
);

export default LiquidWasteSection;