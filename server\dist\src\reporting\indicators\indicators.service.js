"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndicatorsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const client_1 = require("@prisma/client");
let IndicatorsService = class IndicatorsService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getBasicIndicators(data) {
        try {
            const indicatorRequestProps = {
                level: data.level,
                levelId: data.levelId,
                startDate: new Date(data.startDate),
                endDate: new Date(data.endDate),
            };
            const indicators = [];
            switch (data.facilityType) {
                case "HOUSEHOLD":
                    indicators.push({
                        title: "Household Water Supply",
                        items: await this.householdWaterSuppy(indicatorRequestProps),
                    });
                    indicators.push({
                        title: "Household Sanitation",
                        items: await this.houseHoldSanitation(indicatorRequestProps),
                    });
                    indicators.push({
                        title: "Household Hygiene",
                        items: await this.houseHoldHygiene(indicatorRequestProps),
                    });
                    break;
                case "SCHOOL":
                    indicators.push({
                        title: "School Water Supply",
                        items: await this.schoolWaterSupply(indicatorRequestProps),
                    });
                    indicators.push({
                        title: "School Sanitation",
                        items: await this.schoolSanitation(indicatorRequestProps),
                    });
                    indicators.push({
                        title: "School Hygiene",
                        items: await this.schoolHygiene(indicatorRequestProps),
                    });
                    break;
                case "HEALTH_FACILITY":
                    indicators.push({
                        title: "Health Facility Water Supply",
                        items: await this.healthFacilityWaterSupply(indicatorRequestProps),
                    });
                    indicators.push({
                        title: "Health Facility Sanitation",
                        items: await this.healthFacilitySanitation(indicatorRequestProps),
                    });
                    indicators.push({
                        title: "Health Facility Hygiene",
                        items: await this.healthFacilityHygiene(indicatorRequestProps),
                    });
                    break;
                case "PUBLIC_PLACE":
                    indicators.push({
                        title: "Public Place Water Supply",
                        items: await this.publicPlaceWaterSupply(indicatorRequestProps),
                    });
                    indicators.push({
                        title: "Public Place Sanitation",
                        items: await this.publicPlaceSanitation(indicatorRequestProps),
                    });
                    indicators.push({
                        title: "Public Place Hygiene",
                        items: await this.publicPlaceHygiene(indicatorRequestProps),
                    });
                    break;
                case "WASTE_COLLECTION_COMPANY":
                    break;
                case "WASTE_RECOVERY_COMPANY":
                    break;
                case "WASTE_DISPOSAL_COMPANY":
                    break;
                default:
                    break;
            }
            return {
                level: data.level,
                levelId: data.levelId,
                startDate: data.startDate,
                endDate: data.endDate,
                facilityType: data.facilityType,
                indicators,
            };
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException('Failed to get indicators');
        }
    }
    async householdWaterSuppy(data) {
        let houseHolds = [];
        switch (data.level) {
            case "COUNTRY":
                houseHolds = await this.prisma.houseHold.findMany({});
                break;
            case "PROVINCE":
                const province = await this.prisma.province.findUnique({
                    where: { id: Number(data.levelId) },
                });
                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        district: {
                                            provinceId: province.id,
                                        },
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "DISTRICT":
                const district = await this.prisma.district.findUnique({
                    where: { id: Number(data.levelId) },
                });
                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        districtId: district.id,
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "SECTOR":
                const sector = await this.prisma.sector.findUnique({
                    where: { id: Number(data.levelId) },
                });
                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sectorId: sector.id,
                                },
                            },
                        },
                    },
                });
                break;
            case "CELL":
                const cell = await this.prisma.cell.findUnique({
                    where: { id: Number(data.levelId) },
                });
                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cellId: cell.id,
                            },
                        },
                    },
                });
                break;
            case "VILLAGE":
                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            villageId: Number(data.levelId),
                        },
                    },
                });
                break;
            default:
                break;
        }
        if (houseHolds.length === 0) {
            return [];
        }
        const submissions = await this.prisma.submission.findMany({
            where: {
                houseHoldId: {
                    in: houseHolds.map((houseHold) => houseHold.id),
                },
                submittedAt: {
                    gte: new Date(data.startDate),
                    lte: new Date(data.endDate),
                },
            },
            include: {
                HouseHoldGeneralInfo: true,
                HouseHoldWaterSupply: true,
                household: {
                    include: {
                        location: true,
                    },
                },
            },
        });
        if (submissions.length === 0) {
            return [];
        }
        const totalHouseholds = submissions.length;
        const safelyManagedWaterSupply = submissions.filter((submission) => submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.WATER_TAP_WITHIN_HH).length;
        const percentageSafelyManagedWaterSupply = (safelyManagedWaterSupply / totalHouseholds) * 100;
        const basicWaterServices = submissions.filter((submission) => submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.WATER_TAP_WITHIN_HH ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.PUBLIC_WATER_TAP_KIOSK ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.PROTECTED_IMPROVED_SPRINGS ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.BOREHOLE ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.FROM_NEIGHBOR).length;
        const percentageBasicWaterServices = (basicWaterServices / totalHouseholds) * 100;
        const limitedWaterServices = submissions.filter((submission) => (submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.PUBLIC_WATER_TAP_KIOSK ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.PROTECTED_IMPROVED_SPRINGS ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.BOREHOLE ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.FROM_NEIGHBOR) &&
            (submission.HouseHoldWaterSupply[0].timeToFetch === client_1.WaterFetchingTime.MINUTES_31_TO_60 ||
                submission.HouseHoldWaterSupply[0].timeToFetch === client_1.WaterFetchingTime.ABOVE_ONE_HOUR)).length;
        const percentageLimitedWaterServices = (limitedWaterServices / totalHouseholds) * 100;
        const improvedWaterSources = submissions.filter((submission) => submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.WATER_TAP_WITHIN_HH ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.PUBLIC_WATER_TAP_KIOSK ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.PROTECTED_IMPROVED_SPRINGS ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.BOREHOLE ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.FROM_NEIGHBOR).length;
        const percentageImprovedWaterSources = (improvedWaterSources / totalHouseholds) * 100;
        const unimprovedWaterSources = submissions.filter((submission) => submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.UNIMPROVED_SPRINGS ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.RAIN_WATER).length;
        const percentageUnimprovedWaterSources = (unimprovedWaterSources / totalHouseholds) * 100;
        const noWaterServices = submissions.filter((submission) => submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.SURFACE_WATER).length;
        const percentageNoWaterServices = (noWaterServices / totalHouseholds) * 100;
        const ruralHouseholds = submissions.filter((submission) => submission.household.location.settlementType === client_1.SettlementType.RURAL && (submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.WATER_TAP_WITHIN_HH ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.PUBLIC_WATER_TAP_KIOSK ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.PROTECTED_IMPROVED_SPRINGS ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.BOREHOLE ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.FROM_NEIGHBOR) && (submission.HouseHoldWaterSupply[0].timeToFetch === client_1.WaterFetchingTime.MINUTES_31_TO_60 ||
            submission.HouseHoldWaterSupply[0].timeToFetch === client_1.WaterFetchingTime.ABOVE_ONE_HOUR)).length;
        const percentageRuralHouseholds = (ruralHouseholds / totalHouseholds) * 100;
        const urbanHouseholds = submissions.filter((submission) => submission.household.location.settlementType === client_1.SettlementType.URBAN && (submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.WATER_TAP_WITHIN_HH ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.PUBLIC_WATER_TAP_KIOSK ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.PROTECTED_IMPROVED_SPRINGS ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.BOREHOLE ||
            submission.HouseHoldWaterSupply[0].waterSource === client_1.MainWaterSource.FROM_NEIGHBOR) && (submission.HouseHoldWaterSupply[0].timeToFetch === client_1.WaterFetchingTime.MINUTES_31_TO_60 ||
            submission.HouseHoldWaterSupply[0].timeToFetch === client_1.WaterFetchingTime.ABOVE_ONE_HOUR)).length;
        const percentageUrbanHouseholds = (urbanHouseholds / totalHouseholds) * 100;
        return [
            {
                name: "Households with safely managed water supply",
                value: safelyManagedWaterSupply,
                percentage: percentageSafelyManagedWaterSupply,
            },
            {
                name: "Households with basic water supply",
                value: basicWaterServices,
                percentage: percentageBasicWaterServices,
            },
            {
                name: "Households with limited water supply",
                value: limitedWaterServices,
                percentage: percentageLimitedWaterServices,
            },
            {
                name: "Households with improved water supply",
                value: improvedWaterSources,
                percentage: percentageImprovedWaterSources,
            },
            {
                name: "Households with unimproved water supply",
                value: unimprovedWaterSources,
                percentage: percentageUnimprovedWaterSources,
            },
            {
                name: "Households with no water supply",
                value: noWaterServices,
                percentage: percentageNoWaterServices,
            },
            {
                name: "Households with access to basic water services in rural areas",
                value: ruralHouseholds,
                percentage: percentageRuralHouseholds,
            },
            {
                name: "Households with access to basic water services in urban areas",
                value: urbanHouseholds,
                percentage: percentageUrbanHouseholds,
            },
        ];
    }
    async houseHoldSanitation(data) {
        let houseHolds = [];
        switch (data.level) {
            case "COUNTRY":
                houseHolds = await this.prisma.houseHold.findMany({});
                break;
            case "PROVINCE":
                const province = await this.prisma.province.findUnique({
                    where: { id: Number(data.levelId) },
                });
                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        district: {
                                            provinceId: province.id,
                                        },
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "DISTRICT":
                const district = await this.prisma.district.findUnique({
                    where: { id: Number(data.levelId) },
                });
                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        districtId: district.id,
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "SECTOR":
                const sector = await this.prisma.sector.findUnique({
                    where: { id: Number(data.levelId) },
                });
                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sectorId: sector.id,
                                },
                            },
                        },
                    },
                });
                break;
            case "CELL":
                const cell = await this.prisma.cell.findUnique({
                    where: { id: Number(data.levelId) },
                });
                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cellId: cell.id,
                            },
                        },
                    },
                });
                break;
            case "VILLAGE":
                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            villageId: Number(data.levelId),
                        },
                    },
                });
                break;
            default:
                break;
        }
        if (houseHolds.length === 0) {
            return [];
        }
        const submissions = await this.prisma.submission.findMany({
            where: {
                houseHoldId: {
                    in: houseHolds.map((houseHold) => houseHold.id),
                },
                submittedAt: {
                    gte: new Date(data.startDate),
                    lte: new Date(data.endDate),
                },
            },
            include: {
                HouseHoldGeneralInfo: true,
                HouseHoldSanitation: true,
                household: {
                    include: {
                        location: true,
                    },
                },
            },
        });
        if (submissions.length === 0) {
            return [];
        }
        const totalHouseholds = submissions.length;
        const safelyManagedSanitation = submissions.filter((submission) => submission.HouseHoldSanitation[0].toiletType === client_1.ToiletFacilityType.FLUSH_TO_SEWER_SYSTEM ||
            submission.HouseHoldSanitation[0].toiletType === client_1.ToiletFacilityType.FLUSH_TO_SEPTIC_TANK ||
            submission.HouseHoldSanitation[0].toiletType === client_1.ToiletFacilityType.FLUSH_TO_PIT ||
            submission.HouseHoldSanitation[0].toiletType === client_1.ToiletFacilityType.VENTILATED_IMPROVED_PIT ||
            submission.HouseHoldSanitation[0].toiletType === client_1.ToiletFacilityType.PIT_WITH_SLAB ||
            submission.HouseHoldSanitation[0].toiletType === client_1.ToiletFacilityType.COMPOSTING_TOILET ||
            submission.HouseHoldSanitation[0].toiletType === client_1.ToiletFacilityType.URINE_DIVERSION_DRY_TOILET).length;
        const percentageSafelyManagedSanitation = (safelyManagedSanitation / totalHouseholds) * 100;
        const limitedSanitation = submissions.filter((submission) => submission.HouseHoldSanitation[0].toiletShared).length;
        const percentageLimitedSanitation = (limitedSanitation / totalHouseholds) * 100;
        const noSanitation = submissions.filter((submission) => submission.HouseHoldSanitation[0].toiletType === client_1.ToiletFacilityType.NO_FACILITY ||
            submission.HouseHoldSanitation[0].toiletType === client_1.ToiletFacilityType.OTHER).length;
        const percentageNoSanitation = (noSanitation / totalHouseholds) * 100;
        const basicSanitation = submissions.filter((submission) => (submission.HouseHoldSanitation[0].toiletType === client_1.ToiletFacilityType.FLUSH_TO_SEWER_SYSTEM ||
            submission.HouseHoldSanitation[0].toiletType === client_1.ToiletFacilityType.FLUSH_TO_SEPTIC_TANK ||
            submission.HouseHoldSanitation[0].toiletType === client_1.ToiletFacilityType.FLUSH_TO_PIT ||
            submission.HouseHoldSanitation[0].toiletType === client_1.ToiletFacilityType.VENTILATED_IMPROVED_PIT ||
            submission.HouseHoldSanitation[0].toiletType === client_1.ToiletFacilityType.PIT_WITH_SLAB ||
            submission.HouseHoldSanitation[0].toiletType === client_1.ToiletFacilityType.COMPOSTING_TOILET ||
            submission.HouseHoldSanitation[0].toiletType === client_1.ToiletFacilityType.URINE_DIVERSION_DRY_TOILET) && !submission.HouseHoldSanitation[0].toiletShared).length;
        const percentageBasicSanitation = (basicSanitation / totalHouseholds) * 100;
        const ruralHouseholds = submissions.filter((submission) => submission.household.location.settlementType === client_1.SettlementType.RURAL && !submission.HouseHoldSanitation[0].toiletShared).length;
        const percentageRuralHouseholds = (ruralHouseholds / totalHouseholds) * 100;
        const urbanHouseholds = submissions.filter((submission) => submission.household.location.settlementType === client_1.SettlementType.URBAN && !submission.HouseHoldSanitation[0].toiletShared).length;
        const percentageUrbanHouseholds = (urbanHouseholds / totalHouseholds) * 100;
        return [
            {
                name: "Households with safely managed sanitation services",
                value: safelyManagedSanitation,
                percentage: percentageSafelyManagedSanitation,
            },
            {
                name: "Households with limited sanitation services",
                value: limitedSanitation,
                percentage: percentageLimitedSanitation,
            },
            {
                name: "Households without sanitation services",
                value: noSanitation,
                percentage: percentageNoSanitation,
            },
            {
                name: "Households with basic sanitation services",
                value: basicSanitation,
                percentage: percentageBasicSanitation,
            },
        ];
    }
    async houseHoldHygiene(data) {
        let houseHolds = [];
        switch (data.level) {
            case "COUNTRY":
                houseHolds = await this.prisma.houseHold.findMany({});
                break;
            case "PROVINCE":
                const province = await this.prisma.province.findUnique({
                    where: { id: Number(data.levelId) },
                });
                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        district: {
                                            provinceId: province.id,
                                        },
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "DISTRICT":
                const district = await this.prisma.district.findUnique({
                    where: { id: Number(data.levelId) },
                });
                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        districtId: district.id,
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "SECTOR":
                const sector = await this.prisma.sector.findUnique({
                    where: { id: Number(data.levelId) },
                });
                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sectorId: sector.id,
                                },
                            },
                        },
                    },
                });
                break;
            case "CELL":
                const cell = await this.prisma.cell.findUnique({
                    where: { id: Number(data.levelId) },
                });
                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cellId: cell.id,
                            },
                        },
                    },
                });
                break;
            case "VILLAGE":
                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            villageId: Number(data.levelId),
                        },
                    },
                });
                break;
            default:
                break;
        }
        if (houseHolds.length === 0) {
            return [];
        }
        const submissions = await this.prisma.submission.findMany({
            where: {
                houseHoldId: {
                    in: houseHolds.map((houseHold) => houseHold.id),
                },
                submittedAt: {
                    gte: new Date(data.startDate),
                    lte: new Date(data.endDate),
                },
            },
            include: {
                HouseHoldGeneralInfo: true,
                HouseHoldHygiene: true,
                household: {
                    include: {
                        location: true,
                    },
                },
            },
        });
        if (submissions.length === 0) {
            return [];
        }
        const totalHouseholds = submissions.length;
        const basicHygiene = submissions.filter((submission) => submission.HouseHoldHygiene[0].handwashingFacility && submission.HouseHoldHygiene[0].handwashingMaterials === client_1.HandWashingMaterial.WATER_AND_SOAP).length;
        const percentageBasicHygiene = (basicHygiene / totalHouseholds) * 100;
        const limitedHygiene = submissions.filter((submission) => submission.HouseHoldHygiene[0].handwashingFacility && submission.HouseHoldHygiene[0].handwashingMaterials === client_1.HandWashingMaterial.ONLY_WATER || submission.HouseHoldHygiene[0].handwashingMaterials === client_1.HandWashingMaterial.NONE).length;
        const percentageLimitedHygiene = (limitedHygiene / totalHouseholds) * 100;
        const noHygiene = submissions.filter((submission) => !submission.HouseHoldHygiene[0].handwashingFacility).length;
        const percentageNoHygiene = (noHygiene / totalHouseholds) * 100;
        return [
            {
                name: "Households with basic hygiene services",
                value: basicHygiene,
                percentage: percentageBasicHygiene,
            },
            {
                name: "Households with limited hygiene services",
                value: limitedHygiene,
                percentage: percentageLimitedHygiene,
            },
            {
                name: "Households with no hygiene services",
                value: noHygiene,
                percentage: percentageNoHygiene,
            },
        ];
    }
    async getGeoIndicatorMapping(data) {
        try {
            const startDate = new Date(data.startDate);
            const endDate = new Date(data.endDate);
            let locations = [];
            switch (data.level) {
                case "PROVINCE":
                    locations = await this.prisma.province.findMany({
                        select: { id: true, name: true }
                    });
                    break;
                case "DISTRICT":
                    locations = await this.prisma.district.findMany({
                        select: { id: true, name: true }
                    });
                    break;
                case "SECTOR":
                    locations = await this.prisma.sector.findMany({
                        select: { id: true, name: true }
                    });
                    break;
                case "CELL":
                    locations = await this.prisma.cell.findMany({
                        select: { id: true, name: true }
                    });
                    break;
                case "VILLAGE":
                    locations = await this.prisma.village.findMany({
                        select: { id: true, name: true }
                    });
                    break;
                default:
                    throw new common_1.InternalServerErrorException('Invalid administrative level');
            }
            const locationData = [];
            for (const location of locations) {
                const indicatorRequestProps = {
                    level: data.level,
                    levelId: location.id.toString(),
                    startDate,
                    endDate,
                };
                let facilities = [];
                let coordinatesQuery = {};
                switch (data.facilityType) {
                    case "HOUSEHOLD":
                        coordinatesQuery = this.getHouseholdCoordinatesQuery(data.level, location.id);
                        break;
                    case "SCHOOL":
                        coordinatesQuery = this.getSchoolCoordinatesQuery(data.level, location.id);
                        break;
                    case "HEALTH_FACILITY":
                        coordinatesQuery = this.getHealthFacilityCoordinatesQuery(data.level, location.id);
                        break;
                    default:
                        continue;
                }
                const locationsWithCoords = await this.prisma.location.findMany({
                    where: coordinatesQuery,
                    select: {
                        latitude: true,
                        longitude: true,
                    },
                });
                const validCoords = locationsWithCoords.filter(loc => loc.latitude && loc.longitude);
                if (validCoords.length === 0) {
                    continue;
                }
                const avgLatitude = validCoords.reduce((sum, loc) => sum + loc.latitude, 0) / validCoords.length;
                const avgLongitude = validCoords.reduce((sum, loc) => sum + loc.longitude, 0) / validCoords.length;
                let indicators = [];
                if (data.facilityType === "HOUSEHOLD") {
                    const waterIndicators = await this.householdWaterSuppy(indicatorRequestProps);
                    const sanitationIndicators = await this.houseHoldSanitation(indicatorRequestProps);
                    const hygieneIndicators = await this.houseHoldHygiene(indicatorRequestProps);
                    indicators = [
                        { title: "Household Water Supply", items: waterIndicators },
                        { title: "Household Sanitation", items: sanitationIndicators },
                        { title: "Household Hygiene", items: hygieneIndicators },
                    ];
                }
                else if (data.facilityType === "SCHOOL") {
                    const waterIndicators = await this.schoolWaterSupply(indicatorRequestProps);
                    const sanitationIndicators = await this.schoolSanitation(indicatorRequestProps);
                    const hygieneIndicators = await this.schoolHygiene(indicatorRequestProps);
                    indicators = [
                        { title: "School Water Supply", items: waterIndicators },
                        { title: "School Sanitation", items: sanitationIndicators },
                        { title: "School Hygiene", items: hygieneIndicators },
                    ];
                }
                else if (data.facilityType === "HEALTH_FACILITY") {
                    const waterIndicators = await this.healthFacilityWaterSupply(indicatorRequestProps);
                    const sanitationIndicators = await this.healthFacilitySanitation(indicatorRequestProps);
                    const hygieneIndicators = await this.healthFacilityHygiene(indicatorRequestProps);
                    indicators = [
                        { title: "Health Facility Water Supply", items: waterIndicators },
                        { title: "Health Facility Sanitation", items: sanitationIndicators },
                        { title: "Health Facility Hygiene", items: hygieneIndicators },
                    ];
                }
                else if (data.facilityType === "PUBLIC_PLACE") {
                    const waterIndicators = await this.publicPlaceWaterSupply(indicatorRequestProps);
                    const sanitationIndicators = await this.publicPlaceSanitation(indicatorRequestProps);
                    const hygieneIndicators = await this.publicPlaceHygiene(indicatorRequestProps);
                    indicators = [
                        { title: "Public Place Water Supply", items: waterIndicators },
                        { title: "Public Place Sanitation", items: sanitationIndicators },
                        { title: "Public Place Hygiene", items: hygieneIndicators },
                    ];
                }
                if (indicators.some(indicator => indicator.items.length > 0)) {
                    locationData.push({
                        id: location.id,
                        name: location.name,
                        latitude: avgLatitude,
                        longitude: avgLongitude,
                        indicators,
                    });
                }
            }
            return {
                level: data.level,
                startDate: data.startDate,
                endDate: data.endDate,
                facilityType: data.facilityType,
                locations: locationData,
            };
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException('Failed to get geo mapping indicators');
        }
    }
    getHouseholdCoordinatesQuery(level, locationId) {
        switch (level) {
            case "PROVINCE":
                return {
                    village: {
                        cell: {
                            sector: {
                                district: {
                                    provinceId: locationId,
                                },
                            },
                        },
                    },
                };
            case "DISTRICT":
                return {
                    village: {
                        cell: {
                            sector: {
                                districtId: locationId,
                            },
                        },
                    },
                };
            case "SECTOR":
                return {
                    village: {
                        cell: {
                            sectorId: locationId,
                        },
                    },
                };
            case "CELL":
                return {
                    village: {
                        cellId: locationId,
                    },
                };
            case "VILLAGE":
                return {
                    villageId: locationId,
                };
            default:
                return {};
        }
    }
    getSchoolCoordinatesQuery(level, locationId) {
        return this.getHouseholdCoordinatesQuery(level, locationId);
    }
    getHealthFacilityCoordinatesQuery(level, locationId) {
        return this.getHouseholdCoordinatesQuery(level, locationId);
    }
    async schoolWaterSupply(data) {
        let schools = [];
        switch (data.level) {
            case "COUNTRY":
                schools = await this.prisma.school.findMany({});
                break;
            case "PROVINCE":
                const province = await this.prisma.province.findUnique({
                    where: { id: Number(data.levelId) },
                });
                schools = await this.prisma.school.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        district: {
                                            provinceId: province.id,
                                        },
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "DISTRICT":
                const district = await this.prisma.district.findUnique({
                    where: { id: Number(data.levelId) },
                });
                schools = await this.prisma.school.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        districtId: district.id,
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "SECTOR":
                const sector = await this.prisma.sector.findUnique({
                    where: { id: Number(data.levelId) },
                });
                schools = await this.prisma.school.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sectorId: sector.id,
                                },
                            },
                        },
                    },
                });
                break;
            case "CELL":
                const cell = await this.prisma.cell.findUnique({
                    where: { id: Number(data.levelId) },
                });
                schools = await this.prisma.school.findMany({
                    where: {
                        location: {
                            village: {
                                cellId: cell.id,
                            },
                        },
                    },
                });
                break;
            case "VILLAGE":
                const village = await this.prisma.village.findUnique({
                    where: { id: Number(data.levelId) },
                });
                schools = await this.prisma.school.findMany({
                    where: {
                        location: {
                            villageId: village.id,
                        },
                    },
                });
                break;
        }
        const submissions = await this.prisma.submission.findMany({
            where: {
                schoolId: {
                    in: schools.map((school) => school.id),
                },
                submittedAt: {
                    gte: new Date(data.startDate),
                    lte: new Date(data.endDate),
                },
            },
            include: {
                SchoolGeneralInfo: true,
                SchoolWaterSupply: true,
                school: {
                    include: {
                        location: true,
                    },
                },
            },
        });
        if (submissions.length === 0) {
            return [];
        }
        const totalSchools = submissions.length;
        const connectedToPipeline = submissions.filter((submission) => submission.SchoolWaterSupply[0].connectedToPipeline).length;
        const percentageConnectedToPipeline = (connectedToPipeline / totalSchools) * 100;
        const waterAvailable = submissions.filter((submission) => submission.SchoolWaterSupply[0].waterAvailability).length;
        const percentageWaterAvailable = (waterAvailable / totalSchools) * 100;
        const improvedWaterSources = submissions.filter((submission) => submission.SchoolWaterSupply[0].mainWaterSource === client_1.MainWaterSource.WATER_TAP_WITHIN_HH ||
            submission.SchoolWaterSupply[0].mainWaterSource === client_1.MainWaterSource.PUBLIC_WATER_TAP_KIOSK ||
            submission.SchoolWaterSupply[0].mainWaterSource === client_1.MainWaterSource.PROTECTED_IMPROVED_SPRINGS ||
            submission.SchoolWaterSupply[0].mainWaterSource === client_1.MainWaterSource.BOREHOLE ||
            submission.SchoolWaterSupply[0].mainWaterSource === client_1.MainWaterSource.FROM_NEIGHBOR).length;
        const percentageImprovedWaterSources = (improvedWaterSources / totalSchools) * 100;
        const nearWaterSource = submissions.filter((submission) => submission.SchoolWaterSupply[0].distanceToSource === client_1.WaterSourceDistance.METERS_1_TO_200).length;
        const percentageNearWaterSource = (nearWaterSource / totalSchools) * 100;
        return [
            {
                name: "Schools connected to pipeline",
                value: connectedToPipeline,
                percentage: percentageConnectedToPipeline,
            },
            {
                name: "Schools with water availability",
                value: waterAvailable,
                percentage: percentageWaterAvailable,
            },
            {
                name: "Schools with improved water sources",
                value: improvedWaterSources,
                percentage: percentageImprovedWaterSources,
            },
            {
                name: "Schools with water source within 200m",
                value: nearWaterSource,
                percentage: percentageNearWaterSource,
            },
        ];
    }
    async schoolSanitation(data) {
        let schools = [];
        switch (data.level) {
            case "COUNTRY":
                schools = await this.prisma.school.findMany({});
                break;
            case "PROVINCE":
                const province = await this.prisma.province.findUnique({
                    where: { id: Number(data.levelId) },
                });
                schools = await this.prisma.school.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        district: {
                                            provinceId: province.id,
                                        },
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "DISTRICT":
                const district = await this.prisma.district.findUnique({
                    where: { id: Number(data.levelId) },
                });
                schools = await this.prisma.school.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        districtId: district.id,
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "SECTOR":
                const sector = await this.prisma.sector.findUnique({
                    where: { id: Number(data.levelId) },
                });
                schools = await this.prisma.school.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sectorId: sector.id,
                                },
                            },
                        },
                    },
                });
                break;
            case "CELL":
                const cell = await this.prisma.cell.findUnique({
                    where: { id: Number(data.levelId) },
                });
                schools = await this.prisma.school.findMany({
                    where: {
                        location: {
                            village: {
                                cellId: cell.id,
                            },
                        },
                    },
                });
                break;
            case "VILLAGE":
                const village = await this.prisma.village.findUnique({
                    where: { id: Number(data.levelId) },
                });
                schools = await this.prisma.school.findMany({
                    where: {
                        location: {
                            villageId: village.id,
                        },
                    },
                });
                break;
        }
        const submissions = await this.prisma.submission.findMany({
            where: {
                schoolId: {
                    in: schools.map((school) => school.id),
                },
                submittedAt: {
                    gte: new Date(data.startDate),
                    lte: new Date(data.endDate),
                },
            },
            include: {
                SchoolGeneralInfo: true,
                SchoolSanitation: true,
                school: {
                    include: {
                        location: true,
                    },
                },
            },
        });
        if (submissions.length === 0) {
            return [];
        }
        const totalSchools = submissions.length;
        const basicSanitation = submissions.filter((submission) => submission.SchoolSanitation[0].toiletType === client_1.ToiletFacilityType.FLUSH_TO_SEWER_SYSTEM ||
            submission.SchoolSanitation[0].toiletType === client_1.ToiletFacilityType.FLUSH_TO_SEPTIC_TANK ||
            submission.SchoolSanitation[0].toiletType === client_1.ToiletFacilityType.FLUSH_TO_PIT ||
            submission.SchoolSanitation[0].toiletType === client_1.ToiletFacilityType.VENTILATED_IMPROVED_PIT ||
            submission.SchoolSanitation[0].toiletType === client_1.ToiletFacilityType.PIT_WITH_SLAB ||
            submission.SchoolSanitation[0].toiletType === client_1.ToiletFacilityType.COMPOSTING_TOILET ||
            submission.SchoolSanitation[0].toiletType === client_1.ToiletFacilityType.URINE_DIVERSION_DRY_TOILET).length;
        const percentageBasicSanitation = (basicSanitation / totalSchools) * 100;
        const noSanitation = submissions.filter((submission) => submission.SchoolSanitation[0].toiletType === client_1.ToiletFacilityType.NO_FACILITY ||
            submission.SchoolSanitation[0].toiletType === client_1.ToiletFacilityType.OTHER).length;
        const percentageNoSanitation = (noSanitation / totalSchools) * 100;
        return [
            {
                name: "Schools with basic sanitation services",
                value: basicSanitation,
                percentage: percentageBasicSanitation,
            },
            {
                name: "Schools without sanitation services",
                value: noSanitation,
                percentage: percentageNoSanitation,
            },
        ];
    }
    async schoolHygiene(data) {
        let schools = [];
        switch (data.level) {
            case "COUNTRY":
                schools = await this.prisma.school.findMany({});
                break;
            case "PROVINCE":
                const province = await this.prisma.province.findUnique({
                    where: { id: Number(data.levelId) },
                });
                schools = await this.prisma.school.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        district: {
                                            provinceId: province.id,
                                        },
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "DISTRICT":
                const district = await this.prisma.district.findUnique({
                    where: { id: Number(data.levelId) },
                });
                schools = await this.prisma.school.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        districtId: district.id,
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "SECTOR":
                const sector = await this.prisma.sector.findUnique({
                    where: { id: Number(data.levelId) },
                });
                schools = await this.prisma.school.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sectorId: sector.id,
                                },
                            },
                        },
                    },
                });
                break;
            case "CELL":
                const cell = await this.prisma.cell.findUnique({
                    where: { id: Number(data.levelId) },
                });
                schools = await this.prisma.school.findMany({
                    where: {
                        location: {
                            village: {
                                cellId: cell.id,
                            },
                        },
                    },
                });
                break;
            case "VILLAGE":
                const village = await this.prisma.village.findUnique({
                    where: { id: Number(data.levelId) },
                });
                schools = await this.prisma.school.findMany({
                    where: {
                        location: {
                            villageId: village.id,
                        },
                    },
                });
                break;
        }
        const submissions = await this.prisma.submission.findMany({
            where: {
                schoolId: {
                    in: schools.map((school) => school.id),
                },
                submittedAt: {
                    gte: new Date(data.startDate),
                    lte: new Date(data.endDate),
                },
            },
            include: {
                SchoolGeneralInfo: true,
                SchoolHygiene: true,
                school: {
                    include: {
                        location: true,
                    },
                },
            },
        });
        if (submissions.length === 0) {
            return [];
        }
        const totalSchools = submissions.length;
        const basicHygiene = submissions.filter((submission) => submission.SchoolHygiene[0].handwashingFacility &&
            submission.SchoolHygiene[0].handwashingMaterials === client_1.HandWashingMaterial.WATER_AND_SOAP).length;
        const percentageBasicHygiene = (basicHygiene / totalSchools) * 100;
        const limitedHygiene = submissions.filter((submission) => submission.SchoolHygiene[0].handwashingFacility &&
            (submission.SchoolHygiene[0].handwashingMaterials === client_1.HandWashingMaterial.ONLY_WATER ||
                submission.SchoolHygiene[0].handwashingMaterials === client_1.HandWashingMaterial.NONE)).length;
        const percentageLimitedHygiene = (limitedHygiene / totalSchools) * 100;
        const noHygiene = submissions.filter((submission) => !submission.SchoolHygiene[0].handwashingFacility).length;
        const percentageNoHygiene = (noHygiene / totalSchools) * 100;
        return [
            {
                name: "Schools with basic hygiene services",
                value: basicHygiene,
                percentage: percentageBasicHygiene,
            },
            {
                name: "Schools with limited hygiene services",
                value: limitedHygiene,
                percentage: percentageLimitedHygiene,
            },
            {
                name: "Schools with no hygiene services",
                value: noHygiene,
                percentage: percentageNoHygiene,
            },
        ];
    }
    async healthFacilityWaterSupply(data) {
        let healthFacilities = [];
        switch (data.level) {
            case "COUNTRY":
                healthFacilities = await this.prisma.healthFacility.findMany({});
                break;
            case "PROVINCE":
                const province = await this.prisma.province.findUnique({
                    where: { id: Number(data.levelId) },
                });
                healthFacilities = await this.prisma.healthFacility.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        district: {
                                            provinceId: province.id,
                                        },
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "DISTRICT":
                const district = await this.prisma.district.findUnique({
                    where: { id: Number(data.levelId) },
                });
                healthFacilities = await this.prisma.healthFacility.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        districtId: district.id,
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "SECTOR":
                const sector = await this.prisma.sector.findUnique({
                    where: { id: Number(data.levelId) },
                });
                healthFacilities = await this.prisma.healthFacility.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sectorId: sector.id,
                                },
                            },
                        },
                    },
                });
                break;
            case "CELL":
                const cell = await this.prisma.cell.findUnique({
                    where: { id: Number(data.levelId) },
                });
                healthFacilities = await this.prisma.healthFacility.findMany({
                    where: {
                        location: {
                            village: {
                                cellId: cell.id,
                            },
                        },
                    },
                });
                break;
            case "VILLAGE":
                const village = await this.prisma.village.findUnique({
                    where: { id: Number(data.levelId) },
                });
                healthFacilities = await this.prisma.healthFacility.findMany({
                    where: {
                        location: {
                            villageId: village.id,
                        },
                    },
                });
                break;
        }
        const submissions = await this.prisma.submission.findMany({
            where: {
                healthFacilityId: {
                    in: healthFacilities.map((facility) => facility.id),
                },
                submittedAt: {
                    gte: new Date(data.startDate),
                    lte: new Date(data.endDate),
                },
            },
            include: {
                HealthFacilityGeneralInfo: true,
                HealthFacilityWaterSupply: true,
                healthFacility: {
                    include: {
                        location: true,
                    },
                },
            },
        });
        if (submissions.length === 0) {
            return [];
        }
        const totalHealthFacilities = submissions.length;
        const connectedToPipeline = submissions.filter((submission) => submission.HealthFacilityWaterSupply[0].connectedToPipeline).length;
        const percentageConnectedToPipeline = (connectedToPipeline / totalHealthFacilities) * 100;
        const waterAvailable = submissions.filter((submission) => submission.HealthFacilityWaterSupply[0].waterAvailability === client_1.WaterAvailability.ALWAYS_AVAILABLE).length;
        const percentageWaterAvailable = (waterAvailable / totalHealthFacilities) * 100;
        const improvedWaterSources = submissions.filter((submission) => submission.HealthFacilityWaterSupply[0].mainWaterSource === client_1.MainWaterSource.WATER_TAP_WITHIN_HH ||
            submission.HealthFacilityWaterSupply[0].mainWaterSource === client_1.MainWaterSource.PUBLIC_WATER_TAP_KIOSK ||
            submission.HealthFacilityWaterSupply[0].mainWaterSource === client_1.MainWaterSource.PROTECTED_IMPROVED_SPRINGS ||
            submission.HealthFacilityWaterSupply[0].mainWaterSource === client_1.MainWaterSource.BOREHOLE ||
            submission.HealthFacilityWaterSupply[0].mainWaterSource === client_1.MainWaterSource.FROM_NEIGHBOR).length;
        const percentageImprovedWaterSources = (improvedWaterSources / totalHealthFacilities) * 100;
        return [
            {
                name: "Health facilities connected to pipeline",
                value: connectedToPipeline,
                percentage: percentageConnectedToPipeline,
            },
            {
                name: "Health facilities with water availability",
                value: waterAvailable,
                percentage: percentageWaterAvailable,
            },
            {
                name: "Health facilities with improved water sources",
                value: improvedWaterSources,
                percentage: percentageImprovedWaterSources,
            },
        ];
    }
    async healthFacilitySanitation(data) {
        let healthFacilities = [];
        switch (data.level) {
            case "COUNTRY":
                healthFacilities = await this.prisma.healthFacility.findMany({});
                break;
            case "PROVINCE":
                const province = await this.prisma.province.findUnique({
                    where: { id: Number(data.levelId) },
                });
                healthFacilities = await this.prisma.healthFacility.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        district: {
                                            provinceId: province.id,
                                        },
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "DISTRICT":
                const district = await this.prisma.district.findUnique({
                    where: { id: Number(data.levelId) },
                });
                healthFacilities = await this.prisma.healthFacility.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        districtId: district.id,
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "SECTOR":
                const sector = await this.prisma.sector.findUnique({
                    where: { id: Number(data.levelId) },
                });
                healthFacilities = await this.prisma.healthFacility.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sectorId: sector.id,
                                },
                            },
                        },
                    },
                });
                break;
            case "CELL":
                const cell = await this.prisma.cell.findUnique({
                    where: { id: Number(data.levelId) },
                });
                healthFacilities = await this.prisma.healthFacility.findMany({
                    where: {
                        location: {
                            village: {
                                cellId: cell.id,
                            },
                        },
                    },
                });
                break;
            case "VILLAGE":
                const village = await this.prisma.village.findUnique({
                    where: { id: Number(data.levelId) },
                });
                healthFacilities = await this.prisma.healthFacility.findMany({
                    where: {
                        location: {
                            villageId: village.id,
                        },
                    },
                });
                break;
        }
        const submissions = await this.prisma.submission.findMany({
            where: {
                healthFacilityId: {
                    in: healthFacilities.map((facility) => facility.id),
                },
                submittedAt: {
                    gte: new Date(data.startDate),
                    lte: new Date(data.endDate),
                },
            },
            include: {
                HealthFacilityGeneralInfo: true,
                HealthFacilitySanitation: true,
                healthFacility: {
                    include: {
                        location: true,
                    },
                },
            },
        });
        if (submissions.length === 0) {
            return [];
        }
        const totalHealthFacilities = submissions.length;
        const basicSanitation = submissions.filter((submission) => submission.HealthFacilitySanitation[0].toiletType === client_1.ToiletFacilityType.FLUSH_TO_SEWER_SYSTEM ||
            submission.HealthFacilitySanitation[0].toiletType === client_1.ToiletFacilityType.FLUSH_TO_SEPTIC_TANK ||
            submission.HealthFacilitySanitation[0].toiletType === client_1.ToiletFacilityType.FLUSH_TO_PIT ||
            submission.HealthFacilitySanitation[0].toiletType === client_1.ToiletFacilityType.VENTILATED_IMPROVED_PIT ||
            submission.HealthFacilitySanitation[0].toiletType === client_1.ToiletFacilityType.PIT_WITH_SLAB ||
            submission.HealthFacilitySanitation[0].toiletType === client_1.ToiletFacilityType.COMPOSTING_TOILET ||
            submission.HealthFacilitySanitation[0].toiletType === client_1.ToiletFacilityType.URINE_DIVERSION_DRY_TOILET).length;
        const percentageBasicSanitation = (basicSanitation / totalHealthFacilities) * 100;
        const noSanitation = submissions.filter((submission) => submission.HealthFacilitySanitation[0].toiletType === client_1.ToiletFacilityType.NO_FACILITY ||
            submission.HealthFacilitySanitation[0].toiletType === client_1.ToiletFacilityType.OTHER).length;
        const percentageNoSanitation = (noSanitation / totalHealthFacilities) * 100;
        return [
            {
                name: "Health facilities with basic sanitation services",
                value: basicSanitation,
                percentage: percentageBasicSanitation,
            },
            {
                name: "Health facilities without sanitation services",
                value: noSanitation,
                percentage: percentageNoSanitation,
            },
        ];
    }
    async healthFacilityHygiene(data) {
        let healthFacilities = [];
        switch (data.level) {
            case "COUNTRY":
                healthFacilities = await this.prisma.healthFacility.findMany({});
                break;
            case "PROVINCE":
                const province = await this.prisma.province.findUnique({
                    where: { id: Number(data.levelId) },
                });
                healthFacilities = await this.prisma.healthFacility.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        district: {
                                            provinceId: province.id,
                                        },
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "DISTRICT":
                const district = await this.prisma.district.findUnique({
                    where: { id: Number(data.levelId) },
                });
                healthFacilities = await this.prisma.healthFacility.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        districtId: district.id,
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "SECTOR":
                const sector = await this.prisma.sector.findUnique({
                    where: { id: Number(data.levelId) },
                });
                healthFacilities = await this.prisma.healthFacility.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sectorId: sector.id,
                                },
                            },
                        },
                    },
                });
                break;
            case "CELL":
                const cell = await this.prisma.cell.findUnique({
                    where: { id: Number(data.levelId) },
                });
                healthFacilities = await this.prisma.healthFacility.findMany({
                    where: {
                        location: {
                            village: {
                                cellId: cell.id,
                            },
                        },
                    },
                });
                break;
            case "VILLAGE":
                const village = await this.prisma.village.findUnique({
                    where: { id: Number(data.levelId) },
                });
                healthFacilities = await this.prisma.healthFacility.findMany({
                    where: {
                        location: {
                            villageId: village.id,
                        },
                    },
                });
                break;
        }
        const submissions = await this.prisma.submission.findMany({
            where: {
                healthFacilityId: {
                    in: healthFacilities.map((facility) => facility.id),
                },
                submittedAt: {
                    gte: new Date(data.startDate),
                    lte: new Date(data.endDate),
                },
            },
            include: {
                HealthFacilityGeneralInfo: true,
                HealthFacilityHygiene: true,
                healthFacility: {
                    include: {
                        location: true,
                    },
                },
            },
        });
        if (submissions.length === 0) {
            return [];
        }
        const totalHealthFacilities = submissions.length;
        const basicHygiene = submissions.filter((submission) => submission.HealthFacilityHygiene[0].handwashingFacility &&
            submission.HealthFacilityHygiene[0].handwashingMaterials === client_1.HandWashingMaterial.WATER_AND_SOAP).length;
        const percentageBasicHygiene = (basicHygiene / totalHealthFacilities) * 100;
        const limitedHygiene = submissions.filter((submission) => submission.HealthFacilityHygiene[0].handwashingFacility &&
            (submission.HealthFacilityHygiene[0].handwashingMaterials === client_1.HandWashingMaterial.ONLY_WATER ||
                submission.HealthFacilityHygiene[0].handwashingMaterials === client_1.HandWashingMaterial.NONE)).length;
        const percentageLimitedHygiene = (limitedHygiene / totalHealthFacilities) * 100;
        const noHygiene = submissions.filter((submission) => !submission.HealthFacilityHygiene[0].handwashingFacility).length;
        const percentageNoHygiene = (noHygiene / totalHealthFacilities) * 100;
        return [
            {
                name: "Health facilities with basic hygiene services",
                value: basicHygiene,
                percentage: percentageBasicHygiene,
            },
            {
                name: "Health facilities with limited hygiene services",
                value: limitedHygiene,
                percentage: percentageLimitedHygiene,
            },
            {
                name: "Health facilities with no hygiene services",
                value: noHygiene,
                percentage: percentageNoHygiene,
            },
        ];
    }
    async publicPlaceWaterSupply(data) {
        let publicPlaces = [];
        switch (data.level) {
            case "COUNTRY":
                publicPlaces = await this.prisma.publicPlace.findMany({});
                break;
            case "PROVINCE":
                const province = await this.prisma.province.findUnique({
                    where: { id: Number(data.levelId) },
                });
                publicPlaces = await this.prisma.publicPlace.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        district: {
                                            provinceId: province.id,
                                        },
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "DISTRICT":
                const district = await this.prisma.district.findUnique({
                    where: { id: Number(data.levelId) },
                });
                publicPlaces = await this.prisma.publicPlace.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        districtId: district.id,
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "SECTOR":
                const sector = await this.prisma.sector.findUnique({
                    where: { id: Number(data.levelId) },
                });
                publicPlaces = await this.prisma.publicPlace.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sectorId: sector.id,
                                },
                            },
                        },
                    },
                });
                break;
            case "CELL":
                const cell = await this.prisma.cell.findUnique({
                    where: { id: Number(data.levelId) },
                });
                publicPlaces = await this.prisma.publicPlace.findMany({
                    where: {
                        location: {
                            village: {
                                cellId: cell.id,
                            },
                        },
                    },
                });
                break;
            case "VILLAGE":
                const village = await this.prisma.village.findUnique({
                    where: { id: Number(data.levelId) },
                });
                publicPlaces = await this.prisma.publicPlace.findMany({
                    where: {
                        location: {
                            villageId: village.id,
                        },
                    },
                });
                break;
        }
        const submissions = await this.prisma.submission.findMany({
            where: {
                publicPlaceId: {
                    in: publicPlaces.map((place) => place.id),
                },
                submittedAt: {
                    gte: new Date(data.startDate),
                    lte: new Date(data.endDate),
                },
            },
            include: {
                PublicPlaceGeneralInfo: true,
                PublicPlaceWaterSupply: true,
                publicPlace: {
                    include: {
                        location: true,
                    },
                },
            },
        });
        if (submissions.length === 0) {
            return [];
        }
        const totalPublicPlaces = submissions.length;
        const connectedToPipeline = submissions.filter((submission) => submission.PublicPlaceWaterSupply[0].connectedToPipeline).length;
        const percentageConnectedToPipeline = (connectedToPipeline / totalPublicPlaces) * 100;
        const waterAvailable = submissions.filter((submission) => submission.PublicPlaceWaterSupply[0].waterAvailability === client_1.WaterAvailability.ALWAYS_AVAILABLE).length;
        const percentageWaterAvailable = (waterAvailable / totalPublicPlaces) * 100;
        const improvedWaterSources = submissions.filter((submission) => submission.PublicPlaceWaterSupply[0].mainWaterSource === client_1.MainWaterSource.WATER_TAP_WITHIN_HH ||
            submission.PublicPlaceWaterSupply[0].mainWaterSource === client_1.MainWaterSource.PUBLIC_WATER_TAP_KIOSK ||
            submission.PublicPlaceWaterSupply[0].mainWaterSource === client_1.MainWaterSource.PROTECTED_IMPROVED_SPRINGS ||
            submission.PublicPlaceWaterSupply[0].mainWaterSource === client_1.MainWaterSource.BOREHOLE ||
            submission.PublicPlaceWaterSupply[0].mainWaterSource === client_1.MainWaterSource.FROM_NEIGHBOR).length;
        const percentageImprovedWaterSources = (improvedWaterSources / totalPublicPlaces) * 100;
        return [
            {
                name: "Public places connected to pipeline",
                value: connectedToPipeline,
                percentage: percentageConnectedToPipeline,
            },
            {
                name: "Public places with water availability",
                value: waterAvailable,
                percentage: percentageWaterAvailable,
            },
            {
                name: "Public places with improved water sources",
                value: improvedWaterSources,
                percentage: percentageImprovedWaterSources,
            },
        ];
    }
    async publicPlaceSanitation(data) {
        let publicPlaces = [];
        switch (data.level) {
            case "COUNTRY":
                publicPlaces = await this.prisma.publicPlace.findMany({});
                break;
            case "PROVINCE":
                const province = await this.prisma.province.findUnique({
                    where: { id: Number(data.levelId) },
                });
                publicPlaces = await this.prisma.publicPlace.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        district: {
                                            provinceId: province.id,
                                        },
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "DISTRICT":
                const district = await this.prisma.district.findUnique({
                    where: { id: Number(data.levelId) },
                });
                publicPlaces = await this.prisma.publicPlace.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        districtId: district.id,
                                    },
                                },
                            },
                        },
                    },
                });
                break;
            case "SECTOR":
                const sector = await this.prisma.sector.findUnique({
                    where: { id: Number(data.levelId) },
                });
                publicPlaces = await this.prisma.publicPlace.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sectorId: sector.id,
                                },
                            },
                        },
                    },
                });
                break;
            case "CELL":
                const cell = await this.prisma.cell.findUnique({
                    where: { id: Number(data.levelId) },
                });
                publicPlaces = await this.prisma.publicPlace.findMany({
                    where: {
                        location: {
                            village: {
                                cellId: cell.id,
                            },
                        },
                    },
                });
                break;
            case "VILLAGE":
                const village = await this.prisma.village.findUnique({
                    where: { id: Number(data.levelId) },
                });
                publicPlaces = await this.prisma.publicPlace.findMany({
                    where: {
                        location: {
                            villageId: village.id,
                        },
                    },
                });
                break;
        }
        const submissions = await this.prisma.submission.findMany({
            where: {
                publicPlaceId: {
                    in: publicPlaces.map((place) => place.id),
                },
                submittedAt: {
                    gte: new Date(data.startDate),
                    lte: new Date(data.endDate),
                },
            },
            include: {
                PublicPlaceGeneralInfo: true,
                PublicPlaceSanitation: true,
                publicPlace: {
                    include: {
                        location: true,
                    },
                },
            },
        });
        if (submissions.length === 0) {
            return [];
        }
        const totalPublicPlaces = submissions.length;
        const basicSanitation = submissions.filter((submission) => submission.PublicPlaceSanitation[0].toiletType === client_1.ToiletFacilityType.FLUSH_TO_SEWER_SYSTEM ||
            submission.PublicPlaceSanitation[0].toiletType === client_1.ToiletFacilityType.FLUSH_TO_SEPTIC_TANK ||
            submission.PublicPlaceSanitation[0].toiletType === client_1.ToiletFacilityType.FLUSH_TO_PIT ||
            submission.PublicPlaceSanitation[0].toiletType === client_1.ToiletFacilityType.VENTILATED_IMPROVED_PIT ||
            submission.PublicPlaceSanitation[0].toiletType === client_1.ToiletFacilityType.PIT_WITH_SLAB ||
            submission.PublicPlaceSanitation[0].toiletType === client_1.ToiletFacilityType.COMPOSTING_TOILET ||
            submission.PublicPlaceSanitation[0].toiletType === client_1.ToiletFacilityType.URINE_DIVERSION_DRY_TOILET).length;
        const percentageBasicSanitation = (basicSanitation / totalPublicPlaces) * 100;
        const noSanitation = submissions.filter((submission) => submission.PublicPlaceSanitation[0].toiletType === client_1.ToiletFacilityType.NO_FACILITY ||
            submission.PublicPlaceSanitation[0].toiletType === client_1.ToiletFacilityType.OTHER).length;
        const percentageNoSanitation = (noSanitation / totalPublicPlaces) * 100;
        return [
            {
                name: "Public places with basic sanitation services",
                value: basicSanitation,
                percentage: percentageBasicSanitation,
            },
            {
                name: "Public places without sanitation services",
                value: noSanitation,
                percentage: percentageNoSanitation,
            },
        ];
    }
    async publicPlaceHygiene(data) {
        const publicPlaces = await this.getPublicPlacesByLevel(data.level, data.levelId);
        const submissions = await this.prisma.submission.findMany({
            where: {
                publicPlaceId: {
                    in: publicPlaces.map((place) => place.id),
                },
                submittedAt: {
                    gte: new Date(data.startDate),
                    lte: new Date(data.endDate),
                },
            },
            include: {
                PublicPlaceGeneralInfo: true,
                PublicPlaceHygiene: true,
                publicPlace: {
                    include: {
                        location: true,
                    },
                },
            },
        });
        if (submissions.length === 0) {
            return [];
        }
        const totalPublicPlaces = submissions.length;
        const basicHygiene = submissions.filter((submission) => submission.PublicPlaceHygiene[0].handwashingFacility &&
            submission.PublicPlaceHygiene[0].handwashingMaterials === 'WATER_AND_SOAP').length;
        const percentageBasicHygiene = (basicHygiene / totalPublicPlaces) * 100;
        const noHygiene = submissions.filter((submission) => !submission.PublicPlaceHygiene[0].handwashingFacility).length;
        const percentageNoHygiene = (noHygiene / totalPublicPlaces) * 100;
        return [
            {
                name: "Public places with basic hygiene services",
                value: basicHygiene,
                percentage: percentageBasicHygiene,
            },
            {
                name: "Public places with no hygiene services",
                value: noHygiene,
                percentage: percentageNoHygiene,
            },
        ];
    }
    async getPublicPlacesByLevel(level, levelId) {
        switch (level) {
            case "COUNTRY":
                return await this.prisma.publicPlace.findMany({});
            case "PROVINCE":
                const province = await this.prisma.province.findUnique({
                    where: { id: Number(levelId) },
                });
                return await this.prisma.publicPlace.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        district: {
                                            provinceId: province.id,
                                        },
                                    },
                                },
                            },
                        },
                    },
                });
            case "DISTRICT":
                const district = await this.prisma.district.findUnique({
                    where: { id: Number(levelId) },
                });
                return await this.prisma.publicPlace.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        districtId: district.id,
                                    },
                                },
                            },
                        },
                    },
                });
            case "SECTOR":
                const sector = await this.prisma.sector.findUnique({
                    where: { id: Number(levelId) },
                });
                return await this.prisma.publicPlace.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sectorId: sector.id,
                                },
                            },
                        },
                    },
                });
            case "CELL":
                const cell = await this.prisma.cell.findUnique({
                    where: { id: Number(levelId) },
                });
                return await this.prisma.publicPlace.findMany({
                    where: {
                        location: {
                            village: {
                                cellId: cell.id,
                            },
                        },
                    },
                });
            case "VILLAGE":
                const village = await this.prisma.village.findUnique({
                    where: { id: Number(levelId) },
                });
                return await this.prisma.publicPlace.findMany({
                    where: {
                        location: {
                            villageId: village.id,
                        },
                    },
                });
            default:
                return [];
        }
    }
};
exports.IndicatorsService = IndicatorsService;
exports.IndicatorsService = IndicatorsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], IndicatorsService);
//# sourceMappingURL=indicators.service.js.map