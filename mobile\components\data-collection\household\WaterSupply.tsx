import AppText from '@/components/ui/Text';
import * as enums from '@/types/enums';
import { getEnumOptions, isEnumKey } from '@/utils/enum';
import { Picker } from '@react-native-picker/picker';
import React, { useEffect } from 'react';
import { Controller, useWatch } from 'react-hook-form';
import { View } from 'react-native';
import { TextInput } from 'react-native-paper';
import tw from 'twrnc';

const WaterSupplySection = ({ control, errors }: any) => {
    const unimprovedReason = useWatch({ control, name: 'waterSupply.unimprovedReason' });
    const waterSource = useWatch({ control, name: 'waterSupply.waterSource' });
    const waterAvailability = useWatch({ control, name: 'waterSupply.waterAvailability' });

    return (
        <>
            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Main source of water supply used by the members of Household</AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="waterSupply.waterSource"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={onChange}>
                                <Picker.Item label="Select Water Source" value="" />
                                {getEnumOptions(enums.MainWaterSource).map(opt => (
                                    <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.waterSupply?.waterSource && <AppText style={tw`text-red-500`}>{errors.waterSupply?.waterSource.message}</AppText>}
            </View>

            {isEnumKey(enums.MainWaterSource, 'WATER_TAP_WITHIN_HH', waterSource) && (
                <View style={tw`mb-4`}>
                    <AppText style={tw`mb-2`}>Is the water available for use whenever needed by the members of the household?</AppText>
                    <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                        <Controller
                            control={control}
                            name="waterSupply.waterAvailability"
                            render={({ field: { onChange, value } }) => (
                                <Picker selectedValue={value} onValueChange={onChange}>
                                    <Picker.Item label="Select Water Availability" value="" />
                                    {getEnumOptions(enums.WaterAvailability).map(opt => (
                                        <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                    ))}
                                </Picker>
                            )}
                        />
                    </View>
                    {errors.waterSupply?.waterAvailability && <AppText style={tw`text-red-500`}>{errors.waterSupply?.waterAvailability.message}</AppText>}
                </View>)}

            {isEnumKey(enums.WaterAvailability, 'SOMETIMES_UNAVAILABLE', waterAvailability) &&
                <View style={tw`mb-4`}>
                    <AppText style={tw`mb-2`}>How many days is water available in the tap?</AppText>
                    <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                        <Controller
                            control={control}
                            name="waterSupply.availableDays"
                            render={({ field: { onChange, value } }) => (
                                <Picker selectedValue={value} onValueChange={onChange}>
                                    <Picker.Item label="Select Frequency" value="" />
                                    {getEnumOptions(enums.WaterAvailabilityFrequency).map(opt => (
                                        <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                    ))}
                                </Picker>
                            )}
                        />
                    </View>
                    {errors.waterSupply?.availableDays && <AppText style={tw`text-red-500`}>{errors.waterSupply?.availableDays.message}</AppText>}
                </View>}

            {isEnumKey(enums.MainWaterSource, 'WATER_TAP_WITHIN_HH', waterSource) &&
                <View style={tw`mb-4`}>
                    <AppText style={tw`mb-2`}>What is the average cost of water per month?</AppText>
                    <Controller
                        control={control}
                        name="waterSupply.averageWaterCost"
                        render={({ field: { onChange, onBlur, value } }) => (
                            <TextInput
                                style={tw`bg-white`}
                                mode="outlined"
                                placeholder="Average Water Cost"
                                outlineColor='#E5E7EB'
                                inputMode='numeric'
                                value={value}
                                onChangeText={onChange}
                                onBlur={onBlur}
                                keyboardType="numeric"
                            />
                        )}
                    />
                    {errors.waterSupply?.averageWaterCost && <AppText style={tw`text-red-500`}>{errors.waterSupply?.averageWaterCost.message}</AppText>}
                </View>}

            <View style={tw`mb-4`}>
                <AppText style={tw`mb-2`}>Clean water storage capacity by the Household (Tanks, etc ) </AppText>
                <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                    <Controller
                        control={control}
                        name="waterSupply.storageCapacity"
                        render={({ field: { onChange, value } }) => (
                            <Picker selectedValue={value} onValueChange={onChange}>
                                <Picker.Item label="Select Storage Capacity" value="" />
                                {getEnumOptions(enums.CleanWaterStorageCapacity).map(opt => (
                                    <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                ))}
                            </Picker>
                        )}
                    />
                </View>
                {errors.waterSupply?.storageCapacity && <AppText style={tw`text-red-500`}>{errors.waterSupply?.storageCapacity.message}</AppText>}
            </View>

            {!isEnumKey(enums.MainWaterSource, 'WATER_TAP_WITHIN_HH', waterSource) &&
                <View style={tw`mb-4`}>
                    <AppText style={tw`mb-2`}>Distance (estimated in metres) between the water source and the household location</AppText>
                    <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                        <Controller
                            control={control}
                            name="waterSupply.distanceToSource"
                            render={({ field: { onChange, value } }) => (
                                <Picker selectedValue={value} onValueChange={onChange}>
                                    <Picker.Item label="Select Distance" value="" />
                                    {getEnumOptions(enums.WaterSourceDistance).map(opt => (
                                        <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                    ))}
                                </Picker>
                            )}
                        />
                    </View>
                    {errors.waterSupply?.distanceToSource && <AppText style={tw`text-red-500`}>{errors.waterSupply?.distanceToSource.message}</AppText>}
                </View>}

            {!isEnumKey(enums.MainWaterSource, 'WATER_TAP_WITHIN_HH', waterSource) &&
                <View style={tw`mb-4`}>
                    <AppText style={tw`mb-2`}>Time (estimated in minutes) taken by the household members to fetch water</AppText>
                    <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                        <Controller
                            control={control}
                            name="waterSupply.timeToFetch"
                            render={({ field: { onChange, value } }) => (
                                <Picker selectedValue={value} onValueChange={onChange}>
                                    <Picker.Item label="Select Time" value="" />
                                    {getEnumOptions(enums.WaterFetchingTime).map(opt => (
                                        <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                    ))}
                                </Picker>
                            )}
                        />
                    </View>
                    {errors.waterSupply?.timeToFetch && <AppText style={tw`text-red-500`}>{errors.waterSupply?.timeToFetch.message}</AppText>}
                </View>}

            {isEnumKey(enums.MainWaterSource, 'PUBLIC_WATER_TAP_KIOSK', waterSource) &&
                <View style={tw`mb-4`}>
                    <AppText style={tw`mb-2`}>Price of water per jerry can (20L) as sold on the public water tap</AppText>
                    <Controller
                        control={control}
                        name="waterSupply.jerryCanPrice"
                        render={({ field: { onChange, onBlur, value } }) => (
                            <TextInput
                                style={tw`bg-white`}
                                mode="outlined"
                                placeholder="Jerry Can Price"
                                outlineColor='#E5E7EB'
                                inputMode='numeric'
                                value={value}
                                onChangeText={onChange}
                                onBlur={onBlur}
                                keyboardType="numeric"
                            />
                        )}
                    />
                    {errors.waterSupply?.jerryCanPrice && <AppText style={tw`text-red-500`}>{errors.waterSupply?.jerryCanPrice.message}</AppText>}
                </View>}

            {(isEnumKey(enums.MainWaterSource, 'UNIMPROVED_SPRINGS', waterSource) ||
                isEnumKey(enums.MainWaterSource, 'SURFACE_WATER', waterSource) ||
                isEnumKey(enums.MainWaterSource, 'RAIN_WATER', waterSource)
            ) &&
                <View style={tw`mb-4`}>
                    <AppText style={tw`mb-2`}>What is the main reason for using an unimproved water source?</AppText>
                    <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                        <Controller
                            control={control}
                            name="waterSupply.unimprovedReason"
                            render={({ field: { onChange, value } }) => (
                                <Picker selectedValue={value} onValueChange={onChange}>
                                    <Picker.Item label="Select Reason" value="" />
                                    {getEnumOptions(enums.UnimprovedWaterReason).map(opt => (
                                        <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                    ))}
                                </Picker>
                            )}
                        />
                    </View>
                    {errors.waterSupply?.unimprovedReason && <AppText style={tw`text-red-500`}>{errors.waterSupply?.unimprovedReason.message}</AppText>}
                </View>}

            {isEnumKey(enums.UnimprovedWaterReason, 'NEARBY_SOURCE_NOT_FUNCTIONING', unimprovedReason) && (
                <>
                    <View style={tw`mb-4`}>
                        <AppText style={tw`mb-2`}>The reason why the PWS does not function</AppText>
                        <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                            <Controller
                                control={control}
                                name="waterSupply.pwsNonFunctionalityReason"
                                render={({ field: { onChange, value } }) => (
                                    <Picker selectedValue={value} onValueChange={onChange}>
                                        <Picker.Item label="Select Reason" value="" />
                                        {getEnumOptions(enums.PwsNonFunctionalityReason).map(opt => (
                                            <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                        ))}
                                    </Picker>
                                )}
                            />
                        </View>
                        {errors.waterSupply?.pwsNonFunctionalityReason && <AppText style={tw`text-red-500`}>{errors.waterSupply?.pwsNonFunctionalityReason.message}</AppText>}
                    </View>

                    <View style={tw`mb-4`}>
                        <AppText style={tw`mb-2`}>For how long has the PWS not functioned?</AppText>
                        <View style={tw`border border-gray-300 rounded-lg mb-2`}>
                            <Controller
                                control={control}
                                name="waterSupply.pwsNonFunctionalityDuration"
                                render={({ field: { onChange, value } }) => (
                                    <Picker selectedValue={value} onValueChange={onChange}>
                                        <Picker.Item label="Select Duration" value="" />
                                        {getEnumOptions(enums.PwsNonFunctionalityDuration).map(opt => (
                                            <Picker.Item key={opt.value} label={opt.label} value={opt.value} />
                                        ))}
                                    </Picker>
                                )}
                            />
                        </View>
                        {errors.waterSupply?.pwsNonFunctionalityDuration && <AppText style={tw`text-red-500`}>{errors.waterSupply?.pwsNonFunctionalityDuration.message}</AppText>}
                    </View>
                </>
            )}
        </>
    );
};

export default WaterSupplySection;