"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthFacilitySubmissionResponseDto = exports.CreateHealthFacilitySubmissionDto = exports.HealthFacilityLiquidWasteManagementDto = exports.HealthFacilitySolidWasteManagementDto = exports.HealthFacilityHygieneDto = exports.HealthFacilitySanitationDto = exports.HealthFacilityWaterSupplyDto = exports.HealthFacilityGeneralInfoDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const client_1 = require("@prisma/client");
const base_submission_dto_1 = require("./base-submission.dto");
class HealthFacilityGeneralInfoDto {
    facilityName;
    facilityType;
    managementType;
    dailyPatientVolume;
    totalStaff;
}
exports.HealthFacilityGeneralInfoDto = HealthFacilityGeneralInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HealthFacilityGeneralInfoDto.prototype, "facilityName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.HealthFacilityType }),
    (0, class_validator_1.IsEnum)(client_1.HealthFacilityType),
    __metadata("design:type", String)
], HealthFacilityGeneralInfoDto.prototype, "facilityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.HealthFacilityManagement }),
    (0, class_validator_1.IsEnum)(client_1.HealthFacilityManagement),
    __metadata("design:type", String)
], HealthFacilityGeneralInfoDto.prototype, "managementType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.DailyPatientVolume }),
    (0, class_validator_1.IsEnum)(client_1.DailyPatientVolume),
    __metadata("design:type", String)
], HealthFacilityGeneralInfoDto.prototype, "dailyPatientVolume", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], HealthFacilityGeneralInfoDto.prototype, "totalStaff", void 0);
class HealthFacilityWaterSupplyDto {
    connectedToPipeline;
    waterAvailability;
    availableDays;
    storageCapacity;
    mainWaterSource;
    distanceToSource;
}
exports.HealthFacilityWaterSupplyDto = HealthFacilityWaterSupplyDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], HealthFacilityWaterSupplyDto.prototype, "connectedToPipeline", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WaterAvailability }),
    (0, class_validator_1.IsEnum)(client_1.WaterAvailability),
    __metadata("design:type", String)
], HealthFacilityWaterSupplyDto.prototype, "waterAvailability", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WaterAvailabilityFrequency, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.WaterAvailabilityFrequency),
    __metadata("design:type", String)
], HealthFacilityWaterSupplyDto.prototype, "availableDays", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.CleanWaterStorageCapacity }),
    (0, class_validator_1.IsEnum)(client_1.CleanWaterStorageCapacity),
    __metadata("design:type", String)
], HealthFacilityWaterSupplyDto.prototype, "storageCapacity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.MainWaterSource, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.MainWaterSource),
    __metadata("design:type", String)
], HealthFacilityWaterSupplyDto.prototype, "mainWaterSource", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WaterSourceDistance }),
    (0, class_validator_1.IsEnum)(client_1.WaterSourceDistance),
    __metadata("design:type", String)
], HealthFacilityWaterSupplyDto.prototype, "distanceToSource", void 0);
class HealthFacilitySanitationDto {
    toiletType;
    slabConstructionMaterial;
    totalToilets;
    genderSeparation;
    femaleToilets;
    maleToilets;
    disabilityAccess;
    staffToilets;
    hasToiletFullInLast2Years;
    excretaManagement;
}
exports.HealthFacilitySanitationDto = HealthFacilitySanitationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ToiletFacilityType }),
    (0, class_validator_1.IsEnum)(client_1.ToiletFacilityType),
    __metadata("design:type", String)
], HealthFacilitySanitationDto.prototype, "toiletType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.FacilitySlabConstructionMaterial }),
    (0, class_validator_1.IsEnum)(client_1.FacilitySlabConstructionMaterial),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], HealthFacilitySanitationDto.prototype, "slabConstructionMaterial", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], HealthFacilitySanitationDto.prototype, "totalToilets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], HealthFacilitySanitationDto.prototype, "genderSeparation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], HealthFacilitySanitationDto.prototype, "femaleToilets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], HealthFacilitySanitationDto.prototype, "maleToilets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], HealthFacilitySanitationDto.prototype, "disabilityAccess", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], HealthFacilitySanitationDto.prototype, "staffToilets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], HealthFacilitySanitationDto.prototype, "hasToiletFullInLast2Years", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ExcretaManagement, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.ExcretaManagement),
    __metadata("design:type", String)
], HealthFacilitySanitationDto.prototype, "excretaManagement", void 0);
class HealthFacilityHygieneDto {
    handwashingFacility;
    facilityType;
    handwashingMaterials;
    handWashingfacilityNearToilet;
    toiletHandWashingFacilityType;
    toiletHandWashingMaterials;
}
exports.HealthFacilityHygieneDto = HealthFacilityHygieneDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], HealthFacilityHygieneDto.prototype, "handwashingFacility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.HandWashingFacilityType, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.HandWashingFacilityType),
    __metadata("design:type", String)
], HealthFacilityHygieneDto.prototype, "facilityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.HandWashingMaterial, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.HandWashingMaterial),
    __metadata("design:type", String)
], HealthFacilityHygieneDto.prototype, "handwashingMaterials", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], HealthFacilityHygieneDto.prototype, "handWashingfacilityNearToilet", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.HandWashingFacilityType, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.HandWashingFacilityType),
    __metadata("design:type", String)
], HealthFacilityHygieneDto.prototype, "toiletHandWashingFacilityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.HandWashingMaterial, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.HandWashingMaterial),
    __metadata("design:type", String)
], HealthFacilityHygieneDto.prototype, "toiletHandWashingMaterials", void 0);
class HealthFacilitySolidWasteManagementDto {
    wasteSeparation;
    wasteManagement;
    treatmentType;
    collectionFrequency;
    collectionCost;
}
exports.HealthFacilitySolidWasteManagementDto = HealthFacilitySolidWasteManagementDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], HealthFacilitySolidWasteManagementDto.prototype, "wasteSeparation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteManagementAfterSeparation, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.WasteManagementAfterSeparation),
    __metadata("design:type", String)
], HealthFacilitySolidWasteManagementDto.prototype, "wasteManagement", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteTreatmentType, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.WasteTreatmentType),
    __metadata("design:type", String)
], HealthFacilitySolidWasteManagementDto.prototype, "treatmentType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteCollectionFrequency, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.WasteCollectionFrequency),
    __metadata("design:type", String)
], HealthFacilitySolidWasteManagementDto.prototype, "collectionFrequency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], HealthFacilitySolidWasteManagementDto.prototype, "collectionCost", void 0);
class HealthFacilityLiquidWasteManagementDto {
    liquidWasteManagement;
}
exports.HealthFacilityLiquidWasteManagementDto = HealthFacilityLiquidWasteManagementDto;
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteWaterManagement }),
    (0, class_validator_1.IsEnum)(client_1.WasteWaterManagement),
    __metadata("design:type", String)
], HealthFacilityLiquidWasteManagementDto.prototype, "liquidWasteManagement", void 0);
class CreateHealthFacilitySubmissionDto extends base_submission_dto_1.BaseCreateSubmissionDto {
    generalInfo;
    waterSupply;
    sanitation;
    hygiene;
    solidWaste;
    liquidWaste;
}
exports.CreateHealthFacilitySubmissionDto = CreateHealthFacilitySubmissionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: HealthFacilityGeneralInfoDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => HealthFacilityGeneralInfoDto),
    __metadata("design:type", HealthFacilityGeneralInfoDto)
], CreateHealthFacilitySubmissionDto.prototype, "generalInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HealthFacilityWaterSupplyDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => HealthFacilityWaterSupplyDto),
    __metadata("design:type", HealthFacilityWaterSupplyDto)
], CreateHealthFacilitySubmissionDto.prototype, "waterSupply", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HealthFacilitySanitationDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => HealthFacilitySanitationDto),
    __metadata("design:type", HealthFacilitySanitationDto)
], CreateHealthFacilitySubmissionDto.prototype, "sanitation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HealthFacilityHygieneDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => HealthFacilityHygieneDto),
    __metadata("design:type", HealthFacilityHygieneDto)
], CreateHealthFacilitySubmissionDto.prototype, "hygiene", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HealthFacilitySolidWasteManagementDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => HealthFacilitySolidWasteManagementDto),
    __metadata("design:type", HealthFacilitySolidWasteManagementDto)
], CreateHealthFacilitySubmissionDto.prototype, "solidWaste", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HealthFacilityLiquidWasteManagementDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => HealthFacilityLiquidWasteManagementDto),
    __metadata("design:type", HealthFacilityLiquidWasteManagementDto)
], CreateHealthFacilitySubmissionDto.prototype, "liquidWaste", void 0);
class HealthFacilitySubmissionResponseDto extends base_submission_dto_1.BaseSubmissionResponseDto {
    generalInfo;
    waterSupply;
    sanitation;
    hygiene;
    solidWaste;
    liquidWaste;
}
exports.HealthFacilitySubmissionResponseDto = HealthFacilitySubmissionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: HealthFacilityGeneralInfoDto }),
    __metadata("design:type", HealthFacilityGeneralInfoDto)
], HealthFacilitySubmissionResponseDto.prototype, "generalInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HealthFacilityWaterSupplyDto }),
    __metadata("design:type", HealthFacilityWaterSupplyDto)
], HealthFacilitySubmissionResponseDto.prototype, "waterSupply", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HealthFacilitySanitationDto }),
    __metadata("design:type", HealthFacilitySanitationDto)
], HealthFacilitySubmissionResponseDto.prototype, "sanitation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HealthFacilityHygieneDto }),
    __metadata("design:type", HealthFacilityHygieneDto)
], HealthFacilitySubmissionResponseDto.prototype, "hygiene", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HealthFacilitySolidWasteManagementDto }),
    __metadata("design:type", HealthFacilitySolidWasteManagementDto)
], HealthFacilitySubmissionResponseDto.prototype, "solidWaste", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HealthFacilityLiquidWasteManagementDto }),
    __metadata("design:type", HealthFacilityLiquidWasteManagementDto)
], HealthFacilitySubmissionResponseDto.prototype, "liquidWaste", void 0);
//# sourceMappingURL=health-facility.dto.js.map