import { SchoolType, SchoolManagement, SchoolTypeDayBoarding, WaterAvailabilityFrequency, CleanWaterStorageCapacity, MainWaterSource, WaterSourceDistance, ToiletFacilityType, FacilitySlabConstructionMaterial, ExcretaManagement, HandWashingFacilityType, HandWashingMaterial, WasteManagementAfterSeparation, WasteTreatmentType, WasteCollectionFrequency, WasteWaterManagement } from '@prisma/client';
import { BaseCreateSubmissionDto, BaseSubmissionResponseDto } from './base-submission.dto';
export declare class SchoolGeneralInfoDto {
    schoolName: string;
    schoolType: SchoolType;
    managementType: SchoolManagement;
    dayBoarding: SchoolTypeDayBoarding;
    totalStudents: number;
    femaleStudents: number;
    maleStudents: number;
    studentsWithDisabilities: number;
}
export declare class SchoolWaterSupplyDto {
    connectedToPipeline: boolean;
    waterAvailability: boolean;
    availableDays?: WaterAvailabilityFrequency;
    storageCapacity: CleanWaterStorageCapacity;
    mainWaterSource?: MainWaterSource;
    distanceToSource: WaterSourceDistance;
}
export declare class SchoolSanitationDto {
    toiletType: ToiletFacilityType;
    slabConstructionMaterial?: FacilitySlabConstructionMaterial;
    totalToilets: number;
    genderSeparation: boolean;
    femaleToilets: number;
    maleToilets: number;
    girlsRoom: boolean;
    disabilityAccess: boolean;
    staffToilets: boolean;
    hasToiletFullInLast2Years: boolean;
    excretaManagement?: ExcretaManagement;
}
export declare class SchoolHygieneDto {
    handwashingFacility: boolean;
    facilityType?: HandWashingFacilityType;
    handwashingMaterials?: HandWashingMaterial;
    handWashingfacilityNearToilet?: boolean;
    toiletHandWashingFacilityType?: HandWashingFacilityType;
    toiletHandWashingMaterials?: HandWashingMaterial;
}
export declare class SchoolSolidWasteManagementDto {
    wasteSeparation: boolean;
    wasteManagement?: WasteManagementAfterSeparation;
    treatmentType?: WasteTreatmentType;
    collectionFrequency?: WasteCollectionFrequency;
    collectionCost?: number;
}
export declare class SchoolLiquidWasteManagementDto {
    liquidWasteManagement: WasteWaterManagement;
}
export declare class CreateSchoolSubmissionDto extends BaseCreateSubmissionDto {
    generalInfo: SchoolGeneralInfoDto;
    waterSupply: SchoolWaterSupplyDto;
    sanitation: SchoolSanitationDto;
    hygiene: SchoolHygieneDto;
    solidWaste: SchoolSolidWasteManagementDto;
    liquidWaste: SchoolLiquidWasteManagementDto;
}
export declare class SchoolSubmissionResponseDto extends BaseSubmissionResponseDto {
    generalInfo: SchoolGeneralInfoDto;
    waterSupply: SchoolWaterSupplyDto;
    sanitation: SchoolSanitationDto;
    hygiene: SchoolHygieneDto;
    solidWaste: SchoolSolidWasteManagementDto;
    liquidWaste: SchoolLiquidWasteManagementDto;
}
