import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Animated,
  Dimensions,
  Pressable,
} from 'react-native';
import tw from 'twrnc';
import { useAuth } from '../../hooks/useAuth';
import { router } from 'expo-router';
import AppText from '@/components/ui/Text';
import AppButton from '@/components/ui/Button';
import { RadioButton } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { PRIMARY_COLOR } from '@/constants/colors';

const screenWidth = Dimensions.get('window').width;

export default function MainScreen() {
  const { user, logout } = useAuth();
  const [facilityType, setFacilityType] = useState('');
  const [drawerVisible, setDrawerVisible] = useState(false);
  const drawerAnim = useRef(new Animated.Value(screenWidth)).current;

  const openDrawer = () => {
    setDrawerVisible(true);
    Animated.timing(drawerAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  };

  const closeDrawer = () => {
    Animated.timing(drawerAnim, {
      toValue: screenWidth,
      duration: 300,
      useNativeDriver: false,
    }).start(() => setDrawerVisible(false));
  };

  const handleLogout = () => {
    closeDrawer();
    Alert.alert('Logout', 'Are you sure you want to logout?', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Logout',
        style: 'destructive',
        onPress: async () => {
          await logout();
          router.replace('/intro');
        },
      },
    ]);
  };

  const goToProfile = () => {
    closeDrawer();
    router.push('/profile');
  };

  useEffect(() => {
    if (!user) {
      router.replace('/intro');
    }
  }, [user]);

  return (
    <View style={tw`flex-1 bg-gray-50`}>
      <View style={tw`bg-[${PRIMARY_COLOR}] pt-12 pb-6 px-6 rounded-b-3xl h-48`}>
        <View style={tw`flex-row justify-between items-center`}>
          <AppText weight="bold" style={tw`text-white text-3xl`}>
            Hello, {user?.firstName}
          </AppText>
          <TouchableOpacity onPress={openDrawer}>
            <Ionicons name="person-circle-outline" size={50} color="#fff" />
          </TouchableOpacity>
        </View>
        <AppText style={tw`text-white text-sm mt-2`}>
          Let's start collecting data!
        </AppText>
      </View>

      {/* Content */}
      <ScrollView contentContainerStyle={tw`p-6`}>
        <AppText weight="bold" style={tw`text-2xl text-[${PRIMARY_COLOR}] mb-1`}>
          Facility Type
        </AppText>
        <AppText style={tw`text-gray-500 mb-4`}>
          Choose the facility you are going to collect the data for.
        </AppText>

        <RadioButton.Group
          onValueChange={(value) => setFacilityType(value)}
          value={facilityType}
        >
          {['Household', 'School', 'Health Facility', 'Public Place', 'Waste Collection & Transport Company', 'Waste Recovery Company', 'Waste Disposal Company'].map((label) => {
            const isSelected = facilityType === label;
            return (
              <TouchableOpacity
                key={label}
                onPress={() => setFacilityType(label)}
                activeOpacity={0.8}
                style={tw.style(
                  `flex-row items-center border border-gray-200 mb-3 px-4 py-3 rounded-xl`,
                  isSelected ? 'bg-blue-100 ' : 'bg-white',
                )}
              >
                <RadioButton value={label} />
                <AppText style={tw`text-sm text-gray-700 ml-2`}>{label}</AppText>
              </TouchableOpacity>
            );
          })}

        </RadioButton.Group>

        <View style={tw`mt-8`}>
          <AppButton
            title="Next"
            disabled={!facilityType}
            onPress={() => router.push({ pathname: '/facility/location', params: { type: facilityType } })}
          />
        </View>
      </ScrollView>

      {drawerVisible && (
        <Pressable
          onPress={closeDrawer}
          style={tw`absolute inset-0 bg-black bg-opacity-25`}
        />
      )}

      <Animated.View
        style={[
          tw`absolute top-0 right-0 h-full w-64 overflow-hidden rounded-l-xl`,
          { transform: [{ translateX: drawerAnim }] },
        ]}
      >
        <BlurView
          intensity={90}
          tint="light"
          experimentalBlurMethod='dimezisBlurView'
          style={tw`h-full w-full p-6`}
        >
          <Text style={tw`text-xl font-bold mb-4 text-black`}>Menu</Text>
          <TouchableOpacity onPress={goToProfile} style={tw`py-3`}>
            <Text style={tw`text-base text-blue-700`}>Profile</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={handleLogout} style={tw`py-3`}>
            <Text style={tw`text-base text-red-600`}>Logout</Text>
          </TouchableOpacity>
        </BlurView>
      </Animated.View>
    </View>
  );
}