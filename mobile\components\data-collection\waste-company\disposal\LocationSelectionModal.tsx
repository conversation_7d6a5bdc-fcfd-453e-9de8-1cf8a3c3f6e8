import React, { useEffect, useState } from 'react';
import { View, Modal, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { Picker } from '@react-native-picker/picker';
import tw from 'twrnc';
import AppText from '@/components/ui/Text';
import { TextInput, Button } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { PRIMARY_COLOR } from '@/constants/colors';
import {
  getCells,
  getDistricts,
  getProvinces,
  getSectors,
  getVillages,
} from '@/services/administrative';
import { Cell, District, Province, Sector, Village } from '@/types/administrative';

interface LocationSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (location: {
    villageId: number;
    villageName: string;
    fullAddress: string;
    latitude?: number;
    longitude?: number;
  }) => void;
  initialLocation?: {
    villageId?: number;
    latitude?: number;
    longitude?: number;
  };
}

const LocationSelectionModal = ({
  visible,
  onClose,
  onSelect,
  initialLocation,
}: LocationSelectionModalProps) => {
  const [provinces, setProvinces] = useState<Province[]>([]);
  const [districts, setDistricts] = useState<District[]>([]);
  const [sectors, setSectors] = useState<Sector[]>([]);
  const [cells, setCells] = useState<Cell[]>([]);
  const [villages, setVillages] = useState<Village[]>([]);

  const [selectedProvince, setSelectedProvince] = useState<number | undefined>();
  const [selectedDistrict, setSelectedDistrict] = useState<number | undefined>();
  const [selectedSector, setSelectedSector] = useState<number | undefined>();
  const [selectedCell, setSelectedCell] = useState<number | undefined>();
  const [selectedVillage, setSelectedVillage] = useState<number | undefined>();

  const [latitude, setLatitude] = useState<string>('');
  const [longitude, setLongitude] = useState<string>('');

  const [loading, setLoading] = useState({
    provinces: false,
    districts: false,
    sectors: false,
    cells: false,
    villages: false,
  });

  // Load provinces on mount
  useEffect(() => {
    if (visible) {
      loadProvinces();
      if (initialLocation) {
        setLatitude(initialLocation.latitude?.toString() || '');
        setLongitude(initialLocation.longitude?.toString() || '');
      }
    }
  }, [visible, initialLocation]);

  // Load districts when province changes
  useEffect(() => {
    if (selectedProvince) {
      loadDistricts(selectedProvince);
    } else {
      setDistricts([]);
      setSelectedDistrict(undefined);
    }
  }, [selectedProvince]);

  // Load sectors when district changes
  useEffect(() => {
    if (selectedDistrict) {
      loadSectors(selectedDistrict);
    } else {
      setSectors([]);
      setSelectedSector(undefined);
    }
  }, [selectedDistrict]);

  // Load cells when sector changes
  useEffect(() => {
    if (selectedSector) {
      loadCells(selectedSector);
    } else {
      setCells([]);
      setSelectedCell(undefined);
    }
  }, [selectedSector]);

  // Load villages when cell changes
  useEffect(() => {
    if (selectedCell) {
      loadVillages(selectedCell);
    } else {
      setVillages([]);
      setSelectedVillage(undefined);
    }
  }, [selectedCell]);

  const loadProvinces = async () => {
    setLoading((prev) => ({ ...prev, provinces: true }));
    try {
      const res = await getProvinces();
      setProvinces(res.data.provinces);
    } catch (error) {
      Alert.alert('Error', 'Failed to load provinces');
    } finally {
      setLoading((prev) => ({ ...prev, provinces: false }));
    }
  };

  const loadDistricts = async (provinceId: number) => {
    setLoading((prev) => ({ ...prev, districts: true }));
    try {
      const res = await getDistricts(provinceId);
      setDistricts(res.data.districts);
    } catch (error) {
      Alert.alert('Error', 'Failed to load districts');
    } finally {
      setLoading((prev) => ({ ...prev, districts: false }));
    }
  };

  const loadSectors = async (districtId: number) => {
    setLoading((prev) => ({ ...prev, sectors: true }));
    try {
      const res = await getSectors(districtId);
      setSectors(res.data.sectors);
    } catch (error) {
      Alert.alert('Error', 'Failed to load sectors');
    } finally {
      setLoading((prev) => ({ ...prev, sectors: false }));
    }
  };

  const loadCells = async (sectorId: number) => {
    setLoading((prev) => ({ ...prev, cells: true }));
    try {
      const res = await getCells(sectorId);
      setCells(res.data.cells);
    } catch (error) {
      Alert.alert('Error', 'Failed to load cells');
    } finally {
      setLoading((prev) => ({ ...prev, cells: false }));
    }
  };

  const loadVillages = async (cellId: number) => {
    setLoading((prev) => ({ ...prev, villages: true }));
    try {
      const res = await getVillages(cellId);
      setVillages(res.data.villages);
    } catch (error) {
      Alert.alert('Error', 'Failed to load villages');
    } finally {
      setLoading((prev) => ({ ...prev, villages: false }));
    }
  };

  const handleSelect = () => {
    if (!selectedVillage) {
      Alert.alert('Error', 'Please select a village');
      return;
    }

    const selectedVillageData = villages.find(v => v.id === selectedVillage);
    if (!selectedVillageData) {
      Alert.alert('Error', 'Selected village not found');
      return;
    }

    const fullAddress = `${selectedVillageData.name}, ${selectedVillageData.cell.name}, ${selectedVillageData.cell.sector.name}, ${selectedVillageData.cell.sector.district.name}, ${selectedVillageData.cell.sector.district.province.name}`;

    onSelect({
      villageId: selectedVillage,
      villageName: selectedVillageData.name,
      fullAddress,
      latitude: latitude ? parseFloat(latitude) : undefined,
      longitude: longitude ? parseFloat(longitude) : undefined,
    });
  };

  const resetForm = () => {
    setSelectedProvince(undefined);
    setSelectedDistrict(undefined);
    setSelectedSector(undefined);
    setSelectedCell(undefined);
    setSelectedVillage(undefined);
    setLatitude('');
    setLongitude('');
    setDistricts([]);
    setSectors([]);
    setCells([]);
    setVillages([]);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={tw`flex-1 bg-white`}>
        {/* Header */}
        <View style={tw`flex-row items-center justify-between p-4 border-b border-gray-200`}>
          <AppText weight="semibold" style={tw`text-lg`}>
            Select Facility Location
          </AppText>
          <TouchableOpacity onPress={handleClose}>
            <Ionicons name="close" size={24} color="gray" />
          </TouchableOpacity>
        </View>

        <ScrollView style={tw`flex-1 p-4`}>
          {/* Province Picker */}
          <View style={tw`mb-4`}>
            <AppText style={tw`mb-2 font-medium`}>Province *</AppText>
            <View style={tw`border border-gray-300 rounded-lg`}>
              <Picker
                selectedValue={selectedProvince?.toString() || ''}
                onValueChange={(val) => setSelectedProvince(val ? Number(val) : undefined)}
                enabled={!loading.provinces && provinces.length > 0}
              >
                <Picker.Item label="Select Province" value="" />
                {provinces.map((p) => (
                  <Picker.Item key={p.id} label={p.name} value={p.id.toString()} />
                ))}
              </Picker>
            </View>
          </View>

          {/* District Picker */}
          <View style={tw`mb-4`}>
            <AppText style={tw`mb-2 font-medium`}>District *</AppText>
            <View style={tw`border border-gray-300 rounded-lg ${!selectedProvince ? 'bg-gray-100' : ''}`}>
              <Picker
                selectedValue={selectedDistrict?.toString() || ''}
                onValueChange={(val) => setSelectedDistrict(val ? Number(val) : undefined)}
                enabled={!loading.districts && !!selectedProvince && districts.length > 0}
              >
                <Picker.Item label="Select District" value="" />
                {districts.map((d) => (
                  <Picker.Item key={d.id} label={d.name} value={d.id.toString()} />
                ))}
              </Picker>
            </View>
          </View>

          {/* Sector Picker */}
          <View style={tw`mb-4`}>
            <AppText style={tw`mb-2 font-medium`}>Sector *</AppText>
            <View style={tw`border border-gray-300 rounded-lg ${!selectedDistrict ? 'bg-gray-100' : ''}`}>
              <Picker
                selectedValue={selectedSector?.toString() || ''}
                onValueChange={(val) => setSelectedSector(val ? Number(val) : undefined)}
                enabled={!loading.sectors && !!selectedDistrict && sectors.length > 0}
              >
                <Picker.Item label="Select Sector" value="" />
                {sectors.map((s) => (
                  <Picker.Item key={s.id} label={s.name} value={s.id.toString()} />
                ))}
              </Picker>
            </View>
          </View>

          {/* Cell Picker */}
          <View style={tw`mb-4`}>
            <AppText style={tw`mb-2 font-medium`}>Cell *</AppText>
            <View style={tw`border border-gray-300 rounded-lg ${!selectedSector ? 'bg-gray-100' : ''}`}>
              <Picker
                selectedValue={selectedCell?.toString() || ''}
                onValueChange={(val) => setSelectedCell(val ? Number(val) : undefined)}
                enabled={!loading.cells && !!selectedSector && cells.length > 0}
              >
                <Picker.Item label="Select Cell" value="" />
                {cells.map((c) => (
                  <Picker.Item key={c.id} label={c.name} value={c.id.toString()} />
                ))}
              </Picker>
            </View>
          </View>

          {/* Village Picker */}
          <View style={tw`mb-4`}>
            <AppText style={tw`mb-2 font-medium`}>Village *</AppText>
            <View style={tw`border border-gray-300 rounded-lg ${!selectedCell ? 'bg-gray-100' : ''}`}>
              <Picker
                selectedValue={selectedVillage?.toString() || ''}
                onValueChange={(val) => setSelectedVillage(val ? Number(val) : undefined)}
                enabled={!loading.villages && !!selectedCell && villages.length > 0}
              >
                <Picker.Item label="Select Village" value="" />
                {villages.map((v) => (
                  <Picker.Item key={v.id} label={v.name} value={v.id.toString()} />
                ))}
              </Picker>
            </View>
          </View>

          {/* Coordinates */}
          <View style={tw`mb-4`}>
            <AppText style={tw`mb-2 font-medium`}>Coordinates (Optional)</AppText>
            <View style={tw`flex-row gap-2`}>
              <View style={tw`flex-1`}>
                <TextInput
                  mode="outlined"
                  label="Latitude"
                  value={latitude}
                  onChangeText={setLatitude}
                  keyboardType="numeric"
                  placeholder="-1.9441"
                  outlineColor="gray"
                />
              </View>
              <View style={tw`flex-1`}>
                <TextInput
                  mode="outlined"
                  label="Longitude"
                  value={longitude}
                  onChangeText={setLongitude}
                  keyboardType="numeric"
                  placeholder="30.0619"
                  outlineColor="gray"
                />
              </View>
            </View>
          </View>
        </ScrollView>

        {/* Footer */}
        <View style={tw`p-4 border-t border-gray-200`}>
          <View style={tw`flex-row gap-2`}>
            <Button
              mode="outlined"
              onPress={handleClose}
              style={tw`flex-1`}
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleSelect}
              style={tw`flex-1`}
              disabled={!selectedVillage}
              buttonColor={PRIMARY_COLOR}
              textColor='white'
            >
              Select Location
            </Button>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default LocationSelectionModal;
