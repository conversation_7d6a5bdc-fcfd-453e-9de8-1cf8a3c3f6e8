import { SchoolsListResponse, PaginationQuery, CreateSchoolInput, CreateSchoolResponse, School } from '@/types/facility';
import axios from '../../utils/axios';

export const getAllSchools = (query?: PaginationQuery) => {
    return axios.get<SchoolsListResponse>(`/facilities/schools`, { params: query });
};

export const createSchool = (data: CreateSchoolInput) => {
    return axios.post<CreateSchoolResponse>(`/facilities/schools`, data);
};

export const getSchool = (id: string) => {
    return axios.get<School>(`/facilities/schools/${id}`);
}

export const submitSchoolForm = (facilityId: string, data: any) => {
    return axios.post(`/submission/school`, {
        facilityId,
        facilityType: "SCHOOL",
        ...data
    });
};