import { ApiProperty } from "@nestjs/swagger";
import { FacilityType } from "@prisma/client";
import { IsEnum, IsOptional, IsString } from "class-validator";

export class IndicatorsRequestDto {
    @ApiProperty({
        description: "Administrative level to filter data by",
        enum: ["COUNTRY", "PROVINCE", "DISTRICT", "SECTOR", "CELL", "VILLAGE"],
    })
    @IsString()
    level: "COUNTRY" | "PROVINCE" | "DISTRICT" | "SECTOR" | "CELL" | "VILLAGE";

    @ApiProperty({ description: "ID of the selected administrative level", required: false })
    @IsString()
    @IsOptional()
    levelId?: string;

    @ApiProperty({ description: "Start date in YYYY-MM-DD format" })
    @IsString()
    startDate: string;

    @ApiProperty({ description: "End date in YYYY-MM-DD format" })
    @IsString()
    endDate: string;

    @ApiProperty({ enum: FacilityType, description: "Type of facility to filter indicators" })
    @IsEnum(FacilityType)
    facilityType: FacilityType;
}

export class IndicatorsResponseDto {
    @ApiProperty({ description: "Administrative level queried" })
    level: string;

    @ApiProperty({ description: "ID of the administrative level" })
    levelId: string;

    @ApiProperty({ description: "Start date of the data period" })
    startDate: string;

    @ApiProperty({ description: "End date of the data period" })
    endDate: string;

    @ApiProperty({ enum: FacilityType, description: "Facility type used for filtering" })
    facilityType: FacilityType;

    @ApiProperty({
        description: "List of calculated indicators",
        type: [Object],
    })
    indicators: any[];
}
