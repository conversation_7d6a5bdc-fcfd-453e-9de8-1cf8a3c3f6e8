import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { AuthService } from '../../../../services/auth.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil, finalize } from 'rxjs';

@Component({
  selector: 'app-setup-2fa',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './2fa.component.html'
})
export class Setup2FAComponent implements OnInit, OnDestroy {
  qrCode = '';
  manualEntryKey = '';
  twoFAForm: FormGroup;
  successMessage = '';
  errorMessage = '';
  recoveryCodes: string[] = [];
  isLoading = false;
  private destroy$ = new Subject<void>();

  constructor(
    private authService: AuthService,
    private fb: FormBuilder,
    private router: Router
  ) {
    this.twoFAForm = this.fb.group({
      code: ['', [Validators.required, Validators.pattern(/^[0-9]{6}$/)]]
    });
  }

  ngOnInit() {
    if (!this.authService.isAuthenticated()) {
      this.router.navigate(['/login']);
      return;
    }

    this.isLoading = true;
    this.authService.get2FASetup()
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe({
        next: (data) => {
          this.qrCode = data.qrCode;
          this.manualEntryKey = data.manualEntryKey;
          this.errorMessage = '';
        },
        error: (error) => {
          console.error('2FA setup error:', error);
          if (error.status === 401) {
            this.errorMessage = 'Authentication failed. Please log in again.';
            this.router.navigate(['/login']);
          } else if (error.status === 404) {
            this.errorMessage = 'Backend API not found. Is your server running?';
          } else if (error.status === 0) {
            this.errorMessage = 'Cannot connect to server. Is your backend running?';
          } else {
            this.errorMessage = `Failed to load 2FA setup info. Error: ${error.status} - ${error.message}`;
          }
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  enable2FA() {
    if (this.twoFAForm.invalid) {
      this.errorMessage = 'Please enter a valid 6-digit code.';
      return;
    }
    
    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.authService.enable2FA({ totpCode: this.twoFAForm.value.code })
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe({
        next: (data) => {
          this.successMessage = data.message || '2FA enabled successfully!';
          this.recoveryCodes = data.recoveryCodes || [];
          this.errorMessage = '';
          
          // Update user data in AuthService
          if (data.user) {
            localStorage.setItem('currentUser', JSON.stringify(data.user));
          }
          
          // Add a small delay to show the success message
          setTimeout(() => {
            this.router.navigate(['/dashboard']);
          }, 2000);
        },
        error: (error) => {
          console.error('Enable 2FA error:', error);
          if (error.status === 400) {
            this.errorMessage = 'Invalid code. Please try again.';
          } else if (error.status === 401) {
            this.errorMessage = 'Authentication failed. Please log in again.';
            this.router.navigate(['/login']);
          } else if (error.status === 0) {
            this.errorMessage = 'Cannot connect to server. Please check your internet connection.';
          } else {
            this.errorMessage = error?.error?.message || 'Failed to enable 2FA. Please try again.';
          }
        }
      });
  }
}