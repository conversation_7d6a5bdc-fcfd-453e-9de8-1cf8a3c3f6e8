import { HealthFacilityType, HealthFacilityManagement, DailyPatientVolume, WaterAvailabilityFrequency, CleanWaterStorageCapacity, MainWaterSource, WaterSourceDistance, ToiletFacilityType, FacilitySlabConstructionMaterial, ExcretaManagement, HandWashingFacilityType, HandWashingMaterial, WasteManagementAfterSeparation, WasteTreatmentType, WasteCollectionFrequency, WasteWaterManagement, WaterAvailability } from '@prisma/client';
import { BaseCreateSubmissionDto, BaseSubmissionResponseDto } from './base-submission.dto';
export declare class HealthFacilityGeneralInfoDto {
    facilityName: string;
    facilityType: HealthFacilityType;
    managementType: HealthFacilityManagement;
    dailyPatientVolume: DailyPatientVolume;
    totalStaff: number;
}
export declare class HealthFacilityWaterSupplyDto {
    connectedToPipeline: boolean;
    waterAvailability: WaterAvailability;
    availableDays?: WaterAvailabilityFrequency;
    storageCapacity: CleanWaterStorageCapacity;
    mainWaterSource?: MainWaterSource;
    distanceToSource: WaterSourceDistance;
}
export declare class HealthFacilitySanitationDto {
    toiletType: ToiletFacilityType;
    slabConstructionMaterial?: FacilitySlabConstructionMaterial;
    totalToilets: number;
    genderSeparation: boolean;
    femaleToilets: number;
    maleToilets: number;
    disabilityAccess: boolean;
    staffToilets: boolean;
    hasToiletFullInLast2Years: boolean;
    excretaManagement?: ExcretaManagement;
}
export declare class HealthFacilityHygieneDto {
    handwashingFacility: boolean;
    facilityType?: HandWashingFacilityType;
    handwashingMaterials?: HandWashingMaterial;
    handWashingfacilityNearToilet?: boolean;
    toiletHandWashingFacilityType?: HandWashingFacilityType;
    toiletHandWashingMaterials?: HandWashingMaterial;
}
export declare class HealthFacilitySolidWasteManagementDto {
    wasteSeparation: boolean;
    wasteManagement?: WasteManagementAfterSeparation;
    treatmentType?: WasteTreatmentType;
    collectionFrequency?: WasteCollectionFrequency;
    collectionCost?: number;
}
export declare class HealthFacilityLiquidWasteManagementDto {
    liquidWasteManagement: WasteWaterManagement;
}
export declare class CreateHealthFacilitySubmissionDto extends BaseCreateSubmissionDto {
    generalInfo: HealthFacilityGeneralInfoDto;
    waterSupply: HealthFacilityWaterSupplyDto;
    sanitation: HealthFacilitySanitationDto;
    hygiene: HealthFacilityHygieneDto;
    solidWaste: HealthFacilitySolidWasteManagementDto;
    liquidWaste: HealthFacilityLiquidWasteManagementDto;
}
export declare class HealthFacilitySubmissionResponseDto extends BaseSubmissionResponseDto {
    generalInfo: HealthFacilityGeneralInfoDto;
    waterSupply: HealthFacilityWaterSupplyDto;
    sanitation: HealthFacilitySanitationDto;
    hygiene: HealthFacilityHygieneDto;
    solidWaste: HealthFacilitySolidWasteManagementDto;
    liquidWaste: HealthFacilityLiquidWasteManagementDto;
}
