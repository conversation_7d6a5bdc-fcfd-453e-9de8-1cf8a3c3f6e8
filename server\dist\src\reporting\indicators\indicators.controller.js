"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndicatorsController = void 0;
const common_1 = require("@nestjs/common");
const indicators_service_1 = require("./indicators.service");
const swagger_1 = require("@nestjs/swagger");
const indicator_dto_1 = require("./dto/indicator.dto");
let IndicatorsController = class IndicatorsController {
    indicatorsService;
    constructor(indicatorsService) {
        this.indicatorsService = indicatorsService;
    }
    async getBasicIndicators(query) {
        return await this.indicatorsService.getBasicIndicators(query);
    }
    async getGeoIndicatorMapping(query) {
        return await this.indicatorsService.getGeoIndicatorMapping(query);
    }
};
exports.IndicatorsController = IndicatorsController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [indicator_dto_1.IndicatorsRequestDto]),
    __metadata("design:returntype", Promise)
], IndicatorsController.prototype, "getBasicIndicators", null);
__decorate([
    (0, common_1.Get)('geo-mapping'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [indicator_dto_1.GeoMappingRequestDto]),
    __metadata("design:returntype", Promise)
], IndicatorsController.prototype, "getGeoIndicatorMapping", null);
exports.IndicatorsController = IndicatorsController = __decorate([
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('indicators'),
    __metadata("design:paramtypes", [indicators_service_1.IndicatorsService])
], IndicatorsController);
//# sourceMappingURL=indicators.controller.js.map