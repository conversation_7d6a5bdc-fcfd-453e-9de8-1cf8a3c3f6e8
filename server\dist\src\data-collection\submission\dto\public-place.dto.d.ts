import { PublicPlaceCategory, PublicPlaceOpeningDays, WaterAvailability, WaterAvailabilityFrequency, CleanWaterStorageCapacity, MainWaterSource, WaterSourceDistance, ToiletFacilityType, FacilitySlabConstructionMaterial, ExcretaManagement, HandWashingFacilityType, HandWashingMaterial, PublicPlaceHandWashingMaterial, WasteManagementAfterSeparation, WasteTreatmentType, WasteCollectionFrequency, WasteWaterManagement } from '@prisma/client';
import { BaseCreateSubmissionDto, BaseSubmissionResponseDto } from './base-submission.dto';
export declare class PublicPlaceGeneralInfoDto {
    category: PublicPlaceCategory;
    openingDays: PublicPlaceOpeningDays;
}
export declare class PublicPlaceWaterSupplyDto {
    connectedToPipeline: boolean;
    waterAvailability: WaterAvailability;
    availableDays?: WaterAvailabilityFrequency;
    storageCapacity: CleanWaterStorageCapacity;
    mainWaterSource?: MainWaterSource;
    distanceToSource: WaterSourceDistance;
}
export declare class PublicPlaceSanitationDto {
    toiletType: ToiletFacilityType;
    slabConstructionMaterial?: FacilitySlabConstructionMaterial;
    totalToilets: number;
    genderSeparation: boolean;
    femaleToilets: number;
    maleToilets: number;
    disabilityAccess: boolean;
    hasToiletFullInLast2Years: boolean;
    excretaManagement?: ExcretaManagement;
}
export declare class PublicPlaceHygieneDto {
    handwashingFacility: boolean;
    facilityType?: HandWashingFacilityType;
    handwashingMaterials?: PublicPlaceHandWashingMaterial;
    handWashingfacilityNearToilet?: boolean;
    toiletHandWashingFacilityType?: HandWashingFacilityType;
    toiletHandWashingMaterials?: HandWashingMaterial;
}
export declare class PublicPlaceSolidWasteManagementDto {
    wasteSeparation: boolean;
    wasteManagement?: WasteManagementAfterSeparation;
    treatmentType?: WasteTreatmentType;
    collectionFrequency?: WasteCollectionFrequency;
    collectionCost?: number;
}
export declare class PublicPlaceLiquidWasteManagementDto {
    liquidWasteManagement: WasteWaterManagement;
}
export declare class CreatePublicPlaceSubmissionDto extends BaseCreateSubmissionDto {
    generalInfo: PublicPlaceGeneralInfoDto;
    waterSupply: PublicPlaceWaterSupplyDto;
    sanitation: PublicPlaceSanitationDto;
    hygiene: PublicPlaceHygieneDto;
    solidWaste: PublicPlaceSolidWasteManagementDto;
    liquidWaste: PublicPlaceLiquidWasteManagementDto;
}
export declare class PublicPlaceSubmissionResponseDto extends BaseSubmissionResponseDto {
    generalInfo: PublicPlaceGeneralInfoDto;
    waterSupply: PublicPlaceWaterSupplyDto;
    sanitation: PublicPlaceSanitationDto;
    hygiene: PublicPlaceHygieneDto;
    solidWaste: PublicPlaceSolidWasteManagementDto;
    liquidWaste: PublicPlaceLiquidWasteManagementDto;
}
