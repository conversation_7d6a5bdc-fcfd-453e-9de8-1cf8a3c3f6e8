import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Controller, useFormContext } from 'react-hook-form';
import { Picker } from '@react-native-picker/picker';
import tw from 'twrnc';
import AppText from '@/components/ui/Text';
import { TextInput, RadioButton } from 'react-native-paper';
import { CompactionFrequency, TruckFrequency, RecordingMethod } from '@/types/enums';

interface OperationalPracticesSectionProps {
  control: any;
  errors: any;
  watch: any;
}

const OperationalPracticesSection = ({ control, errors, watch }: OperationalPracticesSectionProps) => {
  const { setValue } = useFormContext();
  const weighbridge = watch('operationalPractices.weighbridge');
  const recordingMethod = watch('operationalPractices.recordingMethod');

  // Helper function to handle weighbridge change
  const handleWeighbridgeChange = (value: boolean) => {
    setValue('operationalPractices.weighbridge', value);

    // If weighbridge is true, clear the recording method and other recording method
    if (value === true) {
      setValue('operationalPractices.recordingMethod', null);
      setValue('operationalPractices.otherRecordingMethod', '');
    }
  };

  // Helper function to handle recording method change
  const handleRecordingMethodChange = (value: RecordingMethod | null) => {
    setValue('operationalPractices.recordingMethod', value);

    // If not "OTHER", clear the other recording method field
    if (value !== RecordingMethod.OTHER) {
      setValue('operationalPractices.otherRecordingMethod', '');
    }
  };

  return (
    <View style={tw`gap-4`}>
      {/* Boundary Control */}
      <View>
        <AppText style={tw`mb-2`}>
          Is there a physical boundary surrounding the site and supervised access control 24/7?
        </AppText>
        <Controller
          control={control}
          name="operationalPractices.boundaryControl"
          render={({ field: { onChange, value } }) => (
            <View style={tw`gap-2`}>
              <TouchableOpacity
                style={tw`flex-row items-center gap-2`}
                onPress={() => onChange(true)}
              >
                <RadioButton
                  value="true"
                  status={value === true ? 'checked' : 'unchecked'}
                  onPress={() => onChange(true)}
                />
                <AppText>Yes</AppText>
              </TouchableOpacity>
              <TouchableOpacity
                style={tw`flex-row items-center gap-2`}
                onPress={() => onChange(false)}
              >
                <RadioButton
                  value="false"
                  status={value === false ? 'checked' : 'unchecked'}
                  onPress={() => onChange(false)}
                />
                <AppText>No</AppText>
              </TouchableOpacity>
            </View>
          )}
        />
        {errors.operationalPractices?.boundaryControl && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.operationalPractices.boundaryControl.message}
          </AppText>
        )}
      </View>

      {/* Waste Deposit Control */}
      <View>
        <AppText style={tw`mb-2`}>
          Is waste deposited in clearly defined operational areas with strict management control?
        </AppText>
        <Controller
          control={control}
          name="operationalPractices.wasteDepositControl"
          render={({ field: { onChange, value } }) => (
            <View style={tw`gap-2`}>
              <TouchableOpacity
                style={tw`flex-row items-center gap-2`}
                onPress={() => onChange(true)}
              >
                <RadioButton
                  value="true"
                  status={value === true ? 'checked' : 'unchecked'}
                  onPress={() => onChange(true)}
                />
                <AppText>Yes</AppText>
              </TouchableOpacity>
              <TouchableOpacity
                style={tw`flex-row items-center gap-2`}
                onPress={() => onChange(false)}
              >
                <RadioButton
                  value="false"
                  status={value === false ? 'checked' : 'unchecked'}
                  onPress={() => onChange(false)}
                />
                <AppText>No</AppText>
              </TouchableOpacity>
            </View>
          )}
        />
        {errors.operationalPractices?.wasteDepositControl && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.operationalPractices.wasteDepositControl.message}
          </AppText>
        )}
      </View>

      {/* Compaction Frequency */}
      <View>
        <AppText style={tw`mb-2`}>How often is the waste layered and compacted?</AppText>
        <View style={tw`border border-gray-300 rounded-lg`}>
          <Controller
            control={control}
            name="operationalPractices.compactionFrequency"
            render={({ field: { onChange, value } }) => (
              <Picker
                selectedValue={value}
                onValueChange={onChange}
              >
                <Picker.Item label="Select frequency" value="" />
                <Picker.Item label="Daily" value={CompactionFrequency.DAILY} />
                <Picker.Item label="Weekly" value={CompactionFrequency.WEEKLY} />
                <Picker.Item label="Monthly" value={CompactionFrequency.MONTHLY} />
                <Picker.Item label="Annually" value={CompactionFrequency.ANNUALLY} />
                <Picker.Item label="Emergency only" value={CompactionFrequency.EMERGENCY_ONLY} />
                <Picker.Item label="Never" value={CompactionFrequency.NEVER} />
              </Picker>
            )}
          />
        </View>
        {errors.operationalPractices?.compactionFrequency && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.operationalPractices.compactionFrequency.message}
          </AppText>
        )}
      </View>

      {/* Waste Burning */}
      <View>
        <AppText style={tw`mb-2`}>
          Is there evidence of burning of waste on the surface of the landfill? (observation of data collector)
        </AppText>
        <Controller
          control={control}
          name="operationalPractices.wasteBurning"
          render={({ field: { onChange, value } }) => (
            <View style={tw`gap-2`}>
              <TouchableOpacity
                style={tw`flex-row items-center gap-2`}
                onPress={() => onChange(true)}
              >
                <RadioButton
                  value="true"
                  status={value === true ? 'checked' : 'unchecked'}
                  onPress={() => onChange(true)}
                />
                <AppText>Yes</AppText>
              </TouchableOpacity>
              <TouchableOpacity
                style={tw`flex-row items-center gap-2`}
                onPress={() => onChange(false)}
              >
                <RadioButton
                  value="false"
                  status={value === false ? 'checked' : 'unchecked'}
                  onPress={() => onChange(false)}
                />
                <AppText>No</AppText>
              </TouchableOpacity>
            </View>
          )}
        />
        {errors.operationalPractices?.wasteBurning && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.operationalPractices.wasteBurning.message}
          </AppText>
        )}
      </View>

      {/* Weighbridge */}
      <View>
        <AppText style={tw`mb-2`}>
          Does the site have a functional weighbridge in use, recording waste quantities by waste types?
        </AppText>
        <Controller
          control={control}
          name="operationalPractices.weighbridge"
          render={({ field: { value } }) => (
            <View style={tw`gap-2`}>
              <TouchableOpacity
                style={tw`flex-row items-center gap-2`}
                onPress={() => handleWeighbridgeChange(true)}
              >
                <RadioButton
                  value="true"
                  status={value === true ? 'checked' : 'unchecked'}
                  onPress={() => handleWeighbridgeChange(true)}
                />
                <AppText>Yes</AppText>
              </TouchableOpacity>
              <TouchableOpacity
                style={tw`flex-row items-center gap-2`}
                onPress={() => handleWeighbridgeChange(false)}
              >
                <RadioButton
                  value="false"
                  status={value === false ? 'checked' : 'unchecked'}
                  onPress={() => handleWeighbridgeChange(false)}
                />
                <AppText>No</AppText>
              </TouchableOpacity>
            </View>
          )}
        />
        {errors.operationalPractices?.weighbridge && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.operationalPractices.weighbridge.message}
          </AppText>
        )}
      </View>

      {/* Waste Amount */}
      <View>
        <AppText style={tw`mb-2`}>What is the estimated amount of waste delivered daily to the landfill site? (kg/daily)</AppText>
        <Controller
          control={control}
          name="operationalPractices.wasteAmount"
          render={({ field: { onChange, value } }) => (
            <TextInput
              mode="outlined"
              value={value?.toString() || ''}
              onChangeText={(text) => onChange(text ? parseFloat(text) : '')}
              placeholder="Enter daily waste amount in kg"
              keyboardType="numeric"
              error={!!errors.operationalPractices?.wasteAmount}
              outlineColor="gray"
            />
          )}
        />
        {errors.operationalPractices?.wasteAmount && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.operationalPractices.wasteAmount.message}
          </AppText>
        )}
      </View>

      {/* Truck Frequency */}
      <View>
        <AppText style={tw`mb-2`}>How often trucks are received on this dumpsite/landfill?</AppText>
        <View style={tw`border border-gray-300 rounded-lg`}>
          <Controller
            control={control}
            name="operationalPractices.truckFrequency"
            render={({ field: { onChange, value } }) => (
              <Picker
                selectedValue={value}
                onValueChange={onChange}
              >
                <Picker.Item label="Select frequency" value="" />
                <Picker.Item label="Daily" value={TruckFrequency.DAILY} />
                <Picker.Item label="Weekly" value={TruckFrequency.WEEKLY} />
                <Picker.Item label="Other" value={TruckFrequency.OTHER} />
              </Picker>
            )}
          />
        </View>
        {errors.operationalPractices?.truckFrequency && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.operationalPractices.truckFrequency.message}
          </AppText>
        )}
      </View>

      {/* Recording Method */}
      {weighbridge === false && (
        <View>
          <AppText style={tw`mb-2`}>
            How do you record amount of waste or trips? (for those who responded No to the weighbridge question)
          </AppText>
          <View style={tw`border border-gray-300 rounded-lg`}>
            <Controller
              control={control}
              name="operationalPractices.recordingMethod"
              render={({ field: { value } }) => (
                <Picker
                  selectedValue={value}
                  onValueChange={handleRecordingMethodChange}
                >
                  <Picker.Item label="Select recording method" value="" />
                  <Picker.Item
                    label="Paper logbook"
                    value={RecordingMethod.PAPER_LOGBOOK}
                  />
                  <Picker.Item
                    label="Others, specify"
                    value={RecordingMethod.OTHER}
                  />
                </Picker>
              )}
            />
          </View>
          {errors.operationalPractices?.recordingMethod && (
            <AppText style={tw`text-red-500 text-sm mt-1`}>
              {errors.operationalPractices.recordingMethod.message}
            </AppText>
          )}
        </View>
      )}

      {/* Other Recording Method */}
      {weighbridge === false && recordingMethod === RecordingMethod.OTHER && (
        <View>
          <AppText style={tw`mb-2`}>Please specify other recording method</AppText>
          <Controller
            control={control}
            name="operationalPractices.otherRecordingMethod"
            render={({ field: { onChange, value } }) => (
              <TextInput
                mode="outlined"
                value={value || ''}
                onChangeText={onChange}
                placeholder="Specify other recording method"
                error={!!errors.operationalPractices?.otherRecordingMethod}
                outlineColor="gray"
              />
            )}
          />
          {errors.operationalPractices?.otherRecordingMethod && (
            <AppText style={tw`text-red-500 text-sm mt-1`}>
              {errors.operationalPractices.otherRecordingMethod.message}
            </AppText>
          )}
        </View>
      )}
    </View>
  );
};

export default OperationalPracticesSection;
