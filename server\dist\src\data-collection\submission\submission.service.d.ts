import { CreateHouseholdSubmissionDto } from './dto/household.dto';
import { CreateSchoolSubmissionDto } from './dto/school.dto';
import { CreateHealthFacilitySubmissionDto } from './dto/health-facility.dto';
import { CreatePublicPlaceSubmissionDto } from './dto/public-place.dto';
import { BaseCreateSubmissionResponseDto } from './dto/base-submission.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateWasteCollectionCompanySubmissionDto, WasteCollectionCompanySubmissionResponseDto } from './dto/waste-collection-company.dto';
import { CreateWasteRecoveryCompanySubmissionDto, WasteRecoveryCompanySubmissionResponseDto } from './dto/waste-recovery-company.dto';
import { CreateWasteDisposalCompanySubmissionDto, WasteDisposalCompanySubmissionResponseDto } from './dto/waste-disposal-company.dto';
export declare class SubmissionService {
    private prisma;
    constructor(prisma: PrismaService);
    createHouseholdSubmission(dto: CreateHouseholdSubmissionDto, userId: string): Promise<BaseCreateSubmissionResponseDto>;
    createSchoolSubmission(dto: CreateSchoolSubmissionDto, userId: string): Promise<{
        message: string;
        submission: {
            id: string;
            facilityType: import("@prisma/client").$Enums.FacilityType;
            submittedAt: Date;
            submittedById: string;
            houseHoldId: string | null;
            schoolId: string | null;
            healthFacilityId: string | null;
            publicPlaceId: string | null;
        };
    }>;
    createHealthFacilitySubmission(dto: CreateHealthFacilitySubmissionDto, userId: string): Promise<{
        message: string;
        submission: {
            id: string;
            facilityType: import("@prisma/client").$Enums.FacilityType;
            submittedAt: Date;
            submittedById: string;
            houseHoldId: string | null;
            schoolId: string | null;
            healthFacilityId: string | null;
            publicPlaceId: string | null;
        };
    }>;
    createPublicPlaceSubmission(dto: CreatePublicPlaceSubmissionDto, userId: string): Promise<{
        message: string;
        submission: {
            id: string;
            facilityType: import("@prisma/client").$Enums.FacilityType;
            submittedAt: Date;
            submittedById: string;
            houseHoldId: string | null;
            schoolId: string | null;
            healthFacilityId: string | null;
            publicPlaceId: string | null;
        };
    }>;
    createWasteCollectionCompanySubmission(dto: CreateWasteCollectionCompanySubmissionDto, userId: string): Promise<WasteCollectionCompanySubmissionResponseDto>;
    createWasteRecoveryCompanySubmission(dto: CreateWasteRecoveryCompanySubmissionDto, userId: string): Promise<WasteRecoveryCompanySubmissionResponseDto>;
    createWasteDisposalCompanySubmission(dto: CreateWasteDisposalCompanySubmissionDto, userId: string): Promise<WasteDisposalCompanySubmissionResponseDto>;
}
