import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum, IsInt, IsArray, IsOptional, ValidateNested, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';
import { ServiceProviderType, OperationType } from '@prisma/client';

export class HandledMaterialDto {
  @ApiProperty({ example: 'Plastic bottles' })
  @IsString()
  materialName: string;

  @ApiProperty({ example: 'Local suppliers' })
  @IsString()
  supplier: string;

  @ApiProperty({ example: 500.5 })
  @IsNumber()
  quantityPerDay: number;
}

export class BusinessSiteDto {
  @ApiProperty({ example: 'Main Processing Site' })
  @IsString()
  name: string;

  @ApiProperty({ example: 'Processing facility' })
  @IsString()
  type: string;

  @ApiProperty({ example: 123 })
  @IsInt()
  villageId: number;

  @ApiProperty({ example: -1.9441, required: false })
  @IsOptional()
  @IsNumber()
  latitude?: number;

  @ApiProperty({ example: 30.0619, required: false })
  @IsOptional()
  @IsNumber()
  longitude?: number;
}

export class BusinessSiteResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  type: string;

  @ApiProperty()
  locationId: string;

  @ApiProperty()
  location: {
    id: string;
    latitude?: number;
    longitude?: number;
    villageId: number;
  };
}

export class CreateWasteRecoveryCompanySubmissionDto {
  @ApiProperty({ example: 'EcoRecycle Recovery Ltd' })
  @IsString()
  companyName: string;

  @ApiProperty({ example: 'Jane Smith' })
  @IsString()
  contactPerson: string;

  @ApiProperty({ example: '+250788654321' })
  @IsString()
  contactPhone: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsString()
  contactEmail: string;

  @ApiProperty({ enum: ServiceProviderType })
  @IsEnum(ServiceProviderType)
  companyType: ServiceProviderType;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  otherCompanyType?: string;

  @ApiProperty({ example: 30 })
  @IsInt()
  totalPersonnel: number;

  @ApiProperty({ example: 12 })
  @IsInt()
  femalePersonnel: number;

  @ApiProperty({ example: 18 })
  @IsInt()
  malePersonnel: number;

  @ApiProperty({ enum: OperationType })
  @IsEnum(OperationType)
  operationType: OperationType;

  @ApiProperty({ type: [HandledMaterialDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => HandledMaterialDto)
  handledMaterials: HandledMaterialDto[];

  @ApiProperty({ type: [BusinessSiteDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BusinessSiteDto)
  businessSites: BusinessSiteDto[];
}

export class WasteRecoveryCompanySubmissionResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  submissionId: string;

  @ApiProperty()
  submittedAt: Date;

  @ApiProperty()
  companyName: string;

  @ApiProperty()
  contactPerson: string;

  @ApiProperty()
  contactPhone: string;

  @ApiProperty()
  contactEmail: string;

  @ApiProperty({ enum: ServiceProviderType })
  companyType: ServiceProviderType;

  @ApiProperty({ required: false })
  otherCompanyType?: string;

  @ApiProperty()
  totalPersonnel: number;

  @ApiProperty()
  femalePersonnel: number;

  @ApiProperty()
  malePersonnel: number;

  @ApiProperty({ enum: OperationType })
  operationType: OperationType;

  @ApiProperty({ type: [HandledMaterialDto] })
  handledMaterials: HandledMaterialDto[];

  @ApiProperty({ type: [BusinessSiteResponseDto] })
  businessSites: BusinessSiteResponseDto[];
}