import React from 'react';
import { View } from 'react-native';
import { Controller } from 'react-hook-form';
import { Picker } from '@react-native-picker/picker';
import tw from 'twrnc';
import AppText from '@/components/ui/Text';
import { TextInput } from 'react-native-paper';
import { ServiceProviderType } from '@/types/enums';

interface ManagementCapacitySectionProps {
  control: any;
  errors: any;
  watch: any;
}

const ManagementCapacitySection = ({ control, errors, watch }: ManagementCapacitySectionProps) => {
  const companyType = watch('managementCapacity.companyType');

  return (
    <View style={tw`gap-4`}>
      {/* Service Provider Type */}
      <View>
        <AppText style={tw`mb-2`}>Type of Service Provider</AppText>
        <View style={tw`border border-gray-300 rounded-lg`}>
          <Controller
            control={control}
            name="managementCapacity.companyType"
            render={({ field: { onChange, value } }) => (
              <Picker
                selectedValue={value}
                onValueChange={onChange}
              >
                <Picker.Item label="Select service provider type" value="" />
                <Picker.Item 
                  label="Municipal/public operator (waste management department in the city)" 
                  value={ServiceProviderType.MUNICIPAL_PUBLIC_OPERATOR} 
                />
                <Picker.Item 
                  label="Private service contractor" 
                  value={ServiceProviderType.PRIVATE_SERVICE_CONTRACTOR} 
                />
                <Picker.Item 
                  label="NGO/CSOs" 
                  value={ServiceProviderType.NGO_CSOS} 
                />
                <Picker.Item 
                  label="Other (please specify)" 
                  value={ServiceProviderType.OTHER} 
                />
              </Picker>
            )}
          />
        </View>
        {errors.managementCapacity?.companyType && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.managementCapacity.companyType.message}
          </AppText>
        )}
      </View>

      {/* Other Company Type */}
      {companyType === ServiceProviderType.OTHER && (
        <View>
          <AppText style={tw`mb-2`}>Please specify other type</AppText>
          <Controller
            control={control}
            name="managementCapacity.otherCompanyType"
            render={({ field: { onChange, value } }) => (
              <TextInput
                mode="outlined"
                value={value || ''}
                onChangeText={onChange}
                placeholder="Specify other company type"
                error={!!errors.managementCapacity?.otherCompanyType}
                outlineColor="gray"
              />
            )}
          />
          {errors.managementCapacity?.otherCompanyType && (
            <AppText style={tw`text-red-500 text-sm mt-1`}>
              {errors.managementCapacity.otherCompanyType.message}
            </AppText>
          )}
        </View>
      )}

      {/* Total Personnel */}
      <View>
        <AppText style={tw`mb-2`}>Total number of personnel working for the SP</AppText>
        <Controller
          control={control}
          name="managementCapacity.totalPersonnel"
          render={({ field: { onChange, value } }) => (
            <TextInput
              mode="outlined"
              value={value?.toString() || ''}
              onChangeText={(text) => onChange(text ? parseInt(text) : '')}
              placeholder="Enter total number of personnel"
              keyboardType="numeric"
              error={!!errors.managementCapacity?.totalPersonnel}
              outlineColor="gray"
            />
          )}
        />
        {errors.managementCapacity?.totalPersonnel && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.managementCapacity.totalPersonnel.message}
          </AppText>
        )}
      </View>

      {/* Female Personnel */}
      <View>
        <AppText style={tw`mb-2`}>Number of females working for the SP</AppText>
        <Controller
          control={control}
          name="managementCapacity.femalePersonnel"
          render={({ field: { onChange, value } }) => (
            <TextInput
              mode="outlined"
              value={value?.toString() || ''}
              onChangeText={(text) => onChange(text ? parseInt(text) : '')}
              placeholder="Enter number of female personnel"
              keyboardType="numeric"
              error={!!errors.managementCapacity?.femalePersonnel}
              outlineColor="gray"
            />
          )}
        />
        {errors.managementCapacity?.femalePersonnel && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.managementCapacity.femalePersonnel.message}
          </AppText>
        )}
      </View>

      {/* Male Personnel */}
      <View>
        <AppText style={tw`mb-2`}>Number of males working for the SP</AppText>
        <Controller
          control={control}
          name="managementCapacity.malePersonnel"
          render={({ field: { onChange, value } }) => (
            <TextInput
              mode="outlined"
              value={value?.toString() || ''}
              onChangeText={(text) => onChange(text ? parseInt(text) : '')}
              placeholder="Enter number of male personnel"
              keyboardType="numeric"
              error={!!errors.managementCapacity?.malePersonnel}
              outlineColor="gray"
            />
          )}
        />
        {errors.managementCapacity?.malePersonnel && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.managementCapacity.malePersonnel.message}
          </AppText>
        )}
      </View>
    </View>
  );
};

export default ManagementCapacitySection;
