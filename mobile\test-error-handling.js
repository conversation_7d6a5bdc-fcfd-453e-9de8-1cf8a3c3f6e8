// Test script to verify error message handling
// This can be run in a Node.js environment to test the logic

function handleErrorMessage(message) {
  let errorMessage = 'Default error message';
  if (message) {
    if (Array.isArray(message)) {
      errorMessage = message.join(', ');
    } else {
      errorMessage = message;
    }
  }
  return errorMessage;
}

// Test cases
console.log('Testing error message handling:');

// Test with string message
const stringMessage = 'Invalid TOTP code';
console.log('String message:', handleErrorMessage(stringMessage));

// Test with array message
const arrayMessage = ['Invalid TOTP code', 'Code has expired'];
console.log('Array message:', handleErrorMessage(arrayMessage));

// Test with null/undefined message
console.log('Null message:', handleErrorMessage(null));
console.log('Undefined message:', handleErrorMessage(undefined));

// Test with empty array
console.log('Empty array:', handleErrorMessage([]));
