import AppText from '@/components/ui/Text';
import { PRIMARY_COLOR } from '@/constants/colors';
import { publicPlaceSchema } from '@/lib/forms/publicPlace';
import { Ionicons } from '@expo/vector-icons';
import { yupResolver } from '@hookform/resolvers/yup';
import React, { useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { Alert, ScrollView, View } from 'react-native';
import { Button } from 'react-native-paper';
import tw from 'twrnc';
import GeneralInfoSection from './public-place/GeneralInformation';
import HygieneSection from './public-place/Hygiene';
import LiquidWasteSection from './public-place/LiquidWaste';
import SanitationSection from './public-place/Sanitation';
import SolidWasteSection from './public-place/SolidWaste';
import WaterSupplySection from './public-place/WaterSupply';

const steps = [
    'General Information',
    'Water Supply',
    'Sanitation',
    'Hygiene',
    'Solid Waste Management',
    'Liquid Waste Management',
];

const initialValues = {
    generalInfo: {
        category: null,
        openingDays: null,
    },
    waterSupply: {
        connectedToPipeline: false,
        waterAvailability: null,
        availableDays: null,
        storageCapacity: null,
        mainWaterSource: null,
        distanceToSource: null,
    },
    sanitation: {
        toiletType: null,
        totalToilets: null,
        genderSeparation: false,
        femaleToilets: null,
        maleToilets: null,
        disabilityAccess: false,
        hasToiletFullInLast2Years: false,
        excretaManagement: null,
    },
    hygiene: {
        handwashingFacility: false,
        facilityType: null,
        handwashingMaterials: null,
        handWashingfacilityNearToilet: false,
        toiletHandWashingFacilityType: null,
        toiletHandWashingMaterials: null,
    },
    solidWaste: {
        wasteSeparation: false,
        wasteManagement: null,
        treatmentType: null,
        collectionFrequency: null,
        collectionCost: 0,
    },
    liquidWaste: {
        liquidWasteManagement: null,
    },
};

const sectionComponents = [
    GeneralInfoSection,
    WaterSupplySection,
    SanitationSection,
    HygieneSection,
    SolidWasteSection,
    LiquidWasteSection,
];

interface PublicPlaceFormProps {
    data: any;
    onSubmit: (data: any) => void;
}

const PublicPlaceForm = ({ onSubmit, data }: PublicPlaceFormProps) => {
    const [step, setStep] = useState<number>(0);

    const form = useForm({
        defaultValues: initialValues,
        mode: 'onChange',
        reValidateMode: 'onChange',
        resolver: yupResolver(publicPlaceSchema) as any,
    });

    const {
        control,
        handleSubmit,
        formState: { errors, isSubmitting },
        trigger,
        reset,
    } = form;

    useEffect(() => {
        if (data) {
            reset({
                ...initialValues,
                generalInfo: {
                    ...initialValues.generalInfo,
                    // Note: Public places don't have a name field in the schema
                    // but we can add it if needed in the future
                },
            });
        }
    }, [data]);

    const CurrentSection = sectionComponents[step];

    const sectionKeys = [
        'generalInfo',
        'waterSupply',
        'sanitation',
        'hygiene',
        'solidWaste',
        'liquidWaste',
    ];

    const handleNext = async () => {
        const valid = await trigger(sectionKeys[step] as any, { shouldFocus: true });
        if (valid) setStep(step + 1);
    };

    const handleBack = () => setStep(step - 1);

    const handleConfirmSubmit = (data: any) => {
        Alert.alert(
            'Confirm Submission',
            'Are you sure you want to submit the data?',
            [
                { text: 'Cancel', style: 'cancel' },
                { text: 'OK', onPress: () => onSubmit(data) },
            ]
        );
    };

    const progress = ((step + 1) / steps.length) * 100;

    return (
        <FormProvider {...form}>
            <View style={tw`flex-1`}>
                {/* Header */}
                <View style={tw`mb-4 px-4 pt-4`}>
                    <AppText weight='bold' style={tw`text-gray-400`}>Public Place WASH Information</AppText>
                    <View style={tw`flex-row gap-4 pt-2 items-center`}>
                        <View style={tw`w-[90%] h-2 bg-gray-200 rounded-full overflow-hidden`}>
                            <View style={[tw`h-full bg-[${PRIMARY_COLOR}]`, { width: `${progress}%` }]} />
                        </View>
                        <AppText style={tw`text-sm text-gray-400`}>{Math.round(progress)}%</AppText>
                    </View>
                    <AppText weight="semibold" style={tw`text-2xl text-[${PRIMARY_COLOR}]`}>
                        {steps[step]}
                    </AppText>
                </View>

                {/* Form Section */}
                <ScrollView
                    style={tw`flex-1`}
                    contentContainerStyle={tw`px-4 pb-8`}
                    showsVerticalScrollIndicator={false}
                    bounces={false}
                >
                    <View style={tw`mb-6`}>
                        <CurrentSection control={control} errors={errors} />
                    </View>
                </ScrollView>

                {/* Navigation Buttons */}
                <View style={tw`px-4 py-4 bg-white border-t border-gray-200`}>
                    <View style={tw`flex-row justify-between`}>
                        {step > 0 ? (
                            <Button
                                mode='contained'
                                style={tw`py-1  mr-2`}
                                onPress={handleBack}
                                icon={() => <Ionicons name="arrow-back" size={24} color="#fff" />}
                            >
                                Back
                            </Button>
                        ) : (
                            <View style={tw`flex-1 mr-2`} />
                        )}

                        {step < steps.length - 1 ? (
                            <Button
                                mode='contained'
                                icon={() => <Ionicons name="arrow-forward" size={24} color="#fff" />}
                                style={tw`py-1  ml-2`}
                                contentStyle={{ flexDirection: 'row-reverse' }}
                                onPress={handleNext}
                            >
                                Next
                            </Button>
                        ) : (
                            <Button
                                mode='contained'
                                style={tw`py-1  ml-2`}
                                onPress={handleSubmit(handleConfirmSubmit)}
                                disabled={isSubmitting}
                                icon={() => <Ionicons name="checkmark" size={24} color="#fff" />}
                            >
                                {isSubmitting ? "Submitting..." : "Submit"}
                            </Button>
                        )}
                    </View>
                </View>
            </View>
        </FormProvider>
    );
};

export default PublicPlaceForm;