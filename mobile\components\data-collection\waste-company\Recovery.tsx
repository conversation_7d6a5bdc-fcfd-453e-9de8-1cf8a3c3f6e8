import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, ScrollView, View } from 'react-native';
import { FormProvider, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { Button } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import tw from 'twrnc';
import AppText from '@/components/ui/Text';
import { PRIMARY_COLOR } from '@/constants/colors';
import { wasteRecoveryCompanySchema } from '@/lib/forms/waste-company';
import BasicInfoSection from './recovery/BasicInfo';
import ContactDetailsSection from './recovery/ContactDetails';
import ManagementCapacitySection from './recovery/ManagementCapacity';
import OperationTypeSection from './recovery/OperationType';
import HandledMaterialsSection from './recovery/HandledMaterials';
import BusinessSitesSection from './recovery/BusinessSites';

const steps = [
  'Basic Information',
  'Contact Details',
  'Service Provider Management Capacity and Gender Inclusion',
  'Operation Type',
  'Handled Materials',
  'Business Sites',
];

const initialValues = {
  basicInfo: {
    companyName: '',
  },
  contactDetails: {
    contactPerson: '',
    contactPhone: '',
    contactEmail: '',
  },
  managementCapacity: {
    companyType: null,
    otherCompanyType: '',
    totalPersonnel: null,
    femalePersonnel: null,
    malePersonnel: null,
  },
  operationType: {
    operationType: null,
  },
  handledMaterials: {
    materials: [
      {
        materialName: '',
        supplier: '',
        quantityPerDay: null,
      },
    ],
  },
  businessSites: {
    sites: [
      {
        name: '',
        type: '',
        villageId: null,
        locationDisplay: '',
        latitude: null,
        longitude: null,
      },
    ],
  },
};

const sectionComponents = [
  BasicInfoSection,
  ContactDetailsSection,
  ManagementCapacitySection,
  OperationTypeSection,
  HandledMaterialsSection,
  BusinessSitesSection,
];

interface WasteRecoveryCompanyFormProps {
  data?: any;
  onSubmit: (data: any) => void;
}

const WasteRecoveryCompanyForm = ({ onSubmit, data }: WasteRecoveryCompanyFormProps) => {
  const [step, setStep] = useState<number>(0);

  const form = useForm({
    defaultValues: initialValues,
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: yupResolver(wasteRecoveryCompanySchema) as any,
  });

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    trigger,
    reset,
    watch,
  } = form;

  useEffect(() => {
    if (data) {
      reset({
        ...initialValues,
        basicInfo: {
          ...initialValues.basicInfo,
          companyName: data.companyName || '',
        },
      });
    }
  }, [data]);

  const CurrentSection = sectionComponents[step];

  const sectionKeys = [
    'basicInfo',
    'contactDetails',
    'managementCapacity',
    'operationType',
    'handledMaterials',
    'businessSites',
  ];

  const handleNext = async () => {
    const valid = await trigger(sectionKeys[step] as any, { shouldFocus: true });
    if (valid) setStep(step + 1);
  };

  const handleBack = () => setStep(step - 1);

  const handleConfirmSubmit = (data: any) => {
    Alert.alert(
      'Confirm Submission',
      'Are you sure you want to submit the waste recovery company data?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'OK', onPress: () => onSubmit(data) },
      ]
    );
  };

  const progress = ((step + 1) / steps.length) * 100;

  return (
    <FormProvider {...form}>
      <View style={tw`flex-1`}>
        {/* Header */}
        <View style={tw`mb-4 px-4 pt-4`}>
          <AppText weight='bold' style={tw`text-gray-400`}>
            Waste Recovery Company Information
          </AppText>
          <View style={tw`flex-row gap-4 pt-2 items-center`}>
            <View style={tw`w-[90%] h-2 bg-gray-200 rounded-full overflow-hidden`}>
              <View style={[tw`h-full bg-[${PRIMARY_COLOR}]`, { width: `${progress}%` }]} />
            </View>
            <AppText style={tw`text-sm text-gray-400`}>{Math.round(progress)}%</AppText>
          </View>
          <AppText weight="semibold" style={tw`text-2xl text-[${PRIMARY_COLOR}]`}>
            {steps[step]}
          </AppText>
        </View>

        {/* Form Section */}
        <ScrollView
          style={tw`flex-1`}
          contentContainerStyle={tw`px-4 pb-8`}
          showsVerticalScrollIndicator={false}
          bounces={false}
        >
          <View style={tw`mb-6`}>
            <CurrentSection control={control} errors={errors} watch={watch} />
          </View>
        </ScrollView>

        {/* Navigation Buttons */}
        <View style={tw`px-4 py-4 bg-white border-t border-gray-200`}>
          <View style={tw`flex-row justify-between`}>
            {step > 0 ? (
              <Button
                mode='contained'
                style={tw`py-1 mr-2`}
                onPress={handleBack}
                icon={() => <Ionicons name="arrow-back" size={24} color="#fff" />}
              >
                Back
              </Button>
            ) : (
              <View style={tw`flex-1 mr-2`} />
            )}

            {step < steps.length - 1 ? (
              <Button
                mode='contained'
                icon={() => <Ionicons name="arrow-forward" size={24} color="#fff" />}
                style={tw`py-1 ml-2`}
                contentStyle={{ flexDirection: 'row-reverse' }}
                onPress={handleNext}
              >
                Next
              </Button>
            ) : (
              <Button
                mode='contained'
                style={tw`py-1 ml-2`}
                onPress={handleSubmit(handleConfirmSubmit)}
                disabled={isSubmitting}
                icon={() => <Ionicons name="checkmark" size={24} color="#fff" />}
              >
                {isSubmitting ? "Submitting..." : "Submit"}
              </Button>
            )}
          </View>
        </View>
      </View>
    </FormProvider>
  );
};

export default WasteRecoveryCompanyForm;
