.sidebar-container {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  background: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 40;
  display: flex;
  flex-direction: column;
}

/* Desktop - Always expanded */
.sidebar-expanded {
  width: 240px; /* Wider for better desktop experience */
}

.sidebar-collapsed {
  width: 240px; /* Same as expanded on desktop */
}

/* Hide toggle button on desktop */
.mobile-only {
  display: none;
}
.sidebar-logo-full {
  height: 60px;
  width: 160px;           /* Fixed width */
  max-width: 200px;
  object-fit: contain;
  transition: all 0.3s ease;
}
.sidebar-logo-icon {
  height: 32px;           /* Increased from 24px for better mobile visibility */
  width: 32px;
  object-fit: cover;
  object-position: center;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.toggle-btn {
  flex-shrink: 0;
  min-width: 32px !important;
  min-height: 32px !important;
}

/* Header section padding */
.sidebar-container > div:first-child {
  padding: 0.75rem !important;
  min-height: 60px;
}

/* Navigation container */
.sidebar-container nav {
  padding: 0.75rem 0.5rem !important;
}

/* Navigation items */
.nav-item {
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  margin-bottom: 0.25rem;
  border-radius: 0.375rem;
  padding: 0.75rem 1rem !important;
  margin: 0.25rem 0.5rem !important;
}

.nav-item:hover {
  background-color: #eff6ff !important;
  color: #2563eb !important;
}

.nav-item:hover i {
  color: #2563eb !important;
}

.nav-item span {
  margin-left: 0.75rem !important;
  font-size: 0.875rem !important;
  font-weight: 500;
}

.nav-item i {
  font-size: 1rem !important;
  width: 20px;
  text-align: center;
}

/* Profile section */
.sidebar-container > div:last-child {
  padding: 0.75rem 0.5rem !important;
  margin-top: auto;
}

/* PrimeNG button overrides */
:host ::ng-deep .p-button {
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  padding: 0.5rem !important;
  min-width: 36px !important;
  height: 36px !important;
}

:host ::ng-deep .p-button .p-button-icon {
  font-size: 1rem !important;
}

:host ::ng-deep .p-button:hover {
  background: #f3f4f6 !important;
}

:host ::ng-deep .p-button:focus {
  box-shadow: none !important;
}

/* MOBILE RESPONSIVENESS - Only collapse on smaller devices */
@media (max-width: 768px) {
  /* Show toggle button on mobile */
  .mobile-only {
    display: block !important;
  }
  
  /* Enable collapsing on mobile */
  .sidebar-expanded {
    width: 200px;
  }
  
  .sidebar-collapsed {
    width: 60px;
  }
  
  /* Mobile collapsed logo */
  .sidebar-collapsed .sidebar-logo-icon {
    height: 24px;
    width: 24px;
    object-fit: cover;
    object-position: center;
  }
  
  /* Mobile collapsed navigation */
  .sidebar-collapsed .nav-item {
    justify-content: center !important;
    padding: 0.75rem 0.5rem !important;
    margin: 0.25rem 0.125rem !important;
  }
  
  .sidebar-collapsed .nav-item i {
    font-size: 1.1rem !important;
  }
  
  .sidebar-collapsed .nav-item span {
    display: none;
  }
  
  /* Smaller components on mobile */
  .sidebar-container > div:first-child {
    padding: 0.5rem !important;
    min-height: 50px;
  }
  
  .sidebar-container nav {
    padding: 0.5rem 0.25rem !important;
  }
}

@media (max-width: 480px) {
  .sidebar-expanded {
    width: 180px;
  }
  
  .sidebar-collapsed {
    width: 50px;
  }
}