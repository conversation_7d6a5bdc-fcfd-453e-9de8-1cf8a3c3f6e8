import { Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { CreateHouseholdSubmissionDto } from './dto/household.dto';
import { CreateSchoolSubmissionDto } from './dto/school.dto';
import { CreateHealthFacilitySubmissionDto } from './dto/health-facility.dto';
import { CreatePublicPlaceSubmissionDto, PublicPlaceSubmissionResponseDto } from './dto/public-place.dto';
import { BaseCreateSubmissionResponseDto } from './dto/base-submission.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateWasteCollectionCompanySubmissionDto, WasteCollectionCompanySubmissionResponseDto } from './dto/waste-collection-company.dto';
import { CreateWasteRecoveryCompanySubmissionDto, WasteRecoveryCompanySubmissionResponseDto } from './dto/waste-recovery-company.dto';
import { CreateWasteDisposalCompanySubmissionDto, WasteDisposalCompanySubmissionResponseDto } from './dto/waste-disposal-company.dto';
import { FacilityType } from '@prisma/client';

@Injectable()
export class SubmissionService {

    constructor(private prisma: PrismaService) { }

    async createHouseholdSubmission(dto: CreateHouseholdSubmissionDto, userId: string): Promise<BaseCreateSubmissionResponseDto> {
        const household = await this.prisma.houseHold.findUnique({
            where: { id: dto.facilityId },
        });

        if (!household || household.deleted) {
            throw new NotFoundException('Household not found');
        }

        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }
        
        try {

            const result = await this.prisma.$transaction(async (tx) => {

                const submission = await tx.submission.create({
                    data: {
                        facilityType: dto.facilityType,
                        submittedAt: dto.submittedAt,
                        houseHoldId: dto.facilityId,
                        submittedById: userId,
                        HouseHoldGeneralInfo: {
                            create: dto.generalInfo,
                        },
                        HouseHoldWaterSupply: {
                            create: dto.waterSupply,
                        },
                        HouseHoldSanitation: {
                            create: dto.sanitation,
                        },
                        HouseHoldHygiene: {
                            create: dto.hygiene,
                        },
                        HouseHoldSolidWasteManagement: {
                            create: dto.solidWaste,
                        },
                        HouseHoldLiquidWasteManagement: {
                            create: dto.liquidWaste,
                        },
                    }
                })

                return submission;
            })

            return {
                message: 'Household submission created',
                submission: result,
            };

        } catch (error) {
            console.log(error)
            throw new InternalServerErrorException('Failed to create household submission');
        }
    }

    async createSchoolSubmission(dto: CreateSchoolSubmissionDto, userId: string) {

        const school = await this.prisma.school.findUnique({
            where: { id: dto.facilityId },
        });

        if (!school || school.deleted) {
            throw new NotFoundException('School not found');
        }

        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }

        try {

            const result = await this.prisma.$transaction(async (tx) => {

                const submission = await tx.submission.create({
                    data: {
                        facilityType: dto.facilityType,
                        submittedAt: dto.submittedAt,
                        schoolId: dto.facilityId,
                        submittedById: userId,
                        SchoolGeneralInfo: {
                            create: dto.generalInfo,
                        },
                        SchoolWaterSupply: {
                            create: dto.waterSupply,
                        },
                        SchoolSanitation: {
                            create: dto.sanitation,
                        },
                        SchoolHygiene: {
                            create: dto.hygiene,
                        },
                        SchoolSolidWasteManagement: {
                            create: dto.solidWaste,
                        },
                        SchoolLiquidWasteManagement: {
                            create: dto.liquidWaste,
                        },
                    }
                })

                return submission;
            })

            return {
                message: 'School submission created',
                submission: result,
            };

        } catch (error) {
            console.log(error)
            throw new InternalServerErrorException('Failed to create school submission');
        }
    }

    async createHealthFacilitySubmission(dto: CreateHealthFacilitySubmissionDto, userId: string) {
        const healthFacility = await this.prisma.healthFacility.findUnique({
            where: { id: dto.facilityId },
        });

        if (!healthFacility || healthFacility.deleted) {
            throw new NotFoundException('Health facility not found');
        }

        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }

        try {

            const result = await this.prisma.$transaction(async (tx) => {

                const submission = await tx.submission.create({
                    data: {
                        facilityType: dto.facilityType,
                        submittedAt: dto.submittedAt,
                        healthFacilityId: dto.facilityId,
                        submittedById: userId,
                        HealthFacilityGeneralInfo: {
                            create: dto.generalInfo,
                        },
                        HealthFacilityWaterSupply: {
                            create: dto.waterSupply,
                        },
                        HealthFacilitySanitation: {
                            create: dto.sanitation,
                        },
                        HealthFacilityHygiene: {
                            create: dto.hygiene,
                        },
                        HealthFacilitySolidWasteManagement: {
                            create: dto.solidWaste,
                        },
                        HealthFacilityLiquidWasteManagement: {
                            create: dto.liquidWaste,
                        },
                    }
                })

                return submission;
            })

            return {
                message: 'Health facility submission created',
                submission: result,
            };

        } catch (error) {
            console.log(error)
            throw new InternalServerErrorException('Failed to create health facility submission');
        }
    }

    async createPublicPlaceSubmission(dto: CreatePublicPlaceSubmissionDto, userId: string) {
        const publicPlace = await this.prisma.publicPlace.findUnique({
            where: { id: dto.facilityId },
        }); 

        if (!publicPlace || publicPlace.deleted) {
            throw new NotFoundException('Public place not found');
        }

        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }

        try {

            const result = await this.prisma.$transaction(async (tx) => {

                const submission = await tx.submission.create({
                    data: {
                        facilityType: dto.facilityType,
                        submittedAt: dto.submittedAt,
                        publicPlaceId: dto.facilityId,
                        submittedById: userId,
                        PublicPlaceGeneralInfo: {
                            create: {
                                category: dto.generalInfo.category,
                                openingDays: dto.generalInfo.openingDays,
                            },
                        },
                        PublicPlaceWaterSupply: {
                            create: dto.waterSupply,
                        },
                        PublicPlaceSanitation: {
                            create: dto.sanitation,
                        },
                        PublicPlaceHygiene: {
                            create: dto.hygiene,
                        },
                        PublicPlaceSolidWasteManagement: {
                            create: dto.solidWaste,
                        },
                        PublicPlaceLiquidWasteManagement: {
                            create: dto.liquidWaste,
                        },
                    }
                })

                return submission;
            })

            return {
                message: 'Public place submission created',
                submission: result,
            };

        } catch (error) {
            console.log(error)
            throw new InternalServerErrorException('Failed to create public place submission');
        }
    }

    async createWasteCollectionCompanySubmission(
        dto: CreateWasteCollectionCompanySubmissionDto,
        userId: string,
    ): Promise<WasteCollectionCompanySubmissionResponseDto> {
        return this.prisma.$transaction(async (tx) => {
            const submission = await tx.submission.create({
                data: {
                    facilityType: FacilityType.WASTE_COLLECTION_COMPANY,
                    submittedById: userId,
                },
            });

            const wasteCollectionCompany = await tx.wasteCollectionCompany.create({
                data: {
                    submissionId: submission.id,
                    companyName: dto.companyName,
                    ownerName: dto.ownerName,
                    ownerGender: dto.ownerGender,
                    contactPhone: dto.contactPhone,
                    contactEmail: dto.contactEmail,
                    companyType: dto.companyType,
                    otherCompanyType: dto.otherCompanyType,
                    totalPersonnel: dto.totalPersonnel,
                    femalePersonnel: dto.femalePersonnel,
                    malePersonnel: dto.malePersonnel,
                    clientTypes: dto.clientTypes,
                    otherClientTypes: dto.otherClientTypes || [],
                    wasteSeparation: dto.wasteSeparation,
                    separatedMaterials: dto.separatedMaterials,
                    otherSeparatedMaterials: dto.otherSeparatedMaterials || [],
                    wasteDestination: dto.wasteDestination,
                    destinationDetails: dto.destinationDetails,
                    weighbridge: dto.weighbridge,
                    recordingMethod: dto.recordingMethod,
                    otherRecordingMethod: dto.otherRecordingMethod,
                },
            });

            return {
                id: wasteCollectionCompany.id,
                facilityType: submission.facilityType,
                submissionId: submission.id,
                submittedAt: submission.submittedAt,
                ...wasteCollectionCompany,
            };
        });
    }

    async createWasteRecoveryCompanySubmission(
        dto: CreateWasteRecoveryCompanySubmissionDto,
        userId: string,
    ): Promise<WasteRecoveryCompanySubmissionResponseDto> {
        return this.prisma.$transaction(async (tx) => {
            const submission = await tx.submission.create({
                data: {
                    facilityType: FacilityType.WASTE_RECOVERY_COMPANY,
                    submittedById: userId,
                },
            });

            // Create handled materials
            const handledMaterials = await Promise.all(
                dto.handledMaterials.map(material =>
                    tx.handledMaterial.create({
                        data: {
                            materialName: material.materialName,
                            supplier: material.supplier,
                            quantityPerDay: material.quantityPerDay,
                        },
                    })
                )
            );

            // Create business sites with locations
            const businessSites = await Promise.all(
                dto.businessSites.map(async (site) => {
                    // Verify village exists
                    const village = await tx.village.findUnique({
                        where: { id: site.villageId },
                    });

                    if (!village) {
                        throw new NotFoundException(`Village with id ${site.villageId} not found`);
                    }

                    // Create location for the business site
                    const location = await tx.location.create({
                        data: {
                            villageId: site.villageId,
                            latitude: site.latitude,
                            longitude: site.longitude,
                            settlementType: 'RURAL',
                        },
                    });

                    // Create business site with the new location
                    return tx.businessSite.create({
                        data: {
                            name: site.name,
                            type: site.type,
                            locationId: location.id,
                        },
                    });
                })
            );

            const wasteRecoveryCompany = await tx.wasteRecoveryCompany.create({
                data: {
                    submissionId: submission.id,
                    companyName: dto.companyName,
                    contactPerson: dto.contactPerson,
                    contactPhone: dto.contactPhone,
                    contactEmail: dto.contactEmail,
                    companyType: dto.companyType,
                    otherCompanyType: dto.otherCompanyType,
                    totalPersonnel: dto.totalPersonnel,
                    femalePersonnel: dto.femalePersonnel,
                    malePersonnel: dto.malePersonnel,
                    operationType: dto.operationType,
                    handledMaterials: {
                        connect: handledMaterials.map(hm => ({ id: hm.id })),
                    },
                    businessSites: {
                        connect: businessSites.map(bs => ({ id: bs.id })),
                    },
                },
                include: {
                    handledMaterials: true,
                    businessSites: {
                        include: {
                            location: true,
                        },
                    },
                },
            });

            return {
                id: wasteRecoveryCompany.id,
                facilityType: submission.facilityType,
                submissionId: submission.id,
                submittedAt: submission.submittedAt,
                ...wasteRecoveryCompany,
            };
        });
    }

    async createWasteDisposalCompanySubmission(
        dto: CreateWasteDisposalCompanySubmissionDto,
        userId: string,
    ): Promise<WasteDisposalCompanySubmissionResponseDto> {
        return this.prisma.$transaction(async (tx) => {
            const submission = await tx.submission.create({
                data: {
                    facilityType: FacilityType.WASTE_DISPOSAL_COMPANY,
                    submittedById: userId,
                },
            });

            // Verify village exists
            const village = await tx.village.findUnique({
                where: { id: dto.facilityVillageId },
            });

            if (!village) {
                throw new NotFoundException(`Village with id ${dto.facilityVillageId} not found`);
            }

            // Create location for the disposal facility
            const facilityLocation = await tx.location.create({
                data: {
                    villageId: dto.facilityVillageId,
                    latitude: dto.facilityLatitude,
                    longitude: dto.facilityLongitude,
                    settlementType: 'RURAL',
                },
            });

            const wasteDisposalCompany = await tx.wasteDisposalCompany.create({
                data: {
                    submissionId: submission.id,
                    companyName: dto.companyName,
                    facilityLocationId: facilityLocation.id,
                    contactPerson: dto.contactPerson,
                    contactPhone: dto.contactPhone,
                    contactEmail: dto.contactEmail,
                    companyType: dto.companyType,
                    otherCompanyType: dto.otherCompanyType,
                    totalPersonnel: dto.totalPersonnel,
                    femalePersonnel: dto.femalePersonnel,
                    malePersonnel: dto.malePersonnel,
                    clientTypes: dto.clientTypes,
                    otherClientTypes: dto.otherClientTypes || [],
                    boundaryControl: dto.boundaryControl,
                    wasteDepositControl: dto.wasteDepositControl,
                    compactionFrequency: dto.compactionFrequency,
                    wasteBurning: dto.wasteBurning,
                    weighbridge: dto.weighbridge,
                    wasteAmount: dto.wasteAmount,
                    truckFrequency: dto.truckFrequency,
                    recordingMethod: dto.recordingMethod,
                    otherRecordingMethod: dto.otherRecordingMethod,
                },
                include: {
                    facilityLocation: true,
                },
            });

            return {
                id: wasteDisposalCompany.id,
                facilityType: submission.facilityType,
                submissionId: submission.id,
                submittedAt: submission.submittedAt,
                ...wasteDisposalCompany,
            };
        });
    }

}
