model WasteCollectionCompany {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    companyName  String
    ownerName    String
    ownerGender  Gender
    contactPhone String
    contactEmail String

    companyType      ServiceProviderType
    otherCompanyType String?
    totalPersonnel   Int
    femalePersonnel  Int
    malePersonnel    Int
    clientTypes      ClientType[]
    otherClientTypes String[]

    wasteSeparation         Boolean
    separatedMaterials      WasteMaterial[]
    otherSeparatedMaterials String[]
    wasteDestination        WasteDestination
    destinationDetails      String?
    weighbridge             Boolean
    recordingMethod         RecordingMethod?
    otherRecordingMethod    String?
}

model WasteRecoveryCompany {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    companyName   String
    contactPerson String
    contactPhone  String
    contactEmail  String

    companyType      ServiceProviderType
    otherCompanyType String?
    totalPersonnel   Int
    femalePersonnel  Int
    malePersonnel    Int

    operationType    OperationType
    handledMaterials HandledMaterial[]
    businessSites    BusinessSite[]
}

model HandledMaterial {
    id             String @id @default(uuid())
    materialName   String
    supplier       String
    quantityPerDay Float

    WasteRecoveryCompany WasteRecoveryCompany[]
}

model BusinessSite {
    id String @id @default(uuid())
    name String
    type String

    location Location @relation(fields: [locationId], references: [id])
    locationId String

    WasteRecoveryCompany WasteRecoveryCompany[]
}

model WasteDisposalCompany {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    companyName         String
    facilityLocation    Location @relation(fields: [facilityLocationId], references: [id])
    facilityLocationId  String
    contactPerson       String
    contactPhone        String
    contactEmail        String

    companyType         ServiceProviderType
    otherCompanyType    String?
    totalPersonnel      Int
    femalePersonnel     Int
    malePersonnel       Int

    clientTypes         ClientType[]
    otherClientTypes    String[]

    boundaryControl     Boolean
    wasteDepositControl Boolean
    compactionFrequency CompactionFrequency
    wasteBurning        Boolean
    weighbridge         Boolean
    wasteAmount         Float
    truckFrequency      TruckFrequency
    recordingMethod     RecordingMethod?
    otherRecordingMethod String?
}
