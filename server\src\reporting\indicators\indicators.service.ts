import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { IndicatorsRequestDto, IndicatorsResponseDto, GeoMappingRequestDto, GeoMappingResponseDto } from './dto/indicator.dto';
import { HandWashingMaterial, HouseHold, MainWaterSource, SettlementType, ToiletFacilityType, WaterFetchingTime, WaterSourceDistance } from '@prisma/client';

type IndicatorRequestProps = {
    level: "COUNTRY" | "PROVINCE" | "DISTRICT" | "SECTOR" | "CELL" | "VILLAGE";
    levelId: string;
    startDate: Date;
    endDate: Date;
}

@Injectable()
export class IndicatorsService {

    constructor(private prisma: PrismaService) { }

    async getBasicIndicators(data: IndicatorsRequestDto): Promise<IndicatorsResponseDto> {

        try {
            const indicatorRequestProps: IndicatorRequestProps = {
                level: data.level,
                levelId: data.levelId,
                startDate: new Date(data.startDate),
                endDate: new Date(data.endDate),
            };

            const indicators: { title: string, items: { name: string, value: number, percentage: number }[] }[] = [];
            switch (data.facilityType) {
                case "HOUSEHOLD":
                    
                    indicators.push({
                        title: "Household Water Supply",
                        items: await this.householdWaterSuppy(indicatorRequestProps),
                    });

                    indicators.push({
                        title: "Household Sanitation",
                        items: await this.houseHoldSanitation(indicatorRequestProps),
                    });

                    indicators.push({
                        title: "Household Hygiene",
                        items: await this.houseHoldHygiene(indicatorRequestProps),
                    });

                    break;
                case "SCHOOL":
                    break;
                case "HEALTH_FACILITY":
                    break;
                case "PUBLIC_PLACE":
                    break;
                case "WASTE_COLLECTION_COMPANY":
                    break;
                case "WASTE_RECOVERY_COMPANY":
                    break;
                case "WASTE_DISPOSAL_COMPANY":
                    break;
                default:
                    break;
            }



            return {
                level: data.level,
                levelId: data.levelId,
                startDate: data.startDate,
                endDate: data.endDate,
                facilityType: data.facilityType,
                indicators,
            }

        } catch (error) {
            console.log(error)
            throw new InternalServerErrorException('Failed to get indicators')
        }
    }

    async householdWaterSuppy(data: IndicatorRequestProps) {
        //check level
        let houseHolds: HouseHold[] = [];

        switch (data.level) {
            case "COUNTRY":
                houseHolds = await this.prisma.houseHold.findMany({});
                break;
            case "PROVINCE":
                const province = await this.prisma.province.findUnique({
                    where: { id: Number(data.levelId) },
                });

                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        district: {
                                            provinceId: province.id,
                                        },
                                    },
                                },
                            },
                        },
                    },
                });

                break;
            case "DISTRICT":
                const district = await this.prisma.district.findUnique({
                    where: { id: Number(data.levelId) },
                });

                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        districtId: district.id,
                                    },
                                },
                            },
                        },
                    },
                });

                break;
            case "SECTOR":
                const sector = await this.prisma.sector.findUnique({
                    where: { id: Number(data.levelId) },
                });

                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sectorId: sector.id,
                                },
                            },
                        },
                    },
                });

                break;
            case "CELL":
                const cell = await this.prisma.cell.findUnique({
                    where: { id: Number(data.levelId) },
                });

                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cellId: cell.id,
                            },
                        },
                    },
                });

                break;
            case "VILLAGE":
                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            villageId: Number(data.levelId),
                        },
                    },
                });
                break;
            default:
                break;
        }

        if (houseHolds.length === 0) {
            return [];
        }

        const submissions = await this.prisma.submission.findMany({
            where: {
                houseHoldId: {
                    in: houseHolds.map((houseHold) => houseHold.id),
                },
                submittedAt: {
                    gte: new Date(data.startDate),
                    lte: new Date(data.endDate),
                },
            },
            include: {
                HouseHoldGeneralInfo: true,
                HouseHoldWaterSupply: true,
                household: {
                    include: {
                        location: true,
                    },
                },
            },
        });

        if (submissions.length === 0) {
            return [];
        }

        const totalHouseholds = submissions.length;

        // % of Households have access to safely managed water services
        const safelyManagedWaterSupply = submissions.filter((submission) => submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.WATER_TAP_WITHIN_HH).length;
        const percentageSafelyManagedWaterSupply = (safelyManagedWaterSupply / totalHouseholds) * 100;

        // % of Households have access to basic water services
        const basicWaterServices = submissions.filter((submission) => submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.WATER_TAP_WITHIN_HH ||
            submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.PUBLIC_WATER_TAP_KIOSK ||
            submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.PROTECTED_IMPROVED_SPRINGS ||
            submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.BOREHOLE ||
            submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.FROM_NEIGHBOR
        ).length;
        const percentageBasicWaterServices = (basicWaterServices / totalHouseholds) * 100;

        // % of Households have access to limited water services
        const limitedWaterServices = submissions.filter((submission) =>
            (submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.PUBLIC_WATER_TAP_KIOSK ||
                submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.PROTECTED_IMPROVED_SPRINGS ||
                submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.BOREHOLE ||
                submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.FROM_NEIGHBOR) &&
            (submission.HouseHoldWaterSupply[0].timeToFetch === WaterFetchingTime.MINUTES_31_TO_60 ||
                submission.HouseHoldWaterSupply[0].timeToFetch === WaterFetchingTime.ABOVE_ONE_HOUR)
        ).length;
        const percentageLimitedWaterServices = (limitedWaterServices / totalHouseholds) * 100;

        // % of Households using improved water sources
        const improvedWaterSources = submissions.filter((submission) => submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.WATER_TAP_WITHIN_HH ||
            submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.PUBLIC_WATER_TAP_KIOSK ||
            submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.PROTECTED_IMPROVED_SPRINGS ||
            submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.BOREHOLE ||
            submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.FROM_NEIGHBOR
        ).length;
        const percentageImprovedWaterSources = (improvedWaterSources / totalHouseholds) * 100;

        // % of Households using unimproved water sources
        const unimprovedWaterSources = submissions.filter((submission) => submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.UNIMPROVED_SPRINGS ||
            submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.RAIN_WATER
        ).length;
        const percentageUnimprovedWaterSources = (unimprovedWaterSources / totalHouseholds) * 100;

        // % of Households with No water services
        const noWaterServices = submissions.filter((submission) => submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.SURFACE_WATER).length;
        const percentageNoWaterServices = (noWaterServices / totalHouseholds) * 100;

        // % of households per status of access to basic water services in rural areas
        const ruralHouseholds = submissions.filter((submission) => submission.household.location.settlementType === SettlementType.RURAL && (
            submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.WATER_TAP_WITHIN_HH ||
            submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.PUBLIC_WATER_TAP_KIOSK ||
            submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.PROTECTED_IMPROVED_SPRINGS ||
            submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.BOREHOLE ||
            submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.FROM_NEIGHBOR
        ) && (
                submission.HouseHoldWaterSupply[0].timeToFetch === WaterFetchingTime.MINUTES_31_TO_60 ||
                submission.HouseHoldWaterSupply[0].timeToFetch === WaterFetchingTime.ABOVE_ONE_HOUR
            )).length;
        const percentageRuralHouseholds = (ruralHouseholds / totalHouseholds) * 100;

        // % of households per status of access to basic water services in urban areas
        const urbanHouseholds = submissions.filter((submission) => submission.household.location.settlementType === SettlementType.URBAN && (
            submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.WATER_TAP_WITHIN_HH ||
            submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.PUBLIC_WATER_TAP_KIOSK ||
            submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.PROTECTED_IMPROVED_SPRINGS ||
            submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.BOREHOLE ||
            submission.HouseHoldWaterSupply[0].waterSource === MainWaterSource.FROM_NEIGHBOR
        ) && (
                submission.HouseHoldWaterSupply[0].timeToFetch === WaterFetchingTime.MINUTES_31_TO_60 ||
                submission.HouseHoldWaterSupply[0].timeToFetch === WaterFetchingTime.ABOVE_ONE_HOUR
            )).length;
        const percentageUrbanHouseholds = (urbanHouseholds / totalHouseholds) * 100;

        return [
            {
                name: "Households with safely managed water supply",
                value: safelyManagedWaterSupply,
                percentage: percentageSafelyManagedWaterSupply,
            },
            {
                name: "Households with basic water supply",
                value: basicWaterServices,
                percentage: percentageBasicWaterServices,
            },
            {
                name: "Households with limited water supply",
                value: limitedWaterServices,
                percentage: percentageLimitedWaterServices,
            },
            {
                name: "Households with improved water supply",
                value: improvedWaterSources,
                percentage: percentageImprovedWaterSources,
            },
            {
                name: "Households with unimproved water supply",
                value: unimprovedWaterSources,
                percentage: percentageUnimprovedWaterSources,
            },
            {
                name: "Households with no water supply",
                value: noWaterServices,
                percentage: percentageNoWaterServices,
            },
            {
                name: "Households with access to basic water services in rural areas",
                value: ruralHouseholds,
                percentage: percentageRuralHouseholds,
            },
            {
                name: "Households with access to basic water services in urban areas",
                value: urbanHouseholds,
                percentage: percentageUrbanHouseholds,
            },
        ];
    }

    async houseHoldSanitation(data: IndicatorRequestProps) {
        //check level
        let houseHolds: HouseHold[] = [];

        switch (data.level) {
            case "COUNTRY":

                houseHolds = await this.prisma.houseHold.findMany({});
                break;
            case "PROVINCE":
                const province = await this.prisma.province.findUnique({
                    where: { id: Number(data.levelId) },
                });

                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        district: {
                                            provinceId: province.id,
                                        },
                                    },
                                },
                            },
                        },
                    },
                });

                break;
            case "DISTRICT":
                const district = await this.prisma.district.findUnique({
                    where: { id: Number(data.levelId) },
                });

                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        districtId: district.id,
                                    },
                                },
                            },
                        },
                    },
                });

                break;
            case "SECTOR":
                const sector = await this.prisma.sector.findUnique({
                    where: { id: Number(data.levelId) },
                });

                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sectorId: sector.id,
                                },
                            },
                        },
                    },
                });

                break;
            case "CELL":
                const cell = await this.prisma.cell.findUnique({
                    where: { id: Number(data.levelId) },
                });

                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cellId: cell.id,
                            },
                        },
                    },
                });

                break;
            case "VILLAGE":
                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            villageId: Number(data.levelId),
                        },
                    },
                });
                break;
            default:
                break;
        }

        if (houseHolds.length === 0) {
            return [];
        }

        const submissions = await this.prisma.submission.findMany({
            where: {
                houseHoldId: {
                    in: houseHolds.map((houseHold) => houseHold.id),
                },
                submittedAt: {
                    gte: new Date(data.startDate),
                    lte: new Date(data.endDate),
                },
            },
            include: {
                HouseHoldGeneralInfo: true,
                HouseHoldSanitation: true,
                household: {
                    include: {
                        location: true,
                    },
                },
            },
        });

        if (submissions.length === 0) {
            return [];
        }

        const totalHouseholds = submissions.length;

        // % of Households having access to safely managed sanitation services
        const safelyManagedSanitation = submissions.filter((submission) => submission.HouseHoldSanitation[0].toiletType === ToiletFacilityType.FLUSH_TO_SEWER_SYSTEM ||
            submission.HouseHoldSanitation[0].toiletType === ToiletFacilityType.FLUSH_TO_SEPTIC_TANK ||
            submission.HouseHoldSanitation[0].toiletType === ToiletFacilityType.FLUSH_TO_PIT ||
            submission.HouseHoldSanitation[0].toiletType === ToiletFacilityType.VENTILATED_IMPROVED_PIT ||
            submission.HouseHoldSanitation[0].toiletType === ToiletFacilityType.PIT_WITH_SLAB ||
            submission.HouseHoldSanitation[0].toiletType === ToiletFacilityType.COMPOSTING_TOILET ||
            submission.HouseHoldSanitation[0].toiletType === ToiletFacilityType.URINE_DIVERSION_DRY_TOILET).length;
        const percentageSafelyManagedSanitation = (safelyManagedSanitation / totalHouseholds) * 100;

        // % of Households having access to limited sanitation services
        const limitedSanitation = submissions.filter((submission) => submission.HouseHoldSanitation[0].toiletShared).length;
        const percentageLimitedSanitation = (limitedSanitation / totalHouseholds) * 100;

        // % of Households without sanitation service (open defecation)
        const noSanitation = submissions.filter((submission) => submission.HouseHoldSanitation[0].toiletType === ToiletFacilityType.NO_FACILITY ||
            submission.HouseHoldSanitation[0].toiletType === ToiletFacilityType.OTHER).length;
        const percentageNoSanitation = (noSanitation / totalHouseholds) * 100;

        // % of Households have access to basic sanitation services
        const basicSanitation = submissions.filter((submission) => (submission.HouseHoldSanitation[0].toiletType === ToiletFacilityType.FLUSH_TO_SEWER_SYSTEM ||
            submission.HouseHoldSanitation[0].toiletType === ToiletFacilityType.FLUSH_TO_SEPTIC_TANK ||
            submission.HouseHoldSanitation[0].toiletType === ToiletFacilityType.FLUSH_TO_PIT ||
            submission.HouseHoldSanitation[0].toiletType === ToiletFacilityType.VENTILATED_IMPROVED_PIT ||
            submission.HouseHoldSanitation[0].toiletType === ToiletFacilityType.PIT_WITH_SLAB ||
            submission.HouseHoldSanitation[0].toiletType === ToiletFacilityType.COMPOSTING_TOILET ||
            submission.HouseHoldSanitation[0].toiletType === ToiletFacilityType.URINE_DIVERSION_DRY_TOILET) && !submission.HouseHoldSanitation[0].toiletShared).length;

        const percentageBasicSanitation = (basicSanitation / totalHouseholds) * 100;

        // % of households per status of access to improved and basic sanitation in rural areas
        const ruralHouseholds = submissions.filter((submission) => submission.household.location.settlementType === SettlementType.RURAL && !submission.HouseHoldSanitation[0].toiletShared).length;
        const percentageRuralHouseholds = (ruralHouseholds / totalHouseholds) * 100;

        // % of households per status of access to improved and basic sanitation in urban areas
        const urbanHouseholds = submissions.filter((submission) => submission.household.location.settlementType === SettlementType.URBAN && !submission.HouseHoldSanitation[0].toiletShared).length;
        const percentageUrbanHouseholds = (urbanHouseholds / totalHouseholds) * 100;

        return [
            {
                name: "Households with safely managed sanitation services",
                value: safelyManagedSanitation,
                percentage: percentageSafelyManagedSanitation,
            },
            {
                name: "Households with limited sanitation services",
                value: limitedSanitation,
                percentage: percentageLimitedSanitation,
            },
            {
                name: "Households without sanitation services",
                value: noSanitation,
                percentage: percentageNoSanitation,
            },
            {
                name: "Households with basic sanitation services",
                value: basicSanitation,
                percentage: percentageBasicSanitation,
            },
        ];
    }

    async houseHoldHygiene(data: IndicatorRequestProps) {
        //check level
        let houseHolds: HouseHold[] = [];

        switch (data.level) {
            case "COUNTRY":
                houseHolds = await this.prisma.houseHold.findMany({});
                break;
            case "PROVINCE":
                const province = await this.prisma.province.findUnique({
                    where: { id: Number(data.levelId) },
                });

                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        district: {
                                            provinceId: province.id,
                                        },
                                    },
                                },
                            },
                        },
                    },
                });

                break;
            case "DISTRICT":
                const district = await this.prisma.district.findUnique({
                    where: { id: Number(data.levelId) },
                });

                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sector: {
                                        districtId: district.id,
                                    },
                                },
                            },
                        },
                    },
                });

                break;
            case "SECTOR":
                const sector = await this.prisma.sector.findUnique({
                    where: { id: Number(data.levelId) },
                });

                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cell: {
                                    sectorId: sector.id,
                                },
                            },
                        },
                    },
                });

                break;
            case "CELL":
                const cell = await this.prisma.cell.findUnique({
                    where: { id: Number(data.levelId) },
                });

                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            village: {
                                cellId: cell.id,
                            },
                        },
                    },
                });

                break;
            case "VILLAGE":
                houseHolds = await this.prisma.houseHold.findMany({
                    where: {
                        location: {
                            villageId: Number(data.levelId),
                        },
                    },
                });
                break;
            default:
                break;
        }

        if (houseHolds.length === 0) {
            return [];
        }

        const submissions = await this.prisma.submission.findMany({
            where: {
                houseHoldId: {
                    in: houseHolds.map((houseHold) => houseHold.id),
                },
                submittedAt: {
                    gte: new Date(data.startDate),
                    lte: new Date(data.endDate),
                },
            },
            include: {
                HouseHoldGeneralInfo: true,
                HouseHoldHygiene: true,
                household: {
                    include: {
                        location: true,
                    },
                },
            },
        });

        if (submissions.length === 0) {
            return [];
        }

        const totalHouseholds = submissions.length;

        // % of Households with basic hygiene services
        const basicHygiene = submissions.filter((submission) => submission.HouseHoldHygiene[0].handwashingFacility && submission.HouseHoldHygiene[0].handwashingMaterials === HandWashingMaterial.WATER_AND_SOAP).length;
        const percentageBasicHygiene = (basicHygiene / totalHouseholds) * 100;

        // % of Households with limited hygiene services
        const limitedHygiene = submissions.filter((submission) => submission.HouseHoldHygiene[0].handwashingFacility && submission.HouseHoldHygiene[0].handwashingMaterials === HandWashingMaterial.ONLY_WATER || submission.HouseHoldHygiene[0].handwashingMaterials === HandWashingMaterial.NONE).length;
        const percentageLimitedHygiene = (limitedHygiene / totalHouseholds) * 100;

        // % of Households with not hygiene services
        const noHygiene = submissions.filter((submission) => !submission.HouseHoldHygiene[0].handwashingFacility).length;
        const percentageNoHygiene = (noHygiene / totalHouseholds) * 100;

        return [
            {
                name: "Households with basic hygiene services",
                value: basicHygiene,
                percentage: percentageBasicHygiene,
            },
            {
                name: "Households with limited hygiene services",
                value: limitedHygiene,
                percentage: percentageLimitedHygiene,
            },
            {
                name: "Households with no hygiene services",
                value: noHygiene,
                percentage: percentageNoHygiene,
            },
        ];

    }

    async getGeoIndicatorMapping(data: GeoMappingRequestDto): Promise<GeoMappingResponseDto> {
        try {
            const startDate = new Date(data.startDate);
            const endDate = new Date(data.endDate);

            let locations: any[] = [];

            // Get all locations at the specified administrative level
            switch (data.level) {
                case "PROVINCE":
                    locations = await this.prisma.province.findMany({
                        select: { id: true, name: true }
                    });
                    break;
                case "DISTRICT":
                    locations = await this.prisma.district.findMany({
                        select: { id: true, name: true }
                    });
                    break;
                case "SECTOR":
                    locations = await this.prisma.sector.findMany({
                        select: { id: true, name: true }
                    });
                    break;
                case "CELL":
                    locations = await this.prisma.cell.findMany({
                        select: { id: true, name: true }
                    });
                    break;
                case "VILLAGE":
                    locations = await this.prisma.village.findMany({
                        select: { id: true, name: true }
                    });
                    break;
                default:
                    throw new InternalServerErrorException('Invalid administrative level');
            }

            const locationData = [];

            // Process each location
            for (const location of locations) {
                const indicatorRequestProps: IndicatorRequestProps = {
                    level: data.level,
                    levelId: location.id.toString(),
                    startDate,
                    endDate,
                };

                // Get facilities and their coordinates for this location
                let facilities: any[] = [];
                let coordinatesQuery: any = {};

                switch (data.facilityType) {
                    case "HOUSEHOLD":
                        coordinatesQuery = this.getHouseholdCoordinatesQuery(data.level, location.id);
                        break;
                    case "SCHOOL":
                        coordinatesQuery = this.getSchoolCoordinatesQuery(data.level, location.id);
                        break;
                    case "HEALTH_FACILITY":
                        coordinatesQuery = this.getHealthFacilityCoordinatesQuery(data.level, location.id);
                        break;
                    default:
                        continue; // Skip unsupported facility types
                }

                // Get coordinates for averaging
                const locationsWithCoords = await this.prisma.location.findMany({
                    where: coordinatesQuery,
                    select: {
                        latitude: true,
                        longitude: true,
                    },
                });

                // Calculate average coordinates
                const validCoords = locationsWithCoords.filter(loc => loc.latitude && loc.longitude);
                if (validCoords.length === 0) {
                    continue; // Skip locations without coordinates
                }

                const avgLatitude = validCoords.reduce((sum, loc) => sum + loc.latitude!, 0) / validCoords.length;
                const avgLongitude = validCoords.reduce((sum, loc) => sum + loc.longitude!, 0) / validCoords.length;

                // Calculate indicators for this location
                let indicators: any[] = [];

                if (data.facilityType === "HOUSEHOLD") {
                    const waterIndicators = await this.householdWaterSuppy(indicatorRequestProps);
                    const sanitationIndicators = await this.houseHoldSanitation(indicatorRequestProps);
                    const hygieneIndicators = await this.houseHoldHygiene(indicatorRequestProps);

                    indicators = [
                        { title: "Household Water Supply", items: waterIndicators },
                        { title: "Household Sanitation", items: sanitationIndicators },
                        { title: "Household Hygiene", items: hygieneIndicators },
                    ];
                }
                // Add other facility types as needed

                // Only include locations that have data
                if (indicators.some(indicator => indicator.items.length > 0)) {
                    locationData.push({
                        id: location.id,
                        name: location.name,
                        latitude: avgLatitude,
                        longitude: avgLongitude,
                        indicators,
                    });
                }
            }

            return {
                level: data.level,
                startDate: data.startDate,
                endDate: data.endDate,
                facilityType: data.facilityType,
                locations: locationData,
            };

        } catch (error) {
            console.log(error);
            throw new InternalServerErrorException('Failed to get geo mapping indicators');
        }
    }

    private getHouseholdCoordinatesQuery(level: string, locationId: number) {
        switch (level) {
            case "PROVINCE":
                return {
                    village: {
                        cell: {
                            sector: {
                                district: {
                                    provinceId: locationId,
                                },
                            },
                        },
                    },
                };
            case "DISTRICT":
                return {
                    village: {
                        cell: {
                            sector: {
                                districtId: locationId,
                            },
                        },
                    },
                };
            case "SECTOR":
                return {
                    village: {
                        cell: {
                            sectorId: locationId,
                        },
                    },
                };
            case "CELL":
                return {
                    village: {
                        cellId: locationId,
                    },
                };
            case "VILLAGE":
                return {
                    villageId: locationId,
                };
            default:
                return {};
        }
    }

    private getSchoolCoordinatesQuery(level: string, locationId: number) {
        return this.getHouseholdCoordinatesQuery(level, locationId);
    }

    private getHealthFacilityCoordinatesQuery(level: string, locationId: number) {
        return this.getHouseholdCoordinatesQuery(level, locationId);
    }
}
