import { Location } from "./location"
import { PublicPlaceType, SettlementType } from "./enums"

export interface CreateLocationData {
    villageId: number
    latitude?: number
    longitude?: number
    settlementType?: SettlementType
}

export interface BaseFacilityInput {
    locationId?: string
    locationData?: CreateLocationData
}

export interface BaseFacilityWithNameInput extends BaseFacilityInput {
    name: string
}

export interface BaseFacilityResponse {
    id: string
    number: number
    location: Location
    createdAt: string
    updatedAt: string
}

export interface BaseFacilityWithNameResponse extends BaseFacilityResponse {
    name: string
}

export interface PaginationQuery {
    villageId: number
    page?: number
    limit?: number
    search?: string
}

export interface BasePaginatedResponse<T> {
    total: number
    page: number
    limit: number
    totalPages: number
    data: T[]
}

export interface BaseCreateResponse<T> {
    message: string
    facility: T
}

export interface BaseUpdateResponse<T> {
    message: string
    facility: T
}

export interface BaseDeleteResponse {
    message: string
}

export interface CreateHealthFacilityInput {
    name: string
    locationId?: string
    locationData?: {
        villageId: number
        latitude?: number
        longitude?: number
        settlementType?: SettlementType
    }
}

export type UpdateHealthFacilityInput = Partial<CreateHealthFacilityInput>
export interface HealthFacility extends BaseFacilityWithNameResponse { }
export interface HealthFacilitiesListResponse extends BasePaginatedResponse<HealthFacility> { }

export interface CreateHealthFacilityResponse {
    message: string
    facility: HealthFacility
}

export interface UpdateHealthFacilityResponse {
    message: string
    facility: HealthFacility
}

export interface CreateHouseholdInput {
    headOfHouseholdName?: string
    locationData: {
        villageId: number
        latitude: number
        longitude: number
        settlementType: SettlementType
    }
}

export type UpdateHouseholdInput = Partial<CreateHouseholdInput>
export interface Household extends BaseFacilityResponse {
    headOfHouseholdName: string
}
export interface HouseholdsListResponse extends BasePaginatedResponse<Household> { }
export interface CreateHouseholdResponse {
    message: string
    facility: Household
}

export interface UpdateHouseholdResponse {
    message: string
    facility: Household
}


export interface CreatePublicPlaceInput {
    name: string
    locationId?: string
    type: PublicPlaceType
    locationData?: {
        villageId: number
        latitude?: number
        longitude?: number
        settlementType?: SettlementType
    }
}

export type UpdatePublicPlaceInput = Partial<CreatePublicPlaceInput>
export interface PublicPlace extends BaseFacilityWithNameResponse {
    type: PublicPlaceType
}
export interface PublicPlacesListResponse extends BasePaginatedResponse<PublicPlace> { }
export interface CreatePublicPlaceResponse {
    message: string
    facility: PublicPlace
}

export interface UpdatePublicPlaceResponse {
    message: string
    facility: PublicPlace
}

export interface CreateSchoolInput {
    name: string
    locationId?: string
    locationData?: {
        villageId: number
        latitude?: number
        longitude?: number
        settlementType?: SettlementType
    }
}

export type UpdateSchoolInput = Partial<CreateSchoolInput>
export interface School extends BaseFacilityWithNameResponse { }
export interface SchoolsListResponse extends BasePaginatedResponse<School> { }
export interface CreateSchoolResponse {
    message: string
    facility: School
}

export interface UpdateSchoolResponse {
    message: string
    facility: School
}