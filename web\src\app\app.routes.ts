import { Routes } from '@angular/router';
import { Setup2FAComponent } from './components/auth/login/2fa/2fa.component';
import { Verify2FAComponent } from './components/auth/verify-2fa/verify-2fa.component';

import { LoginComponent } from './components/auth/login/login.component';
import { LayoutComponent } from './components/shared/layout/layout.component';
import { ForgotPasswordComponent } from './components/auth/forgot-password/forgot-password.component';
import { ResetPasswordComponent } from './components/auth/reset-password/reset-password.component';
import { SetPasswordComponent } from './components/auth/set-password/set-password.component';
import { DashboardComponent } from './components/dashboard/dashboard.component';

// Import guard
import { AuthGuard } from './utils/guards/auth.guard';
import { AllUsersComponent } from './components/user-management/all-users/all-users.component';

export const routes: Routes = [
  // Default route - redirects to login
  { path: '', redirectTo: '/login', pathMatch: 'full' },
  
  // Auth routes (public)
  { path: 'login', component: LoginComponent },
  { path: 'setup-2fa', component: Setup2FAComponent },
  { path: 'verify-2fa', component: Verify2FAComponent },
  {
    path: 'dashboard',
    component: LayoutComponent,
    // children: [
    //   {
    //     path: '',
    //     // template: '<div class="p-8"><h2>Select a menu item to get started</h2></div>'
    //   }
    // ]
  },
  { path: 'forgot-password', component: ForgotPasswordComponent },
  { path: 'auth/reset-password', component: SetPasswordComponent },
  { path: 'set-password', component: SetPasswordComponent },
  {
    path: 'app',
    component: LayoutComponent,
    canActivate: [AuthGuard], 
    children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
      },
      {
        path: 'dashboard',
        component: DashboardComponent
      },
      {
        path: 'all-users',
        component: AllUsersComponent
      },
      {
        path: 'roles',
        loadComponent: () => import('./components/role-management/roles.component').then(m => m.RolesComponent)
      }
    ]
  },
  
  // Catch-all route
  { path: '**', redirectTo: '/login' }
];