<div class="min-h-screen flex items-center justify-center bg-gray-50">
  <div class="max-w-md w-full bg-white p-8 rounded shadow">
    <h2 class="text-2xl font-bold mb-4 text-center">Set Up Two-Factor Authentication</h2>
    
    <!-- Loading State -->
    <div *ngIf="isLoading" class="text-center py-4">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
      <p class="mt-2 text-gray-600">Loading...</p>
    </div>

    <!-- QR Code Section -->
    <div *ngIf="!isLoading && qrCode" class="mb-6">
      <p class="text-sm text-gray-600 mb-4">Scan this QR code with your authenticator app:</p>
      <div class="flex justify-center mb-4">
        <img [src]="qrCode" alt="2FA QR Code" class="border border-gray-200 p-2 rounded" />
      </div>
      <p class="text-sm text-gray-600 mb-2">Or enter this key manually:</p>
      <div class="bg-gray-50 p-3 rounded text-center font-mono text-sm">
        {{ manualEntryKey }}
      </div>
    </div>

    <!-- Setup Form -->
    <form [formGroup]="twoFAForm" (ngSubmit)="enable2FA()" class="space-y-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">
          Enter 6-digit code from your authenticator app
        </label>
        <input 
          type="text" 
          formControlName="code" 
          maxlength="6" 
          class="w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="123456"
        />
        <div *ngIf="twoFAForm.get('code')?.invalid && twoFAForm.get('code')?.touched" class="text-red-500 text-xs mt-1">
          Please enter a valid 6-digit code
        </div>
      </div>

      <!-- Messages -->
      <div *ngIf="successMessage" class="text-green-600 text-sm">
        {{ successMessage }}
      </div>
      <div *ngIf="errorMessage" class="text-red-600 text-sm">
        {{ errorMessage }}
      </div>

      <!-- Recovery Codes -->
      <div *ngIf="recoveryCodes.length" class="mt-4 p-4 bg-yellow-50 rounded">
        <p class="text-sm font-medium text-yellow-800 mb-2">Save these recovery codes:</p>
        <ul class="text-sm text-yellow-700 space-y-1">
          <li *ngFor="let code of recoveryCodes" class="font-mono">{{ code }}</li>
        </ul>
        <p class="text-xs text-yellow-600 mt-2">
          These codes can be used to access your account if you lose your authenticator device.
        </p>
      </div>

      <button 
        type="submit" 
        [disabled]="twoFAForm.invalid || isLoading"
        class="w-full py-2.5 px-4 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Enable 2FA
      </button>
    </form>
  </div>
</div>
