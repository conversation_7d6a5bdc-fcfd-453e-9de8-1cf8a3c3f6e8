"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubmissionService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const client_1 = require("@prisma/client");
let SubmissionService = class SubmissionService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createHouseholdSubmission(dto, userId) {
        const household = await this.prisma.houseHold.findUnique({
            where: { id: dto.facilityId },
        });
        if (!household || household.deleted) {
            throw new common_1.NotFoundException('Household not found');
        }
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        try {
            const result = await this.prisma.$transaction(async (tx) => {
                const submission = await tx.submission.create({
                    data: {
                        facilityType: dto.facilityType,
                        submittedAt: dto.submittedAt,
                        houseHoldId: dto.facilityId,
                        submittedById: userId,
                        HouseHoldGeneralInfo: {
                            create: dto.generalInfo,
                        },
                        HouseHoldWaterSupply: {
                            create: dto.waterSupply,
                        },
                        HouseHoldSanitation: {
                            create: dto.sanitation,
                        },
                        HouseHoldHygiene: {
                            create: dto.hygiene,
                        },
                        HouseHoldSolidWasteManagement: {
                            create: dto.solidWaste,
                        },
                        HouseHoldLiquidWasteManagement: {
                            create: dto.liquidWaste,
                        },
                    }
                });
                return submission;
            });
            return {
                message: 'Household submission created',
                submission: result,
            };
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException('Failed to create household submission');
        }
    }
    async createSchoolSubmission(dto, userId) {
        const school = await this.prisma.school.findUnique({
            where: { id: dto.facilityId },
        });
        if (!school || school.deleted) {
            throw new common_1.NotFoundException('School not found');
        }
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        try {
            const result = await this.prisma.$transaction(async (tx) => {
                const submission = await tx.submission.create({
                    data: {
                        facilityType: dto.facilityType,
                        submittedAt: dto.submittedAt,
                        schoolId: dto.facilityId,
                        submittedById: userId,
                        SchoolGeneralInfo: {
                            create: dto.generalInfo,
                        },
                        SchoolWaterSupply: {
                            create: dto.waterSupply,
                        },
                        SchoolSanitation: {
                            create: dto.sanitation,
                        },
                        SchoolHygiene: {
                            create: dto.hygiene,
                        },
                        SchoolSolidWasteManagement: {
                            create: dto.solidWaste,
                        },
                        SchoolLiquidWasteManagement: {
                            create: dto.liquidWaste,
                        },
                    }
                });
                return submission;
            });
            return {
                message: 'School submission created',
                submission: result,
            };
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException('Failed to create school submission');
        }
    }
    async createHealthFacilitySubmission(dto, userId) {
        const healthFacility = await this.prisma.healthFacility.findUnique({
            where: { id: dto.facilityId },
        });
        if (!healthFacility || healthFacility.deleted) {
            throw new common_1.NotFoundException('Health facility not found');
        }
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        try {
            const result = await this.prisma.$transaction(async (tx) => {
                const submission = await tx.submission.create({
                    data: {
                        facilityType: dto.facilityType,
                        submittedAt: dto.submittedAt,
                        healthFacilityId: dto.facilityId,
                        submittedById: userId,
                        HealthFacilityGeneralInfo: {
                            create: dto.generalInfo,
                        },
                        HealthFacilityWaterSupply: {
                            create: dto.waterSupply,
                        },
                        HealthFacilitySanitation: {
                            create: dto.sanitation,
                        },
                        HealthFacilityHygiene: {
                            create: dto.hygiene,
                        },
                        HealthFacilitySolidWasteManagement: {
                            create: dto.solidWaste,
                        },
                        HealthFacilityLiquidWasteManagement: {
                            create: dto.liquidWaste,
                        },
                    }
                });
                return submission;
            });
            return {
                message: 'Health facility submission created',
                submission: result,
            };
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException('Failed to create health facility submission');
        }
    }
    async createPublicPlaceSubmission(dto, userId) {
        const publicPlace = await this.prisma.publicPlace.findUnique({
            where: { id: dto.facilityId },
        });
        if (!publicPlace || publicPlace.deleted) {
            throw new common_1.NotFoundException('Public place not found');
        }
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        try {
            const result = await this.prisma.$transaction(async (tx) => {
                const submission = await tx.submission.create({
                    data: {
                        facilityType: dto.facilityType,
                        submittedAt: dto.submittedAt,
                        publicPlaceId: dto.facilityId,
                        submittedById: userId,
                        PublicPlaceGeneralInfo: {
                            create: {
                                category: dto.generalInfo.category,
                                openingDays: dto.generalInfo.openingDays,
                            },
                        },
                        PublicPlaceWaterSupply: {
                            create: dto.waterSupply,
                        },
                        PublicPlaceSanitation: {
                            create: dto.sanitation,
                        },
                        PublicPlaceHygiene: {
                            create: dto.hygiene,
                        },
                        PublicPlaceSolidWasteManagement: {
                            create: dto.solidWaste,
                        },
                        PublicPlaceLiquidWasteManagement: {
                            create: dto.liquidWaste,
                        },
                    }
                });
                return submission;
            });
            return {
                message: 'Public place submission created',
                submission: result,
            };
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException('Failed to create public place submission');
        }
    }
    async createWasteCollectionCompanySubmission(dto, userId) {
        return this.prisma.$transaction(async (tx) => {
            const submission = await tx.submission.create({
                data: {
                    facilityType: client_1.FacilityType.WASTE_COLLECTION_COMPANY,
                    submittedById: userId,
                },
            });
            const wasteCollectionCompany = await tx.wasteCollectionCompany.create({
                data: {
                    submissionId: submission.id,
                    companyName: dto.companyName,
                    ownerName: dto.ownerName,
                    ownerGender: dto.ownerGender,
                    contactPhone: dto.contactPhone,
                    contactEmail: dto.contactEmail,
                    companyType: dto.companyType,
                    otherCompanyType: dto.otherCompanyType,
                    totalPersonnel: dto.totalPersonnel,
                    femalePersonnel: dto.femalePersonnel,
                    malePersonnel: dto.malePersonnel,
                    clientTypes: dto.clientTypes,
                    otherClientTypes: dto.otherClientTypes || [],
                    wasteSeparation: dto.wasteSeparation,
                    separatedMaterials: dto.separatedMaterials,
                    otherSeparatedMaterials: dto.otherSeparatedMaterials || [],
                    wasteDestination: dto.wasteDestination,
                    destinationDetails: dto.destinationDetails,
                    weighbridge: dto.weighbridge,
                    recordingMethod: dto.recordingMethod,
                    otherRecordingMethod: dto.otherRecordingMethod,
                },
            });
            return {
                id: wasteCollectionCompany.id,
                facilityType: submission.facilityType,
                submissionId: submission.id,
                submittedAt: submission.submittedAt,
                ...wasteCollectionCompany,
            };
        });
    }
    async createWasteRecoveryCompanySubmission(dto, userId) {
        return this.prisma.$transaction(async (tx) => {
            const submission = await tx.submission.create({
                data: {
                    facilityType: client_1.FacilityType.WASTE_RECOVERY_COMPANY,
                    submittedById: userId,
                },
            });
            const handledMaterials = await Promise.all(dto.handledMaterials.map(material => tx.handledMaterial.create({
                data: {
                    materialName: material.materialName,
                    supplier: material.supplier,
                    quantityPerDay: material.quantityPerDay,
                },
            })));
            const businessSites = await Promise.all(dto.businessSites.map(async (site) => {
                const village = await tx.village.findUnique({
                    where: { id: site.villageId },
                });
                if (!village) {
                    throw new common_1.NotFoundException(`Village with id ${site.villageId} not found`);
                }
                const location = await tx.location.create({
                    data: {
                        villageId: site.villageId,
                        latitude: site.latitude,
                        longitude: site.longitude,
                        settlementType: 'RURAL',
                    },
                });
                return tx.businessSite.create({
                    data: {
                        name: site.name,
                        type: site.type,
                        locationId: location.id,
                    },
                });
            }));
            const wasteRecoveryCompany = await tx.wasteRecoveryCompany.create({
                data: {
                    submissionId: submission.id,
                    companyName: dto.companyName,
                    contactPerson: dto.contactPerson,
                    contactPhone: dto.contactPhone,
                    contactEmail: dto.contactEmail,
                    companyType: dto.companyType,
                    otherCompanyType: dto.otherCompanyType,
                    totalPersonnel: dto.totalPersonnel,
                    femalePersonnel: dto.femalePersonnel,
                    malePersonnel: dto.malePersonnel,
                    operationType: dto.operationType,
                    handledMaterials: {
                        connect: handledMaterials.map(hm => ({ id: hm.id })),
                    },
                    businessSites: {
                        connect: businessSites.map(bs => ({ id: bs.id })),
                    },
                },
                include: {
                    handledMaterials: true,
                    businessSites: {
                        include: {
                            location: true,
                        },
                    },
                },
            });
            return {
                id: wasteRecoveryCompany.id,
                facilityType: submission.facilityType,
                submissionId: submission.id,
                submittedAt: submission.submittedAt,
                ...wasteRecoveryCompany,
            };
        });
    }
    async createWasteDisposalCompanySubmission(dto, userId) {
        return this.prisma.$transaction(async (tx) => {
            const submission = await tx.submission.create({
                data: {
                    facilityType: client_1.FacilityType.WASTE_DISPOSAL_COMPANY,
                    submittedById: userId,
                },
            });
            const village = await tx.village.findUnique({
                where: { id: dto.facilityVillageId },
            });
            if (!village) {
                throw new common_1.NotFoundException(`Village with id ${dto.facilityVillageId} not found`);
            }
            const facilityLocation = await tx.location.create({
                data: {
                    villageId: dto.facilityVillageId,
                    latitude: dto.facilityLatitude,
                    longitude: dto.facilityLongitude,
                    settlementType: 'RURAL',
                },
            });
            const wasteDisposalCompany = await tx.wasteDisposalCompany.create({
                data: {
                    submissionId: submission.id,
                    companyName: dto.companyName,
                    facilityLocationId: facilityLocation.id,
                    contactPerson: dto.contactPerson,
                    contactPhone: dto.contactPhone,
                    contactEmail: dto.contactEmail,
                    companyType: dto.companyType,
                    otherCompanyType: dto.otherCompanyType,
                    totalPersonnel: dto.totalPersonnel,
                    femalePersonnel: dto.femalePersonnel,
                    malePersonnel: dto.malePersonnel,
                    clientTypes: dto.clientTypes,
                    otherClientTypes: dto.otherClientTypes || [],
                    boundaryControl: dto.boundaryControl,
                    wasteDepositControl: dto.wasteDepositControl,
                    compactionFrequency: dto.compactionFrequency,
                    wasteBurning: dto.wasteBurning,
                    weighbridge: dto.weighbridge,
                    wasteAmount: dto.wasteAmount,
                    truckFrequency: dto.truckFrequency,
                    recordingMethod: dto.recordingMethod,
                    otherRecordingMethod: dto.otherRecordingMethod,
                },
                include: {
                    facilityLocation: true,
                },
            });
            return {
                id: wasteDisposalCompany.id,
                facilityType: submission.facilityType,
                submissionId: submission.id,
                submittedAt: submission.submittedAt,
                ...wasteDisposalCompany,
            };
        });
    }
};
exports.SubmissionService = SubmissionService;
exports.SubmissionService = SubmissionService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], SubmissionService);
//# sourceMappingURL=submission.service.js.map