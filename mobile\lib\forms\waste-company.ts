import * as yup from 'yup';
import {
  Gender,
  ServiceProviderType,
  ClientType,
  WasteMaterial,
  WasteDestination,
  RecordingMethod,
  OperationType,
  CompactionFrequency,
  TruckFrequency,
} from '@/types/enums';

// ============================================================================
// WASTE COLLECTION COMPANY SCHEMA
// ============================================================================

export const wasteCollectionCompanySchema = yup.object().shape({
  // Section 1: Basic Company Information
  basicInfo: yup.object().shape({
    companyName: yup
      .string()
      .required('Company name is required'),
    ownerName: yup
      .string()
      .required('Owner name is required'),
    ownerGender: yup
      .mixed<Gender>()
      .oneOf(Object.values(Gender) as Gender[], 'Invalid gender')
      .required('Owner gender is required'),
  }),

  // Section 2: Contact Details
  contactDetails: yup.object().shape({
    contactPhone: yup
      .string()
      .required('Contact telephone is required')
      .matches(/^[+]?[\d\s\-()]+$/, 'Invalid phone number format'),
    contactEmail: yup
      .string()
      .email('Invalid email format')
      .required('Contact email is required'),
  }),

  // Section 3: Service Provider Management Capacity and Gender Inclusion
  managementCapacity: yup.object().shape({
    companyType: yup
      .mixed<ServiceProviderType>()
      .oneOf(Object.values(ServiceProviderType) as ServiceProviderType[], 'Invalid service provider type')
      .required('Service provider type is required'),
    otherCompanyType: yup
      .string()
      .when('companyType', {
        is: ServiceProviderType.OTHER,
        then: (schema) => schema.required('Please specify other company type'),
        otherwise: (schema) => schema.optional(),
      }),
    totalPersonnel: yup
      .number()
      .positive('Total personnel must be positive')
      .integer('Total personnel must be a whole number')
      .required('Total number of personnel is required'),
    femalePersonnel: yup
      .number()
      .min(0, 'Female personnel cannot be negative')
      .integer('Female personnel must be a whole number')
      .required('Number of female personnel is required')
      .test('not-exceed-total', 'Female personnel cannot exceed total personnel', function(value) {
        const { totalPersonnel } = this.parent;
        return !value || !totalPersonnel || value <= totalPersonnel;
      }),
    malePersonnel: yup
      .number()
      .min(0, 'Male personnel cannot be negative')
      .integer('Male personnel must be a whole number')
      .required('Number of male personnel is required')
      .test('not-exceed-total', 'Male personnel cannot exceed total personnel', function(value) {
        const { totalPersonnel } = this.parent;
        return !value || !totalPersonnel || value <= totalPersonnel;
      }),
  }).test('personnel-sum', 'Male and female personnel must equal total personnel', function(value) {
    const { totalPersonnel, femalePersonnel, malePersonnel } = value;
    if (totalPersonnel && femalePersonnel !== undefined && malePersonnel !== undefined) {
      return femalePersonnel + malePersonnel === totalPersonnel;
    }
    return true;
  }),

  // Section 4: Client Coverage
  clientCoverage: yup.object().shape({
    clientTypes: yup
      .array()
      .of(yup.mixed<ClientType>().oneOf(Object.values(ClientType) as ClientType[]))
      .min(1, 'At least one client type must be selected')
      .required('Client types are required'),
    otherClientTypes: yup
      .array()
      .of(yup.string())
      .when('clientTypes', {
        is: (clientTypes: ClientType[]) => clientTypes?.includes(ClientType.OTHERS),
        then: (schema) => schema.min(1, 'Please specify other client types'),
        otherwise: (schema) => schema.optional(),
      }),
  }),

  // Section 5: Technical Capacity
  technicalCapacity: yup.object().shape({
    wasteSeparation: yup
      .boolean()
      .required('Waste separation information is required'),
    separatedMaterials: yup
      .array()
      .of(yup.mixed<WasteMaterial>().oneOf(Object.values(WasteMaterial) as WasteMaterial[]))
      .when('wasteSeparation', {
        is: true,
        then: (schema) => schema.min(1, 'At least one separated material must be selected'),
        otherwise: (schema) => schema.optional(),
      }),
    otherSeparatedMaterials: yup
      .array()
      .of(yup.string())
      .optional(),
    wasteDestination: yup
      .mixed<WasteDestination>()
      .oneOf(Object.values(WasteDestination) as WasteDestination[], 'Invalid waste destination')
      .required('Waste destination is required'),
    destinationDetails: yup
      .string()
      .when('wasteDestination', {
        is: WasteDestination.OTHERS,
        then: (schema) => schema.required('Please specify destination details'),
        otherwise: (schema) => schema.optional(),
      }),
  }),

  // Section 6: Recording and Monitoring
  recordingMonitoring: yup.object().shape({
    weighbridge: yup
      .boolean()
      .required('Weighbridge information is required'),
    recordingMethod: yup
      .mixed<RecordingMethod>()
      .nullable()
      .when('weighbridge', {
        is: false,
        then: (schema) => schema
          .oneOf(Object.values(RecordingMethod) as RecordingMethod[], 'Invalid recording method')
          .required('Recording method is required when no weighbridge'),
        otherwise: (schema) => schema.optional(),
      }),
    otherRecordingMethod: yup
      .string()
      .when(['weighbridge', 'recordingMethod'], {
        is: (weighbridge: boolean, recordingMethod: RecordingMethod) =>
          weighbridge === false && recordingMethod === RecordingMethod.OTHER,
        then: (schema) => schema.required('Please specify other recording method'),
        otherwise: (schema) => schema.optional(),
      }),
  }),
});

// ============================================================================
// WASTE RECOVERY COMPANY SCHEMA
// ============================================================================

export const wasteRecoveryCompanySchema = yup.object().shape({
  // Section 1: Basic Company Information
  basicInfo: yup.object().shape({
    companyName: yup
      .string()
      .required('Company name is required'),
  }),

  // Section 2: Contact Details
  contactDetails: yup.object().shape({
    contactPerson: yup
      .string()
      .required('Contact person name is required'),
    contactPhone: yup
      .string()
      .required('Contact telephone is required')
      .matches(/^[+]?[\d\s\-()]+$/, 'Invalid phone number format'),
    contactEmail: yup
      .string()
      .email('Invalid email format')
      .required('Contact email is required'),
  }),

  // Section 3: Service Provider Management Capacity and Gender Inclusion
  managementCapacity: yup.object().shape({
    companyType: yup
      .mixed<ServiceProviderType>()
      .oneOf(Object.values(ServiceProviderType) as ServiceProviderType[], 'Invalid service provider type')
      .required('Service provider type is required'),
    otherCompanyType: yup
      .string()
      .when('companyType', {
        is: ServiceProviderType.OTHER,
        then: (schema) => schema.required('Please specify other company type'),
        otherwise: (schema) => schema.optional(),
      }),
    totalPersonnel: yup
      .number()
      .positive('Total personnel must be positive')
      .integer('Total personnel must be a whole number')
      .required('Total number of personnel is required'),
    femalePersonnel: yup
      .number()
      .min(0, 'Female personnel cannot be negative')
      .integer('Female personnel must be a whole number')
      .required('Number of female personnel is required')
      .test('not-exceed-total', 'Female personnel cannot exceed total personnel', function(value) {
        const { totalPersonnel } = this.parent;
        return !value || !totalPersonnel || value <= totalPersonnel;
      }),
    malePersonnel: yup
      .number()
      .min(0, 'Male personnel cannot be negative')
      .integer('Male personnel must be a whole number')
      .required('Number of male personnel is required')
      .test('not-exceed-total', 'Male personnel cannot exceed total personnel', function(value) {
        const { totalPersonnel } = this.parent;
        return !value || !totalPersonnel || value <= totalPersonnel;
      }),
  }).test('personnel-sum', 'Male and female personnel must equal total personnel', function(value) {
    const { totalPersonnel, femalePersonnel, malePersonnel } = value;
    if (totalPersonnel && femalePersonnel !== undefined && malePersonnel !== undefined) {
      return femalePersonnel + malePersonnel === totalPersonnel;
    }
    return true;
  }),

  // Section 4: Operation Type
  operationType: yup.object().shape({
    operationType: yup
      .mixed<OperationType>()
      .oneOf(Object.values(OperationType) as OperationType[], 'Invalid operation type')
      .required('Operation type is required'),
  }),

  // Section 5: Handled Materials
  handledMaterials: yup.object().shape({
    materials: yup
      .array()
      .of(
        yup.object().shape({
          materialName: yup
            .string()
            .required('Material name is required'),
          supplier: yup
            .string()
            .required('Supplier information is required'),
          quantityPerDay: yup
            .number()
            .positive('Quantity must be positive')
            .required('Daily quantity is required'),
        })
      )
      .min(1, 'At least one material must be specified')
      .required('Handled materials are required'),
  }),

  // Section 6: Business Sites
  businessSites: yup.object().shape({
    sites: yup
      .array()
      .of(
        yup.object().shape({
          name: yup
            .string()
            .required('Site name is required'),
          type: yup
            .string()
            .required('Site type is required'),
          villageId: yup
            .number()
            .positive('Village must be selected')
            .required('Village is required'),
          latitude: yup
            .number()
            .optional(),
          longitude: yup
            .number()
            .optional(),
        })
      )
      .min(1, 'At least one business site must be specified')
      .required('Business sites are required'),
  }),
});

// ============================================================================
// WASTE DISPOSAL COMPANY SCHEMA
// ============================================================================

export const wasteDisposalCompanySchema = yup.object().shape({
  // Section 1: Basic Company Information
  basicInfo: yup.object().shape({
    companyName: yup
      .string()
      .required('Company name is required'),
    facilityVillageId: yup
      .number()
      .positive('Facility village must be selected')
      .required('Facility village is required'),
    facilityLatitude: yup
      .number()
      .optional(),
    facilityLongitude: yup
      .number()
      .optional(),
  }),

  // Section 2: Contact Details
  contactDetails: yup.object().shape({
    contactPerson: yup
      .string()
      .required('Contact person name is required'),
    contactPhone: yup
      .string()
      .required('Contact telephone is required')
      .matches(/^[+]?[\d\s\-()]+$/, 'Invalid phone number format'),
    contactEmail: yup
      .string()
      .email('Invalid email format')
      .required('Contact email is required'),
  }),

  // Section 3: Service Provider Management Capacity and Gender Inclusion
  managementCapacity: yup.object().shape({
    companyType: yup
      .mixed<ServiceProviderType>()
      .oneOf(Object.values(ServiceProviderType) as ServiceProviderType[], 'Invalid service provider type')
      .required('Service provider type is required'),
    otherCompanyType: yup
      .string()
      .when('companyType', {
        is: ServiceProviderType.OTHER,
        then: (schema) => schema.required('Please specify other company type'),
        otherwise: (schema) => schema.optional(),
      }),
    totalPersonnel: yup
      .number()
      .positive('Total personnel must be positive')
      .integer('Total personnel must be a whole number')
      .required('Total number of personnel is required'),
    femalePersonnel: yup
      .number()
      .min(0, 'Female personnel cannot be negative')
      .integer('Female personnel must be a whole number')
      .required('Number of female personnel is required')
      .test('not-exceed-total', 'Female personnel cannot exceed total personnel', function(value) {
        const { totalPersonnel } = this.parent;
        return !value || !totalPersonnel || value <= totalPersonnel;
      }),
    malePersonnel: yup
      .number()
      .min(0, 'Male personnel cannot be negative')
      .integer('Male personnel must be a whole number')
      .required('Number of male personnel is required')
      .test('not-exceed-total', 'Male personnel cannot exceed total personnel', function(value) {
        const { totalPersonnel } = this.parent;
        return !value || !totalPersonnel || value <= totalPersonnel;
      }),
  }).test('personnel-sum', 'Male and female personnel must equal total personnel', function(value) {
    const { totalPersonnel, femalePersonnel, malePersonnel } = value;
    if (totalPersonnel && femalePersonnel !== undefined && malePersonnel !== undefined) {
      return femalePersonnel + malePersonnel === totalPersonnel;
    }
    return true;
  }),

  // Section 4: Market Coverage
  marketCoverage: yup.object().shape({
    clientTypes: yup
      .array()
      .of(yup.mixed().oneOf([
        ...Object.values(ClientType),
        'SPECIALIZED_WASTE_COLLECTION_OPERATORS',
        'SPECIALIZED_WASTE_RECOVERY_OPERATORS'
      ]))
      .min(1, 'At least one client type must be selected')
      .required('Client types are required'),
    otherClientTypes: yup
      .array()
      .of(yup.string())
      .when('clientTypes', {
        is: (clientTypes: any[]) =>
          clientTypes?.includes(ClientType.OTHERS) ||
          clientTypes?.includes('SPECIALIZED_WASTE_COLLECTION_OPERATORS') ||
          clientTypes?.includes('SPECIALIZED_WASTE_RECOVERY_OPERATORS'),
        then: (schema) => schema.optional(), // Don't require since specialized operators are handled automatically
        otherwise: (schema) => schema.optional(),
      }),
  }),

  // Section 5: Operational Practices
  operationalPractices: yup.object().shape({
    boundaryControl: yup
      .boolean()
      .required('Boundary control information is required'),
    wasteDepositControl: yup
      .boolean()
      .required('Waste deposit control information is required'),
    compactionFrequency: yup
      .mixed<CompactionFrequency>()
      .oneOf(Object.values(CompactionFrequency) as CompactionFrequency[], 'Invalid compaction frequency')
      .required('Compaction frequency is required'),
    wasteBurning: yup
      .boolean()
      .required('Waste burning information is required'),
    weighbridge: yup
      .boolean()
      .required('Weighbridge information is required'),
    wasteAmount: yup
      .number()
      .positive('Waste amount must be positive')
      .required('Daily waste amount is required'),
    truckFrequency: yup
      .mixed<TruckFrequency>()
      .oneOf(Object.values(TruckFrequency) as TruckFrequency[], 'Invalid truck frequency')
      .required('Truck frequency is required'),
    recordingMethod: yup
      .mixed<RecordingMethod>()
      .nullable()
      .when('weighbridge', {
        is: false,
        then: (schema) => schema
          .oneOf(Object.values(RecordingMethod) as RecordingMethod[], 'Invalid recording method')
          .required('Recording method is required when no weighbridge'),
        otherwise: (schema) => schema.optional(),
      }),
    otherRecordingMethod: yup
      .string()
      .when(['weighbridge', 'recordingMethod'], {
        is: (weighbridge: boolean, recordingMethod: RecordingMethod) =>
          !weighbridge && recordingMethod === RecordingMethod.OTHER,
        then: (schema) => schema.required('Please specify other recording method'),
        otherwise: (schema) => schema.optional(),
      }),
  }),
});

// ============================================================================
// COMBINED SCHEMA TYPE DEFINITIONS
// ============================================================================

// Type definitions for form data
export type WasteCollectionCompanyFormData = yup.InferType<typeof wasteCollectionCompanySchema>;
export type WasteRecoveryCompanyFormData = yup.InferType<typeof wasteRecoveryCompanySchema>;
export type WasteDisposalCompanyFormData = yup.InferType<typeof wasteDisposalCompanySchema>;

// Helper function to get schema by company type
export const getWasteCompanySchema = (companyType: 'collection' | 'recovery' | 'disposal') => {
  switch (companyType) {
    case 'collection':
      return wasteCollectionCompanySchema;
    case 'recovery':
      return wasteRecoveryCompanySchema;
    case 'disposal':
      return wasteDisposalCompanySchema;
    default:
      throw new Error('Invalid company type');
  }
};