import React, { useState } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Controller } from 'react-hook-form';
import tw from 'twrnc';
import AppText from '@/components/ui/Text';
import { TextInput, Button } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { PRIMARY_COLOR } from '@/constants/colors';
import LocationSelectionModal from './LocationSelectionModal';

interface BusinessSitesSectionProps {
  control: any;
  errors: any;
  watch: any;
}

const BusinessSitesSection = ({ control, errors, watch }: BusinessSitesSectionProps) => {
  const sites = watch('businessSites.sites') || [];
  const [locationModalVisible, setLocationModalVisible] = useState(false);
  const [currentSiteIndex, setCurrentSiteIndex] = useState<number | null>(null);

  const addSite = (onChange: (value: any) => void) => {
    const newSite = {
      name: '',
      type: '',
      villageId: null,
      locationDisplay: '',
      latitude: null,
      longitude: null,
    };
    onChange([...sites, newSite]);
  };

  const removeSite = (index: number, onChange: (value: any) => void) => {
    const updatedSites = sites.filter((_: any, i: number) => i !== index);
    onChange(updatedSites);
  };

  const updateSite = (index: number, field: string, value: any, onChange: (value: any) => void) => {
    const updatedSites = [...sites];
    updatedSites[index] = { ...updatedSites[index], [field]: value };
    onChange(updatedSites);
  };

  const openLocationModal = (index: number) => {
    setCurrentSiteIndex(index);
    setLocationModalVisible(true);
  };

  const handleLocationSelect = (location: {
    villageId: number;
    villageName: string;
    fullAddress: string;
    latitude?: number;
    longitude?: number;
  }, onChange: (value: any) => void) => {
    if (currentSiteIndex !== null) {
      const updatedSites = [...sites];
      updatedSites[currentSiteIndex] = {
        ...updatedSites[currentSiteIndex],
        villageId: location.villageId,
        locationDisplay: location.fullAddress,
        latitude: location.latitude,
        longitude: location.longitude,
      };
      onChange(updatedSites);
      setLocationModalVisible(false);
      setCurrentSiteIndex(null);
    }
  };

  return (
    <View style={tw`gap-4`}>
      <Controller
        control={control}
        name="businessSites.sites"
        render={({ field: { onChange, value } }) => (
          <View style={tw`gap-4`}>
            {(value || []).map((site: any, index: number) => (
              <View key={index} style={tw`p-4 border border-gray-300 rounded-lg`}>
                <View style={tw`flex-row justify-between items-center mb-3`}>
                  <AppText weight="semibold">Site {index + 1}</AppText>
                  {value.length > 1 && (
                    <TouchableOpacity
                      onPress={() => removeSite(index, onChange)}
                      style={tw`p-1`}
                    >
                      <Ionicons name="trash" size={20} color="red" />
                    </TouchableOpacity>
                  )}
                </View>

                {/* Site Name */}
                <View style={tw`mb-3`}>
                  <AppText style={tw`mb-2`}>Name</AppText>
                  <TextInput
                    mode="outlined"
                    value={site.name || ''}
                    onChangeText={(text) => updateSite(index, 'name', text, onChange)}
                    placeholder="Enter site name"
                    error={!!errors.businessSites?.sites?.[index]?.name}
                    outlineColor="gray"
                  />
                  {errors.businessSites?.sites?.[index]?.name && (
                    <AppText style={tw`text-red-500 text-sm mt-1`}>
                      {errors.businessSites.sites[index].name.message}
                    </AppText>
                  )}
                </View>

                {/* Site Type */}
                <View style={tw`mb-3`}>
                  <AppText style={tw`mb-2`}>Type</AppText>
                  <TextInput
                    mode="outlined"
                    value={site.type || ''}
                    onChangeText={(text) => updateSite(index, 'type', text, onChange)}
                    placeholder="Enter site type (e.g., Processing facility, Storage warehouse)"
                    error={!!errors.businessSites?.sites?.[index]?.type}
                    outlineColor="gray"
                  />
                  {errors.businessSites?.sites?.[index]?.type && (
                    <AppText style={tw`text-red-500 text-sm mt-1`}>
                      {errors.businessSites.sites[index].type.message}
                    </AppText>
                  )}
                </View>

                {/* Location */}
                <View style={tw`mb-3`}>
                  <AppText style={tw`mb-2`}>Location</AppText>
                  {site.locationDisplay ? (
                    <View style={tw`border border-gray-300 rounded-lg p-3 bg-gray-50`}>
                      <AppText>{site.locationDisplay}</AppText>
                      {(site.latitude || site.longitude) && (
                        <AppText style={tw`text-gray-500 text-sm mt-1`}>
                          Coordinates: {site.latitude}, {site.longitude}
                        </AppText>
                      )}
                      <Button
                        mode="outlined"
                        onPress={() => openLocationModal(index)}
                        style={tw`mt-2`}
                        icon="map-marker"
                      >
                        Change Location
                      </Button>
                    </View>
                  ) : (
                    <Button
                      mode="outlined"
                    
                      onPress={() => openLocationModal(index)}
                      icon="map-marker"
                    >
                      Select Location
                    </Button>
                  )}
                  {errors.businessSites?.sites?.[index]?.villageId && (
                    <AppText style={tw`text-red-500 text-sm mt-1`}>
                      {errors.businessSites.sites[index].villageId.message}
                    </AppText>
                  )}
                </View>
              </View>
            ))}

            <TouchableOpacity
              onPress={() => addSite(onChange)}
              style={tw`flex-row items-center justify-center gap-2 p-4 border border-dashed border-gray-400 rounded-lg`}
            >
              <Ionicons name="add-circle" size={24} color={PRIMARY_COLOR} />
              <AppText style={tw`text-[${PRIMARY_COLOR}]`}>Add Business Site</AppText>
            </TouchableOpacity>

            {errors.businessSites?.sites && (
              <AppText style={tw`text-red-500 text-sm`}>
                {errors.businessSites.sites.message}
              </AppText>
            )}

            <LocationSelectionModal
              visible={locationModalVisible}
              onClose={() => {
                setLocationModalVisible(false);
                setCurrentSiteIndex(null);
              }}
              onSelect={(location) => handleLocationSelect(location, onChange)}
              initialLocation={
                currentSiteIndex !== null && sites[currentSiteIndex]
                  ? {
                      villageId: sites[currentSiteIndex].villageId,
                      latitude: sites[currentSiteIndex].latitude,
                      longitude: sites[currentSiteIndex].longitude,
                    }
                  : undefined
              }
            />
          </View>
        )}
      />
    </View>
  );
};

export default BusinessSitesSection;
