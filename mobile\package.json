{"name": "test-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo-google-fonts/inter": "^0.4.1", "@expo-google-fonts/sen": "^0.4.1", "@expo/vector-icons": "^14.1.0", "@hookform/resolvers": "^5.1.1", "@react-native-community/blur": "^4.4.1", "@react-native-community/datetimepicker": "8.3.0", "@react-native-picker/picker": "2.11.0", "@react-native-vector-icons/material-design-icons": "^12.0.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "axios": "^1.9.0", "date-fns": "^4.1.0", "expo": "~53.0.9", "expo-blur": "~14.1.5", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-linking": "~7.1.5", "expo-location": "^18.1.5", "expo-router": "~5.0.7", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "formik": "^2.4.6", "lucide-react-native": "^0.515.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.58.1", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-otp-entry": "^1.8.4", "react-native-otp-textinput": "^1.1.7", "react-native-paper": "^5.14.5", "react-native-paper-dropdown": "^2.3.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "~4.11.1", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "twrnc": "^4.8.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}