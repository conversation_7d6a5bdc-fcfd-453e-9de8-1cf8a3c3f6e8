import React from 'react';
import { View } from 'react-native';
import { Controller } from 'react-hook-form';
import { Picker } from '@react-native-picker/picker';
import tw from 'twrnc';
import AppText from '@/components/ui/Text';
import { TextInput } from 'react-native-paper';
import { Gender } from '@/types/enums';

interface BasicInfoSectionProps {
  control: any;
  errors: any;
}

const BasicInfoSection = ({ control, errors }: BasicInfoSectionProps) => {
  return (
    <View style={tw`gap-4`}>
      {/* Company Name */}
      <View>
        <AppText style={tw`mb-2`}>Name of the specialized waste collection company</AppText>
        <Controller
          control={control}
          name="basicInfo.companyName"
          render={({ field: { onChange, value } }) => (
            <TextInput
              mode="outlined"
              value={value || ''}
              onChangeText={onChange}
              placeholder="Enter company name"
              error={!!errors.basicInfo?.companyName}
              autoFocus
              returnKeyType="next"
              outlineColor="gray"
            />
          )}
        />
        {errors.basicInfo?.companyName && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.basicInfo.companyName.message}
          </AppText>
        )}
      </View>

      {/* Owner Name */}
      <View>
        <AppText style={tw`mb-2`}>Owner</AppText>
        <Controller
          control={control}
          name="basicInfo.ownerName"
          render={({ field: { onChange, value } }) => (
            <TextInput
              mode="outlined"
              value={value || ''}
              onChangeText={onChange}
              placeholder="Enter owner name"
              error={!!errors.basicInfo?.ownerName}
              returnKeyType="next"
              outlineColor="gray"
            />
          )}
        />
        {errors.basicInfo?.ownerName && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.basicInfo.ownerName.message}
          </AppText>
        )}
      </View>

      {/* Owner Gender */}
      <View>
        <AppText style={tw`mb-2`}>Gender</AppText>
        <View style={tw`border border-gray-300 rounded-lg`}>
          <Controller
            control={control}
            name="basicInfo.ownerGender"
            render={({ field: { onChange, value } }) => (
              <Picker
                selectedValue={value}
                onValueChange={onChange}
              >
                <Picker.Item label="Select Gender" value="" />
                <Picker.Item label="Male" value={Gender.MALE} />
                <Picker.Item label="Female" value={Gender.FEMALE} />
              </Picker>
            )}
          />
        </View>
        {errors.basicInfo?.ownerGender && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.basicInfo.ownerGender.message}
          </AppText>
        )}
      </View>
    </View>
  );
};

export default BasicInfoSection;
