import { Gender, ServiceProviderType, ClientType, WasteMaterial, WasteDestination, RecordingMethod } from '@prisma/client';
export declare class CreateWasteCollectionCompanySubmissionDto {
    companyName: string;
    ownerName: string;
    ownerGender: Gender;
    contactPhone: string;
    contactEmail: string;
    companyType: ServiceProviderType;
    otherCompanyType?: string;
    totalPersonnel: number;
    femalePersonnel: number;
    malePersonnel: number;
    clientTypes: ClientType[];
    otherClientTypes?: string[];
    wasteSeparation: boolean;
    separatedMaterials: WasteMaterial[];
    otherSeparatedMaterials?: string[];
    wasteDestination: WasteDestination;
    destinationDetails?: string;
    weighbridge: boolean;
    recordingMethod?: RecordingMethod;
    otherRecordingMethod?: string;
}
export declare class WasteCollectionCompanySubmissionResponseDto {
    id: string;
    submissionId: string;
    submittedAt: Date;
    companyName: string;
    ownerName: string;
    ownerGender: Gender;
    contactPhone: string;
    contactEmail: string;
    companyType: ServiceProviderType;
    otherCompanyType?: string;
    totalPersonnel: number;
    femalePersonnel: number;
    malePersonnel: number;
    clientTypes: ClientType[];
    otherClientTypes?: string[];
    wasteSeparation: boolean;
    separatedMaterials: WasteMaterial[];
    otherSeparatedMaterials?: string[];
    wasteDestination: WasteDestination;
    destinationDetails?: string;
    weighbridge: boolean;
    recordingMethod?: RecordingMethod;
    otherRecordingMethod?: string;
}
