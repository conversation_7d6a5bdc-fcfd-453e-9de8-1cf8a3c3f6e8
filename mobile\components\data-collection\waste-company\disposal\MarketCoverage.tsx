import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Controller } from 'react-hook-form';
import tw from 'twrnc';
import AppText from '@/components/ui/Text';
import { TextInput, Checkbox } from 'react-native-paper';
import { ClientType } from '@/types/enums';
import { Ionicons } from '@expo/vector-icons';

interface MarketCoverageSectionProps {
  control: any;
  errors: any;
  watch: any;
}

const MarketCoverageSection = ({ control, errors, watch }: MarketCoverageSectionProps) => {
  const selectedClientTypes = watch('marketCoverage.clientTypes') || [];
  const otherClientTypesFromForm = watch('marketCoverage.otherClientTypes') || [];

  const [otherClientInputs, setOtherClientInputs] = useState<string[]>(['']);

  // Initialize with one empty input when Others is selected, but don't auto-populate
  useEffect(() => {
    const hasOthers = selectedClientTypes.includes('OTHERS');
    if (hasOthers && otherClientInputs.length === 1 && otherClientInputs[0] === '') {
      // Keep the empty input when Others is first selected
      return;
    }

    // Only sync if there are actual values from the form (not auto-populated)
    if (otherClientTypesFromForm.length > 0 && !otherClientTypesFromForm.includes('Specialized waste collection operators')) {
      setOtherClientInputs(otherClientTypesFromForm);
    } else if (!hasOthers) {
      setOtherClientInputs(['']);
    }
  }, [selectedClientTypes, otherClientTypesFromForm]);

  const clientTypeOptions = [
    { label: 'Households', value: ClientType.HOUSEHOLDS },
    { label: 'Specialized waste collection operators', value: 'SPECIALIZED_WASTE_COLLECTION_OPERATORS' },
    { label: 'Specialized waste recovery operators', value: 'SPECIALIZED_WASTE_RECOVERY_OPERATORS' },
    { label: 'Markets', value: ClientType.MARKETS },
    { label: 'Schools', value: ClientType.SCHOOLS },
    { label: 'Health facilities', value: ClientType.HEALTH_FACILITIES },
    { label: 'Industries/Factories', value: ClientType.INDUSTRIES_FACTORIES },
    { label: 'Businesses/restaurants/(street) vendors', value: ClientType.BUSINESSES_RESTAURANTS_HOTELS_VENDORS },
    { label: 'Administrative buildings', value: ClientType.ADMINISTRATIVE_BUILDINGS },
    { label: 'Others (please specify)', value: ClientType.OTHERS },
  ];

  const addOtherClientInput = () => {
    setOtherClientInputs([...otherClientInputs, '']);
  };

  const removeOtherClientInput = (index: number) => {
    const newInputs = otherClientInputs.filter((_, i) => i !== index);
    setOtherClientInputs(newInputs);
  };

  const updateOtherClientInput = (index: number, value: string) => {
    const newInputs = [...otherClientInputs];
    newInputs[index] = value;
    setOtherClientInputs(newInputs);
  };

  return (
    <View style={tw`gap-4`}>
      {/* Client Types */}
      <View>
        <AppText style={tw`mb-2`}>Type of clients covered</AppText>
        <Controller
          control={control}
          name="marketCoverage.clientTypes"
          render={({ field: { onChange, value } }) => (
            <View style={tw`gap-2`}>
              {clientTypeOptions.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={tw`flex-row items-center gap-2`}
                  onPress={() => {
                    const currentValues = value || [];
                    const isSelected = currentValues.includes(option.value);
                    
                    if (isSelected) {
                      onChange(currentValues.filter((v: string) => v !== option.value));
                    } else {
                      onChange([...currentValues, option.value]);
                    }
                  }}
                >
                  <Checkbox
                    status={value?.includes(option.value) ? 'checked' : 'unchecked'}
                    onPress={() => {
                      const currentValues = value || [];
                      const isSelected = currentValues.includes(option.value);
                      
                      if (isSelected) {
                        onChange(currentValues.filter((v: string) => v !== option.value));
                      } else {
                        onChange([...currentValues, option.value]);
                      }
                    }}
                  />
                  <AppText style={tw`flex-1`}>{option.label}</AppText>
                </TouchableOpacity>
              ))}
            </View>
          )}
        />
        {errors.marketCoverage?.clientTypes && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.marketCoverage.clientTypes.message}
          </AppText>
        )}
      </View>

      {/* Other Client Types */}
      {selectedClientTypes.includes(ClientType.OTHERS) && (
        <View>
          <AppText style={tw`mb-2`}>Please specify other client types</AppText>
          <Controller
            control={control}
            name="marketCoverage.otherClientTypes"
            render={({ field: { onChange, value } }) => (
              <View style={tw`gap-2`}>
                {otherClientInputs.map((input, index) => (
                  <View key={index} style={tw`flex-row items-center gap-2`}>
                    <TextInput
                      mode="outlined"
                      value={input}
                      onChangeText={(text) => {
                        updateOtherClientInput(index, text);
                        const newValues = [...otherClientInputs];
                        newValues[index] = text;
                        onChange(newValues.filter(v => v.trim() !== ''));
                      }}
                      placeholder={`Other client type ${index + 1}`}
                      style={tw`flex-1`}
                      returnKeyType="next"
                      outlineColor="gray"
                    />
                    {otherClientInputs.length > 1 && (
                      <TouchableOpacity
                        onPress={() => removeOtherClientInput(index)}
                        style={tw`p-2`}
                      >
                        <Ionicons name="remove-circle" size={24} color="red" />
                      </TouchableOpacity>
                    )}
                  </View>
                ))}
                <TouchableOpacity
                  onPress={addOtherClientInput}
                  style={tw`flex-row items-center gap-2 p-2 border border-dashed border-gray-400 rounded-lg`}
                >
                  <Ionicons name="add-circle" size={24} color="blue" />
                  <AppText style={tw`text-blue-600`}>Add another client type</AppText>
                </TouchableOpacity>
              </View>
            )}
          />
          {errors.marketCoverage?.otherClientTypes && (
            <AppText style={tw`text-red-500 text-sm mt-1`}>
              {errors.marketCoverage.otherClientTypes.message}
            </AppText>
          )}
        </View>
      )}
    </View>
  );
};

export default MarketCoverageSection;
