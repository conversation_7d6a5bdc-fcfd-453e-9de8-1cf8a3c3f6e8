import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HttpClient, HttpParams } from '@angular/common/http';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { TagModule } from 'primeng/tag';
import { DialogModule } from 'primeng/dialog';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { CheckboxModule } from 'primeng/checkbox';
import { TooltipModule } from 'primeng/tooltip';
import { FileUploadModule } from 'primeng/fileupload';
import { RadioButtonModule } from 'primeng/radiobutton';
import { ToolbarModule } from 'primeng/toolbar';
import { MessageService, ConfirmationService } from 'primeng/api';
import { environment } from '../../../../environments/environment';
import { AuthService } from '../../../services/auth.service';
import { MultiSelectModule } from 'primeng/multiselect';
interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  telephoneNumber: string;
  role: {
    id: string;
    name: string;
  };
  status: 'Active' | 'Inactive' | 'Pending';
  locations?: LocationResponse[];
  provinceId?: string;   // Changed from number to string
  districtId?: string;   // Changed from number to string
  sectorId?: string;     // Changed from number to string
  cellId?: string;       // Changed from number to string
  villageId?: string;    // Changed from number to string
  roleId?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface UserRequest {
  firstName: string;
  lastName: string;
  email: string;
  telephoneNumber: string;
  roleId: string;
  locations: LocationRequest[];
}

interface LocationRequest {
  provinceId: string;  // Changed from number to string
  districtId: string;  // Changed from number to string
  sectorId: string;    // Changed from number to string
  cellId: string;      // Changed from number to string
  villageId: string;   // Changed from number to string
}

interface UserResponse {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  telephoneNumber: string;
  role: {
    id: string;
    name: string;
    privileges?: string[];
  };
  status?: 'Active' | 'Inactive' | 'Pending';
  accountVerified?: boolean;
  is2FAEnabled?: boolean;
  locations?: LocationResponse[];
  createdAt: string;
  updatedAt: string;
}

interface LocationResponse {
  id: string;
  provinceId: string;   // Changed from number to string
  districtId: string;   // Changed from number to string
  sectorId: string;     // Changed from number to string
  cellId: string;       // Changed from number to string
  villageId: string;    // Changed from number to string
  provinceName?: string;
  districtName?: string;
  sectorName?: string;
  cellName?: string;
  villageName?: string;
}

interface SelectOption {
  label: string;
  value: string | number;
}

interface UsersApiResponse {
  users: UserResponse[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

interface RolesApiResponse {
  roles: {
    id: string;
    name: string;
    code: string;
    privileges: string[];
    userCount: number;
    createdAt: string;
    updatedAt: string;
  }[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

@Component({
  selector: 'app-user-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TableModule,
    ButtonModule,
    InputTextModule,
    DropdownModule,
    TagModule,
    DialogModule,
    ToastModule,
    ConfirmDialogModule,
    IconFieldModule,
    InputIconModule,
    CheckboxModule,
    TooltipModule,
    FileUploadModule,
    RadioButtonModule,
    ToolbarModule,
    MultiSelectModule
  ],
  providers: [
    MessageService,
    ConfirmationService
  ],
  templateUrl: './all-users.component.html',
  styleUrls: ['./all-users.component.css']
})
export class AllUsersComponent implements OnInit {
  // Component properties
  users: User[] = [];
  selectedUsers: User[] = [];
  totalRecords = 0;
  loading = false;
  itemsPerPage = 10;
  rowsPerPageOptions = [
    { label: '5', value: 5 },
    { label: '10', value: 10 },
    { label: '25', value: 25 },
    { label: '50', value: 50 }
  ];
  searchValue = '';
  selectedFilter = '';
  filterOptions: SelectOption[] = [
    { label: 'All Users', value: '' },
    { label: 'Active', value: 'active' },
    { label: 'Inactive', value: 'inactive' }
  ];

  // Tab Navigation
  activeTab = 'all';

  // Dialog States
  userDialog = false;
  showImportDialog = false;
  isEditMode = false;
  saving = false;
  submitted = false;
  dialogTitle = 'Add User';

  // Current User for Editing
  user: User = this.createEmptyUser();

  // Dropdown Options
  statuses: SelectOption[] = [
    { label: 'Active', value: 'Active' },
    { label: 'Inactive', value: 'Inactive' },
    { label: 'Pending', value: 'Pending' }
  ];



  // Dynamic dropdown data from APIs
  roles: SelectOption[] = [];
  provinces: SelectOption[] = [];
  districts: SelectOption[] = [];
  sectors: SelectOption[] = [];
  cells: SelectOption[] = [];
  villages: SelectOption[] = [];

  // Loading states for dropdowns
  loadingRoles = false;
  loadingProvinces = false;
  loadingDistricts = false;
  loadingSectors = false;
  loadingCells = false;
  loadingVillages = false;

  constructor(
    private http: HttpClient,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    public authService: AuthService
  ) {}

  ngOnInit(): void {
    console.log('AllUsersComponent: Initializing component');
    console.log('AllUsersComponent: User authenticated:', this.authService.isAuthenticated());
    console.log('AllUsersComponent: Token exists:', !!this.authService.getToken());
    
    this.loadUsers();
    this.fetchRoles();
    this.fetchProvinces();
  }

  private createEmptyUser(): User {
    return {
      id: '',
      firstName: '',
      lastName: '',
      email: '',
      telephoneNumber: '',
      role: { id: '', name: '' },
      status: 'Active',
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  loadUsers(): void {
    this.loading = true;
    const apiUrl = `${environment.API_BASE_URL}/users`;
    
    console.log('AllUsersComponent: Loading users from:', apiUrl);
    console.log('AllUsersComponent: Token will be added by interceptor:', !!this.authService.getToken());
    
    this.http.get<UsersApiResponse>(apiUrl).subscribe({
      next: (response) => {
        console.log('AllUsersComponent: Raw API response:', response);
        
        if (response && response.users && Array.isArray(response.users)) {
          this.users = this.mapApiResponseToUsers(response.users);
          this.totalRecords = response.total || response.users.length;
          console.log('AllUsersComponent: Users mapped successfully:', this.users);
        } else {
          console.error('AllUsersComponent: Invalid response structure:', response);
          this.users = [];
          this.totalRecords = 0;
        }
        
        this.loading = false;
      },
      error: (error) => {
        console.error('AllUsersComponent: Error loading users:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: error.status === 401 ? 'Unauthorized - Please login again' : 'Failed to load users'
        });
        this.loading = false;
      }
    });
  }

  fetchRoles(): void {
    this.loadingRoles = true;
  
    const apiUrl = `${environment.API_BASE_URL}/roles`;
    const params = new HttpParams()
      .set('page', '1')
      .set('limit', '10');
  
    console.log('AllUsersComponent: Fetching roles from:', apiUrl);
  
    this.http.get<RolesApiResponse>(apiUrl, { params }).subscribe({
      next: (response) => {
        console.log('AllUsersComponent: Raw roles response:', response);
  
        if (response && Array.isArray(response.roles)) {
          this.roles = response.roles.map((role) => ({
            label: role.name,
            value: role.id
          }));
          this.updateFilterOptions(response.roles);
        } else {
          console.error('AllUsersComponent: Invalid roles response structure:', response);
          this.roles = [];
        }
  
        this.loadingRoles = false;
      },
      error: (error) => {
        console.error('Error fetching roles:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail:
            error.status === 401
              ? 'Unauthorized - Please login again'
              : 'Failed to load roles'
        });
        this.loadingRoles = false;
      }
    });
  }
  

  fetchProvinces(): void {
    this.loadingProvinces = true;
    const apiUrl = `${environment.API_BASE_URL}/provinces`;
    const params = new HttpParams().set('page', '1').set('limit', '100');
  
    this.http.get<any>(apiUrl, { params }).subscribe({
      next: (response) => {
        let provincesArray: any[] = Array.isArray(response)
          ? response
          : response?.provinces || response?.data || [];
  
        this.provinces = provincesArray.map((province: any) => ({
          label: province.name,
          value: province.id
        }));
        this.loadingProvinces = false;
      },
      error: (error) => {
        console.error('Error fetching provinces:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load provinces'
        });
        this.loadingProvinces = false;
      }
    });
  }
  
  fetchDistrictsByProvince(provinceId: string): void {
    this.loadingDistricts = true;
  
    const apiUrl = `${environment.API_BASE_URL}/districts`;
    const params = new HttpParams()
      .set('page', '1')
      .set('limit', '10')
      .set('provinceId', provinceId);
  
    this.http.get<any>(apiUrl, { params }).subscribe({
      next: (response) => {
        let districtsArray: any[] = Array.isArray(response)
          ? response
          : response?.districts || response?.data || [];
  
        this.districts = districtsArray.map((district: any) => ({
          label: district.name,
          value: district.id
        }));
        this.loadingDistricts = false;
      },
      error: (error) => {
        console.error('Error fetching districts:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load districts'
        });
        this.loadingDistricts = false;
      }
    });
  }
  fetchSectorsByDistrict(districtId: string): void {
    this.loadingSectors = true;
  
    const apiUrl = `${environment.API_BASE_URL}/sectors`;
    const params = new HttpParams()
      .set('page', '1')
      .set('limit', '10')
      .set('districtId', districtId);
  
    this.http.get<any>(apiUrl, { params }).subscribe({
      next: (response) => {
        let sectorsArray: any[] = Array.isArray(response)
          ? response
          : response?.sectors || response?.data || [];
  
        this.sectors = sectorsArray.map((sector: any) => ({
          label: sector.name,
          value: sector.id
        }));
        this.loadingSectors = false;
      },
      error: (error) => {
        console.error('Error fetching sectors:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load sectors'
        });
        this.loadingSectors = false;
      }
    });
  }
  fetchCellsBySector(sectorId: string): void {
    this.loadingCells = true;
  
    const apiUrl = `${environment.API_BASE_URL}/cells`;
    const params = new HttpParams()
      .set('page', '1')
      .set('limit', '10')
      .set('sectorId', sectorId);
  
    this.http.get<any>(apiUrl, { params }).subscribe({
      next: (response) => {
        let cellsArray: any[] = Array.isArray(response)
          ? response
          : response?.cells || response?.data || [];
  
        this.cells = cellsArray.map((cell: any) => ({
          label: cell.name,
          value: cell.id
        }));
        this.loadingCells = false;
      },
      error: (error) => {
        console.error('Error fetching cells:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load cells'
        });
        this.loadingCells = false;
      }
    });
  }
  fetchVillagesByCell(cellId: string): void {
    this.loadingVillages = true;
  
    const apiUrl = `${environment.API_BASE_URL}/villages`;
    const params = new HttpParams()
      .set('page', '1')
      .set('limit', '10')
      .set('cellId', cellId);
  
    this.http.get<any>(apiUrl, { params }).subscribe({
      next: (response) => {
        let villagesArray: any[] = Array.isArray(response)
          ? response
          : response?.villages || response?.data || [];
  
        this.villages = villagesArray.map((village: any) => ({
          label: village.name,
          value: village.id
        }));
        this.loadingVillages = false;
      },
      error: (error) => {
        console.error('Error fetching villages:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load villages'
        });
        this.loadingVillages = false;
      }
    });
  }
  
  private updateFilterOptions(roles: any[]): void {
    this.filterOptions = [
      { label: 'All Users', value: '' },
      { label: 'Active', value: 'active' },
      { label: 'Inactive', value: 'inactive' },
      ...roles.map(role => ({
        label: role.name,
        value: role.id
      }))
    ];
  }

  private mapApiResponseToUsers(apiUsers: UserResponse[]): User[] {
    return apiUsers.map(apiUser => ({
      id: apiUser.id,
      firstName: apiUser.firstName,
      lastName: apiUser.lastName,
      email: apiUser.email,
      telephoneNumber: apiUser.telephoneNumber,
      role: apiUser.role,
      status: apiUser.status || (apiUser.accountVerified ? 'Active' : 'Pending'),
      provinceId: apiUser.locations?.[0]?.provinceId,
      districtId: apiUser.locations?.[0]?.districtId,
      sectorId: apiUser.locations?.[0]?.sectorId,
      cellId: apiUser.locations?.[0]?.cellId,
      villageId: apiUser.locations?.[0]?.villageId,
      roleId: apiUser.role?.id,
      locations: apiUser.locations,
      createdAt: new Date(apiUser.createdAt),
      updatedAt: new Date(apiUser.updatedAt)
    }));
  }

  private mapUserToApiRequest(user: User): UserRequest {
    return {
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      telephoneNumber: user.telephoneNumber,
      roleId: user.roleId || user.role.id,
      locations: [{
        provinceId: user.provinceId || '0',    // Ensure string
        districtId: user.districtId || '0',    // Ensure string
        sectorId: user.sectorId || '0',        // Ensure string
        cellId: user.cellId || '0',            // Ensure string
        villageId: user.villageId || '0'       // Ensure string
      }]
    };
  }

  // Tab Management
  switchTab(tabType: string): void {
    this.activeTab = tabType;
    if (tabType === 'all') {
      this.loadUsers();
    } else if (tabType === 'inactive') {
      this.loadInactiveUsers();
    }
  }

  private loadInactiveUsers(): void {
    this.loading = true;
    const apiUrl = `${environment.API_BASE_URL}/users`;
    
    this.http.get<UsersApiResponse>(apiUrl).subscribe({
      next: (response) => {
        if (response && response.users && Array.isArray(response.users)) {
          const allUsers = this.mapApiResponseToUsers(response.users);
          this.users = allUsers.filter(user => user.status === 'Inactive');
          this.totalRecords = this.users.length;
        } else {
          this.users = [];
          this.totalRecords = 0;
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading inactive users:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load inactive users'
        });
        this.loading = false;
      }
    });
  }

  // Search and Filter
  onSearch(event: any): void {
    const value = event.target?.value || '';
    this.searchValue = value;
    console.log('Searching for:', value);
  }

  onFilterChange(): void {
    console.log('Filter changed to:', this.selectedFilter);
  }

  toggleFilters(): void {
    console.log('Toggle filters');
    // TODO: Implement advanced filters panel toggle
  }

  onPageSizeChange(): void {
    this.loadUsers();
  }

  // User Actions
  openNew(): void {
    this.isEditMode = false;
    this.dialogTitle = 'Add User';
    this.user = this.createEmptyUser();
    this.submitted = false;
    this.userDialog = true;
    this.resetLocationDropdowns();
  }

  editUser(user: User): void {
    this.isEditMode = true;
    this.dialogTitle = 'Edit User';
    this.user = { ...user };
    this.submitted = false;
    this.userDialog = true;
    this.loadLocationDataForEdit();
  }

  deleteUser(user: User): void {
    this.confirmationService.confirm({
      message: `Are you sure you want to delete ${user.firstName} ${user.lastName}?`,
      header: 'Confirm Delete',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        const apiUrl = `${environment.API_BASE_URL}/users/${user.id}`;
        this.http.delete(apiUrl).subscribe({
          next: () => {
            this.users = this.users.filter(u => u.id !== user.id);
            this.totalRecords = this.users.length;
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: 'User deleted successfully'
            });
          },
          error: (error) => {
            console.error('Error deleting user:', error);
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Failed to delete user'
            });
          }
        });
      }
    });
  }

  saveUser(): void {
    this.submitted = true;

    if (!this.user.firstName || !this.user.lastName || !this.user.email || !this.user.roleId) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please fill in all required fields'
      });
      return;
    }

    // Validate location selection
    if (!this.user.provinceId || !this.user.districtId || !this.user.sectorId || !this.user.cellId || !this.user.villageId) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please select complete location information'
      });
      return;
    }

    // Validate and format phone number
    if (!this.user.telephoneNumber.startsWith('+250')) {
      this.user.telephoneNumber = '+250' + this.user.telephoneNumber.replace(/^\+?250?/, '');
    }

    this.saving = true;
    const userRequest = this.mapUserToApiRequest(this.user);

    if (this.isEditMode) {
      // Update existing user
      const apiUrl = `${environment.API_BASE_URL}/users/${this.user.id}`;
      this.http.put<UserResponse>(apiUrl, userRequest).subscribe({
        next: (response) => {
          const updatedUser = this.mapApiResponseToUsers([response])[0];
          const index = this.users.findIndex(u => u.id === this.user.id);
          if (index !== -1) {
            this.users[index] = updatedUser;
          }
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'User updated successfully'
          });
          this.saving = false;
          this.hideDialog();
        },
        error: (error) => {
          console.error('Error updating user:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to update user'
          });
          this.saving = false;
        }
      });
    } else {
      // Create new user
      const apiUrl = `${environment.API_BASE_URL}/users`;
      this.http.post<UserResponse>(apiUrl, userRequest).subscribe({
        next: (response) => {
          const newUser = this.mapApiResponseToUsers([response])[0];
          this.users = [newUser, ...this.users];
          this.totalRecords = this.users.length;
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'User created successfully'
          });
          this.saving = false;
          this.hideDialog();
        },
        error: (error) => {
          console.error('Error creating user:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: error.error?.message || 'Failed to create user'
          });
          this.saving = false;
        }
      });
    }
  }

  hideDialog(): void {
    this.userDialog = false;
    this.user = this.createEmptyUser();
    this.submitted = false;
    this.resetLocationDropdowns();
  }

  deleteSelectedUsers(): void {
    if (this.selectedUsers.length === 0) return;

    this.confirmationService.confirm({
      message: `Are you sure you want to delete ${this.selectedUsers.length} selected users?`,
      header: 'Confirm Bulk Delete',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        const selectedIds = this.selectedUsers.map(user => user.id);
        this.processBulkDelete(selectedIds, 0);
      }
    });
  }

  private processBulkDelete(userIds: string[], index: number): void {
    if (index >= userIds.length) {
      this.loadUsers();
      this.selectedUsers = [];
      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: `${userIds.length} users deleted successfully`
      });
      return;
    }

    const apiUrl = `${environment.API_BASE_URL}/users/${userIds[index]}`;
    this.http.delete(apiUrl).subscribe({
      next: () => {
        this.processBulkDelete(userIds, index + 1);
      },
      error: (error) => {
        console.error(`Error deleting user ${userIds[index]}:`, error);
        this.processBulkDelete(userIds, index + 1);
      }
    });
  }

  // Location Management
  private resetLocationDropdowns(): void {
    this.districts = [];
    this.sectors = [];
    this.cells = [];
    this.villages = [];
    this.user.districtId = undefined;
    this.user.sectorId = undefined;
    this.user.cellId = undefined;
    this.user.villageId = undefined;
  }

  private loadLocationDataForEdit(): void {
    if (this.user?.provinceId) {
      this.onProvinceChange(false);
      if (this.user.districtId) {
        this.onDistrictChange(false);
        if (this.user.sectorId) {
          this.onSectorChange(false);
          if (this.user.cellId) {
            this.onCellChange(false);
          }
        }
      }
    }
  }

  onProvinceChange(resetUserSelection: boolean = true): void {
    if (resetUserSelection) {
      this.user.districtId = undefined;
      this.user.sectorId = undefined;
      this.user.cellId = undefined;
      this.user.villageId = undefined;
    }
    
    this.districts = [];
    this.sectors = [];
    this.cells = [];
    this.villages = [];
    
    if (!this.user?.provinceId) return;
    this.fetchDistrictsByProvince(this.user.provinceId);
  }

  onDistrictChange(resetUserSelection: boolean = true): void {
    if (resetUserSelection) {
      this.user.sectorId = undefined;
      this.user.cellId = undefined;
      this.user.villageId = undefined;
    }
    
    this.sectors = [];
    this.cells = [];
    this.villages = [];
    
    if (!this.user?.districtId) return;
    this.fetchSectorsByDistrict(this.user.districtId);
  }

  onSectorChange(resetUserSelection: boolean = true): void {
    if (resetUserSelection) {
      this.user.cellId = undefined;
      this.user.villageId = undefined;
    }
    
    this.cells = [];
    this.villages = [];
    
    if (!this.user?.sectorId) return;
    this.fetchCellsBySector(this.user.sectorId);
  }

  onCellChange(resetUserSelection: boolean = true): void {
    if (resetUserSelection) {
      this.user.villageId = undefined;
    }
    
    this.villages = [];
    
    if (!this.user?.cellId) return;
    this.fetchVillagesByCell(this.user.cellId);
  }

  // Import/Export functionality
  openImportDialog(): void {
    this.showImportDialog = true;
  }

  importUsers(event: any): void {
    const file = event.files[0];
    if (file) {
      this.messageService.add({
        severity: 'info',
        summary: 'Import Started',
        detail: `Processing ${file.name}...`
      });
      
      // TODO: Implement CSV parsing and bulk API calls
      setTimeout(() => {
        this.messageService.add({
          severity: 'success',
          summary: 'Import Complete',
          detail: 'Users imported successfully'
        });
        this.loadUsers();
        this.showImportDialog = false;
      }, 2000);
    }
  }

  exportCSV(): void {
    const csvContent = this.generateCSV();
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `users-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

    this.messageService.add({
      severity: 'success',
      summary: 'Export Complete',
      detail: 'Users exported to CSV successfully'
    });
  }

  private generateCSV(): string {
    const headers = [
      'First Name', 
      'Last Name', 
      'Email', 
      'Phone', 
      'Role', 
      'Status', 
      'Location',
      'Created At'
    ];
    const csvRows = [headers.join(',')];
    
    this.users.forEach(user => {
      const row = [
        user.firstName,
        user.lastName,
        user.email,
        user.telephoneNumber,
        user.role?.name || '',
        user.status,
        this.getLocationDisplay(user),
        user.createdAt.toISOString().split('T')[0]
      ];
      csvRows.push(row.map(field => `"${field}"`).join(','));
    });
    
    return csvRows.join('\n');
  }

  // Utility Methods
  getLocationDisplay(user: User): string {
    if (user.locations && user.locations.length > 0) {
      const location = user.locations[0];
      const parts = [];
      
      if (location.villageName) parts.push(location.villageName);
      if (location.cellName) parts.push(location.cellName);
      if (location.sectorName) parts.push(location.sectorName);
      if (location.districtName) parts.push(location.districtName);
      if (location.provinceName) parts.push(location.provinceName);
      
      return parts.join(', ') || 'Location not specified';
    }
    
    return 'Location not specified';
  }

  getStatusSeverity(status: string): "success" | "secondary" | "info" | "warning" | "danger" | "contrast" | undefined {
    switch (status) {
      case 'Active':
        return 'success';
      case 'Inactive':
        return 'danger';
      case 'Pending':
        return 'warning';
      default:
        return 'info';
    }
  }

  isFormValid(): boolean {
    return !!(
      this.user.firstName &&
      this.user.lastName &&
      this.user.email &&
      this.user.telephoneNumber &&
      this.user.roleId &&
      this.user.provinceId &&
      this.user.districtId &&
      this.user.sectorId &&
      this.user.cellId &&
      this.user.villageId
    );
  }

  // String utility for row numbers
  String = String;
}