import CreateHealthFacilityForm from "@/components/CreateHealthFacilityForm";
import CreateHouseholdForm from "@/components/CreateHouseHoldForm";
import CreatePublicPlaceForm from "@/components/CreatePublicPlaceForm";
import CreateSchoolForm from "@/components/CreateSchoolForm";
import AppText from "@/components/ui/Text";
import { PRIMARY_COLOR } from "@/constants/colors";
import { createHealthFacility } from "@/services/facility/health";
import { createHousehold } from "@/services/facility/household";
import { createPublicPlace } from "@/services/facility/public-place";
import { createSchool } from "@/services/facility/school";
import { router, useLocalSearchParams } from "expo-router";
import React, { useState } from "react";
import { Alert, View } from "react-native";
import tw from "twrnc";

export default function NewFacilityScreen() {

    const { type } = useLocalSearchParams();
    const displayType = Array.isArray(type) ? type[0] : type;
    const _type = displayType.toLocaleLowerCase().split(" ").join("");
    const [loading, setLoading] = useState(false);

    const submitHousehold = async (data: any) => {
        setLoading(true);
        try {
            let settlementType = data.settlementType;
            if (typeof settlementType === "number") {
                settlementType = settlementType === 0 ? "RURAL" : "URBAN";
            }
            const res = await createHousehold({
                headOfHouseholdName: data.headOfHouseholdName,
                locationData: {
                    villageId: Number(data.villageId),
                    latitude: Number(data.latitude),
                    longitude: Number(data.longitude),
                    settlementType,
                },
            });

            Alert.alert(
                "Success",
                `Household (Number: ${res.data.facility.number}) created successfully`,
                [
                  {
                    text: "OK",
                    onPress: () => {
                      router.push({
                        pathname: "/facility/forms",
                        params: { type: displayType, facilityId: res.data.facility.id },
                      });
                    },
                  },
                ]
            );

        } catch (error: any) {
            console.log(error.response.data.message);
            Alert.alert("Something went wrong", "Failed to create household");
        } finally {
            setLoading(false);
        }
    };

    const submitPublicPlace = async (data: any) => {
        setLoading(true);
        try {
            let settlementType = data.settlementType;
            if (typeof settlementType === "number") {
                settlementType = settlementType === 0 ? "RURAL" : "URBAN";
            }
            const res = await createPublicPlace({
                type: data.type,
                name: data.name,
                locationData: {
                    villageId: Number(data.villageId),
                    latitude: Number(data.latitude),
                    longitude: Number(data.longitude),
                    settlementType,
                },
            });

            Alert.alert(
                "Success",
                `Public place created successfully (Number: ${res.data.facility.number})`,
                [
                  {
                    text: "OK",
                    onPress: () => {
                      router.push({
                        pathname: "/facility/forms",
                        params: { type: displayType, facilityId: res.data.facility.id },
                      });
                    },
                  },
                ]
            );

        } catch (error: any) {
            console.log(error.response.data.message);
            Alert.alert("Error", "Failed to create public place");
        } finally {
            setLoading(false);
        }
    };

    const submitSchool = async (data: any) => {
        setLoading(true);
        try {
            let settlementType = data.settlementType;
            if (typeof settlementType === "number") {
                settlementType = settlementType === 0 ? "RURAL" : "URBAN";
            }
            const res = await createSchool({
                name: data.name,
                locationData: {
                    villageId: Number(data.villageId),
                    latitude: Number(data.latitude),
                    longitude: Number(data.longitude),
                    settlementType,
                },
            });
            Alert.alert(
                "Success",
                `School created successfully (Number: ${res.data.facility.number})`,
                [
                  {
                    text: "OK",
                    onPress: () => {
                      router.push({
                        pathname: "/facility/forms",
                        params: { type: displayType, facilityId: res.data.facility.id },
                      });
                    },
                  },
                ]
            );
        } catch (error) {
            console.log(error);
            Alert.alert("Error", "Failed to create school");
        } finally {
            setLoading(false);
        }
    };

    const submitHealthFacility = async (data: any) => {
        setLoading(true);
        try {
            let settlementType = data.settlementType;
            if (typeof settlementType === "number") {
                settlementType = settlementType === 0 ? "RURAL" : "URBAN";
            }

            const res = await createHealthFacility({
                name: data.name,
                locationData: {
                    villageId: Number(data.villageId),
                    latitude: Number(data.latitude),
                    longitude: Number(data.longitude),
                    settlementType
                },
            });
            Alert.alert(
                "Success",
                `Health facility created successfully (Number: ${res.data.facility.number})`,
                [
                  {
                    text: "OK",
                    onPress: () => {
                      router.push({
                        pathname: "/facility/forms",
                        params: { type: displayType, facilityId: res.data.facility.id },
                      });
                    },
                  },
                ]
            );
        } catch (error: any) {
            console.log(error.response.data.message);
            Alert.alert("Something went wrong", "Failed to create health facility");
        } finally {
            setLoading(false);
        }
    };

    return (
        <View style={tw`flex-1 bg-white pt-2 px-6`}>
            <View>
                <AppText
                    weight="bold"
                    style={tw`text-2xl mb-2 text-[${PRIMARY_COLOR}]`}
                >
                    Create New {displayType}
                </AppText>
                <AppText weight="medium" style={tw`text-gray-600`}>
                    Fill in the details for the new {displayType}
                </AppText>
            </View>

            {_type === "household" ? (
                <CreateHouseholdForm onSubmit={submitHousehold} />
            ) : _type === "publicplace" ? (
                <CreatePublicPlaceForm onSubmit={submitPublicPlace} />
            ) : _type === "school" ? (
                <CreateSchoolForm onSubmit={submitSchool} />
            ) : _type === "healthfacility" ? (
                <CreateHealthFacilityForm onSubmit={submitHealthFacility} />
            ) : null}

        </View>
    )
}