import React, { useState } from 'react';
import { View } from 'react-native';
import { Controller, useFormContext } from 'react-hook-form';
import tw from 'twrnc';
import AppText from '@/components/ui/Text';
import { TextInput, Button } from 'react-native-paper';
import LocationSelectionModal from './LocationSelectionModal';

interface BasicInfoSectionProps {
  control: any;
  errors: any;
  watch: any;
}

const BasicInfoSection = ({ control, errors, watch }: BasicInfoSectionProps) => {
  const [locationModalVisible, setLocationModalVisible] = useState(false);
  const { setValue } = useFormContext();
  const facilityLocationDisplay = watch('basicInfo.facilityLocationDisplay');

  const handleLocationSelect = (location: {
    villageId: number;
    villageName: string;
    fullAddress: string;
    latitude?: number;
    longitude?: number;
  }) => {
    // Update all location-related fields
    setValue('basicInfo.facilityVillageId', location.villageId);
    setValue('basicInfo.facilityLocationDisplay', location.fullAddress);
    setValue('basicInfo.facilityLatitude', location.latitude);
    setValue('basicInfo.facilityLongitude', location.longitude);
    setLocationModalVisible(false);
  };

  return (
    <View style={tw`gap-4`}>
      {/* Company Name */}
      <View>
        <AppText style={tw`mb-2`}>Name of the specialized waste disposal company providing services in the district *</AppText>
        <Controller
          control={control}
          name="basicInfo.companyName"
          render={({ field: { onChange, value } }) => (
            <TextInput
              mode="outlined"
              value={value || ''}
              onChangeText={onChange}
              placeholder="Enter company name"
              error={!!errors.basicInfo?.companyName}
              returnKeyType="next"
              outlineColor="gray"
            />
          )}
        />
        {errors.basicInfo?.companyName && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.basicInfo.companyName.message}
          </AppText>
        )}
      </View>

      {/* Facility Location */}
      <View>
        <AppText style={tw`mb-2`}>Please specify the address and location of the disposal facility *</AppText>
        <Controller
          control={control}
          name="basicInfo.facilityVillageId"
          render={({ field: { onChange, value } }) => (
            <View>
              {facilityLocationDisplay ? (
                <View style={tw`border border-gray-300 rounded-lg p-3 bg-gray-50`}>
                  <AppText>{facilityLocationDisplay}</AppText>
                  {(watch('basicInfo.facilityLatitude') || watch('basicInfo.facilityLongitude')) && (
                    <AppText style={tw`text-gray-500 text-sm mt-1`}>
                      Coordinates: {watch('basicInfo.facilityLatitude')}, {watch('basicInfo.facilityLongitude')}
                    </AppText>
                  )}
                  <Button
                    mode="outlined"
                    onPress={() => setLocationModalVisible(true)}
                    style={tw`mt-2`}
                    icon="map-marker"
                  >
                    Change Location
                  </Button>
                </View>
              ) : (
                <Button
                  mode="outlined"
                  onPress={() => setLocationModalVisible(true)}
                  icon="map-marker"
                >
                  Select Facility Location
                </Button>
              )}

              <LocationSelectionModal
                visible={locationModalVisible}
                onClose={() => setLocationModalVisible(false)}
                onSelect={handleLocationSelect}
                initialLocation={{
                  villageId: value,
                  latitude: watch('basicInfo.facilityLatitude'),
                  longitude: watch('basicInfo.facilityLongitude'),
                }}
              />
            </View>
          )}
        />
        {errors.basicInfo?.facilityVillageId && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.basicInfo.facilityVillageId.message}
          </AppText>
        )}
      </View>
    </View>
  );
};

export default BasicInfoSection;
