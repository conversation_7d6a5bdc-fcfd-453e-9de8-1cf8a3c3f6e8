import React from 'react';
import { View } from 'react-native';
import { Controller } from 'react-hook-form';
import tw from 'twrnc';
import AppText from '@/components/ui/Text';
import { TextInput } from 'react-native-paper';

interface BasicInfoSectionProps {
  control: any;
  errors: any;
}

const BasicInfoSection = ({ control, errors }: BasicInfoSectionProps) => {
  return (
    <View style={tw`gap-4`}>
      {/* Company Name */}
      <View>
        <AppText style={tw`mb-2`}>Name of the specialized waste recovery company providing services in the district *</AppText>
        <Controller
          control={control}
          name="basicInfo.companyName"
          render={({ field: { onChange, value } }) => (
            <TextInput
              mode="outlined"
              value={value || ''}
              onChangeText={onChange}
              placeholder="Enter company name"
              error={!!errors.basicInfo?.companyName}
              outlineColor="gray"
            />
          )}
        />
        {errors.basicInfo?.companyName && (
          <AppText style={tw`text-red-500 text-sm mt-1`}>
            {errors.basicInfo.companyName.message}
          </AppText>
        )}
      </View>
    </View>
  );
};

export default BasicInfoSection;
