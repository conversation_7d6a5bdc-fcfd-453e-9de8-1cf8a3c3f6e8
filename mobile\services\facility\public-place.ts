import { CreatePublicPlaceInput, CreatePublicPlaceResponse, PublicPlacesListResponse, PaginationQuery, PublicPlace } from '@/types/facility';
import axios from '../../utils/axios';

export const getAllPublicPlaces = (query?: PaginationQuery) => {
    return axios.get<PublicPlacesListResponse>(`/facilities/public-places`, { params: query });
};

export const createPublicPlace = (data: CreatePublicPlaceInput) => {
    return axios.post<CreatePublicPlaceResponse>(`/facilities/public-places`, data);
};

export const getPublicPlace = (id: string) => {
    return axios.get<PublicPlace>(`/facilities/public-places/${id}`);
}

export const submitPublicPlaceForm = (facilityId: string, data: any) => {
    return axios.post(`/submission/public-place`, {
        facilityId,
        facilityType: "PUBLIC_PLACE",
        ...data
    });
};