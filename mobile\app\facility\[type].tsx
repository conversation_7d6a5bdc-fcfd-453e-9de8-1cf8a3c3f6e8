import FacilityList from "@/components/FacilityList";
import AppButton from "@/components/ui/Button";
import AppText from "@/components/ui/Text";
import { PRIMARY_COLOR } from "@/constants/colors";
import { getAllHealtFacilities } from "@/services/facility/health";
import { getAllHouseHolds } from "@/services/facility/household";
import { getAllPublicPlaces } from "@/services/facility/public-place";
import { getAllSchools } from "@/services/facility/school";
import {
    HealthFacilitiesListResponse,
    HealthFacility,
    Household,
    HouseholdsListResponse,
    PublicPlace,
    PublicPlacesListResponse,
    School,
    SchoolsListResponse,
} from "@/types/facility";
import { Ionicons } from "@expo/vector-icons";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import {
    ActivityIndicator,
    Alert,
    View
} from "react-native";
import { Button, TextInput } from "react-native-paper";
import tw from "twrnc";

function capitalize(str: string) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}

export default function FacilityScreen() {
    const { type, villageId } = useLocalSearchParams();
    const displayType = Array.isArray(type) ? type[0] : type;
    const [search, setSearch] = useState("");
    const [data, setData] = useState<
        Household[] | PublicPlace[] | School[] | HealthFacility[]
    >([]);
    const [loading, setLoading] = useState(false);
    const _type = displayType.toLocaleLowerCase().split(" ").join("");
    const [selectedId, setSelectedId] = useState<string | undefined>(undefined);

    useEffect(() => {
        getData();
    }, []);

    const getData = async () => {
        setLoading(true);
        setSelectedId(undefined);
        try {
            let res:
                | HouseholdsListResponse
                | PublicPlacesListResponse
                | SchoolsListResponse
                | HealthFacilitiesListResponse
                | undefined;

            switch (_type) {
                case "household":
                    res = (await getAllHouseHolds({villageId: Number(villageId)})).data;
                    break;
                case "school":
                    res = (await getAllSchools({villageId: Number(villageId)})).data;
                    break;
                case "healthfacility":
                    res = (await getAllHealtFacilities({villageId: Number(villageId)})).data;
                    break;
                case "publicplace":
                    res = (await getAllPublicPlaces({villageId: Number(villageId)})).data;
                    break;
                default:
                    res = undefined;
                    break;
            }

            if (res) {
                setData(res.data);
            } 
        } catch (error: any) {
            Alert.alert("Error", "An error occurred while fetching data");
            console.log(error.response.data.message);
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = async () => {
        setSelectedId(undefined);
        if (!search.trim()) {
            getData();
            return;
        }
        if (search.trim().length < 3) {
            Alert.alert(
                "Invalid search",
                "Please enter at least 3 characters to search"
            );
            return;
        }


        setLoading(true);
        try {
            let res:
                | HouseholdsListResponse
                | PublicPlacesListResponse
                | SchoolsListResponse
                | HealthFacilitiesListResponse
                | undefined;

            switch (_type) {
                case "household":
                    res = (await getAllHouseHolds({ villageId: Number(villageId), search })).data;
                    break;
                case "school":
                    res = (await getAllSchools({ villageId: Number(villageId), search })).data;
                    break;
                case "healthfacility":
                    res = (await getAllHealtFacilities({ villageId: Number(villageId), search })).data;
                    break;
                case "publicplace":
                    res = (await getAllPublicPlaces({ villageId: Number(villageId), search })).data;
                    break;
                default:
                    res = undefined;
                    break;
            }

            if (res) {
                setData(res.data);
            } else {
                Alert.alert(
                    "No data found",
                    `No ${displayType} data found for the search query`
                );
                setData([]);
            }
        } catch (error) {
            Alert.alert("Error", "An error occurred while fetching data");
            console.log(error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <View style={tw`flex-1 bg-white pt-2 px-6`}>
            <View>
                <AppText
                    weight="bold"
                    style={tw`text-2xl mb-2 text-[${PRIMARY_COLOR}]`}
                >
                    Select {displayType ? capitalize(displayType) : ""}
                </AppText>
                <AppText weight="medium" style={tw`text-gray-600`}>
                    Choose the {displayType} you are going to collect the data for
                </AppText>
            </View>

            <View style={tw`mt-8 flex-row items-center`}>
                <View style={tw`flex-1`}>
                    <TextInput
                        placeholder={`Search ${displayType}`}
                        mode="outlined"
                        left={<TextInput.Icon color={"#9CA3AF"} icon="magnify" />}
                        outlineColor="#E5E7EB"
                        outlineStyle={tw`rounded-3xl`}
                        style={tw`bg-[#F8F9FE]`}
                        value={search}
                        onChangeText={setSearch}
                        onSubmitEditing={handleSearch}
                        editable={!loading}
                    />
                </View>

                <View style={tw`ml-4`}>
                    <Button
                        mode="contained"
                        onPress={handleSearch}
                        contentStyle={tw`rounded-2xl py-2`}
                    >
                        {loading ? (
                            <ActivityIndicator color="#fff" />
                        ) : (
                            <Ionicons name="arrow-forward" size={24} color="#fff" />)}
                    </Button>

                </View>
            </View>
            <View style={tw`flex-1`}>
                {_type === "healthfacility" && (
                    <FacilityList
                        data={data as HealthFacility[]}
                        loading={loading}
                        _type="healthfacility"
                        selectedId={selectedId}
                        onSelect={setSelectedId}
                    />
                )}
                {_type === "household" && (
                    <FacilityList
                        data={data as Household[]}
                        loading={loading}
                        _type="household"
                        selectedId={selectedId}
                        onSelect={setSelectedId}
                    />
                )}
                {_type === "publicplace" && (
                    <FacilityList
                        data={data as PublicPlace[]}
                        loading={loading}
                        _type="publicplace"
                        selectedId={selectedId}
                        onSelect={setSelectedId}
                    />
                )}
                {_type === "school" && (
                    <FacilityList
                        data={data as School[]}
                        loading={loading}
                        _type="school"
                        selectedId={selectedId}
                        onSelect={setSelectedId}
                    />
                )}
                <View>
                    <AppButton disabled={!selectedId} title="Start survey" onPress={() => {
                        router.push({
                            pathname: "/facility/forms",
                            params: { type: displayType, facilityId: selectedId },
                        });
                    }} />
                    <View style={tw`mt-4 flex-row justify-between gap-4 items-center`}>
                        <AppText weight="medium" style={tw`text-gray-600`}>
                            {displayType} not found?
                        </AppText>
                        <Button
                            mode="outlined"
                            onPress={() => {
                                router.push({
                                    pathname: "/facility/new",
                                    params: { type: displayType, villageId },
                                });
                            }}
                            contentStyle={tw`rounded-xl py-1`}
                            style={tw`border-2 border-[${PRIMARY_COLOR}]`}
                            icon={() => (
                                <Ionicons name="add" size={24} color={`${PRIMARY_COLOR}`} />
                            )}
                        >
                            <AppText weight="semibold" style={tw`text-[${PRIMARY_COLOR}]`}>
                                Add new
                            </AppText>
                        </Button>
                    </View>
                </View>
            </View>
        </View>
    );
}