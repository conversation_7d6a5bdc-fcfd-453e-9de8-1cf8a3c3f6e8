"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubmissionController = void 0;
const common_1 = require("@nestjs/common");
const submission_service_1 = require("./submission.service");
const household_dto_1 = require("./dto/household.dto");
const school_dto_1 = require("./dto/school.dto");
const health_facility_dto_1 = require("./dto/health-facility.dto");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
const swagger_1 = require("@nestjs/swagger");
const auth_guard_1 = require("../../common/guards/auth.guard");
const mobile_guard_1 = require("../../common/guards/mobile.guard");
const privilege_guard_1 = require("../../common/guards/privilege.guard");
const public_place_dto_1 = require("./dto/public-place.dto");
const waste_collection_company_dto_1 = require("./dto/waste-collection-company.dto");
const waste_recovery_company_dto_1 = require("./dto/waste-recovery-company.dto");
const waste_disposal_company_dto_1 = require("./dto/waste-disposal-company.dto");
const swagger_2 = require("@nestjs/swagger");
const privileges_decorator_1 = require("../../common/decorators/privileges.decorator");
const client_1 = require("@prisma/client");
let SubmissionController = class SubmissionController {
    submissionService;
    constructor(submissionService) {
        this.submissionService = submissionService;
    }
    createHousehold(dto, user) {
        return this.submissionService.createHouseholdSubmission(dto, user.id);
    }
    createSchool(dto, user) {
        return this.submissionService.createSchoolSubmission(dto, user.id);
    }
    createHealthFacility(dto, user) {
        return this.submissionService.createHealthFacilitySubmission(dto, user.id);
    }
    createPublicPlace(dto, user) {
        return this.submissionService.createPublicPlaceSubmission(dto, user.id);
    }
    createWasteCollectionCompany(dto, user) {
        return this.submissionService.createWasteCollectionCompanySubmission(dto, user.id);
    }
    createWasteRecoveryCompany(dto, user) {
        return this.submissionService.createWasteRecoveryCompanySubmission(dto, user.id);
    }
    createWasteDisposalCompany(dto, user) {
        return this.submissionService.createWasteDisposalCompanySubmission(dto, user.id);
    }
};
exports.SubmissionController = SubmissionController;
__decorate([
    (0, common_1.Post)('household'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [household_dto_1.CreateHouseholdSubmissionDto, Object]),
    __metadata("design:returntype", void 0)
], SubmissionController.prototype, "createHousehold", null);
__decorate([
    (0, common_1.Post)('school'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [school_dto_1.CreateSchoolSubmissionDto, Object]),
    __metadata("design:returntype", void 0)
], SubmissionController.prototype, "createSchool", null);
__decorate([
    (0, common_1.Post)('health-facility'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [health_facility_dto_1.CreateHealthFacilitySubmissionDto, Object]),
    __metadata("design:returntype", void 0)
], SubmissionController.prototype, "createHealthFacility", null);
__decorate([
    (0, common_1.Post)('public-place'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [public_place_dto_1.CreatePublicPlaceSubmissionDto, Object]),
    __metadata("design:returntype", void 0)
], SubmissionController.prototype, "createPublicPlace", null);
__decorate([
    (0, common_1.Post)('waste-collection-company'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, swagger_2.ApiOperation)({ summary: 'Create waste collection company submission' }),
    (0, swagger_2.ApiResponse)({
        status: 201,
        description: 'Waste collection company submission created successfully',
        type: waste_collection_company_dto_1.WasteCollectionCompanySubmissionResponseDto,
    }),
    (0, swagger_2.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_2.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_2.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [waste_collection_company_dto_1.CreateWasteCollectionCompanySubmissionDto, Object]),
    __metadata("design:returntype", void 0)
], SubmissionController.prototype, "createWasteCollectionCompany", null);
__decorate([
    (0, common_1.Post)('waste-recovery-company'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, swagger_2.ApiOperation)({ summary: 'Create waste recovery company submission' }),
    (0, swagger_2.ApiResponse)({
        status: 201,
        description: 'Waste recovery company submission created successfully',
        type: waste_recovery_company_dto_1.WasteRecoveryCompanySubmissionResponseDto,
    }),
    (0, swagger_2.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_2.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_2.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [waste_recovery_company_dto_1.CreateWasteRecoveryCompanySubmissionDto, Object]),
    __metadata("design:returntype", void 0)
], SubmissionController.prototype, "createWasteRecoveryCompany", null);
__decorate([
    (0, common_1.Post)('waste-disposal-company'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, swagger_2.ApiOperation)({ summary: 'Create waste disposal company submission' }),
    (0, swagger_2.ApiResponse)({
        status: 201,
        description: 'Waste disposal company submission created successfully',
        type: waste_disposal_company_dto_1.WasteDisposalCompanySubmissionResponseDto,
    }),
    (0, swagger_2.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_2.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_2.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [waste_disposal_company_dto_1.CreateWasteDisposalCompanySubmissionDto, Object]),
    __metadata("design:returntype", void 0)
], SubmissionController.prototype, "createWasteDisposalCompany", null);
exports.SubmissionController = SubmissionController = __decorate([
    (0, swagger_1.ApiTags)('Submission'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, mobile_guard_1.MobileGuard, privilege_guard_1.PrivilegeGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('submission'),
    __metadata("design:paramtypes", [submission_service_1.SubmissionService])
], SubmissionController);
//# sourceMappingURL=submission.controller.js.map