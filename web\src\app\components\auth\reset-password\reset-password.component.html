<div class="min-h-screen flex">
  <!-- Left Panel -->
  <div class="w-1/2 bg-blue-500 flex items-center justify-center">
    <div class="text-white text-center px-12">
      <h1 class="text-5xl font-bold mb-6">WASH MIS</h1>
      <p class="text-lg leading-relaxed">
        WASH MIS is a comprehensive system that monitors and manages water supply, sanitation, and hygiene services and makes interventions all over the country.
      </p>
    </div>
  </div>
  <!-- Right Panel -->
  <div class="w-1/2 bg-gray-50 flex items-center justify-center">
    <div class="bg-white rounded-2xl border border-gray-200 shadow-sm p-12 w-full max-w-xl min-h-[450px] flex flex-col justify-center">
      <div class="flex flex-col">
        <svg class="w-10 h-10 mb-6 text-gray-900" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M12 11c0-1.104.896-2 2-2s2 .896 2 2-.896 2-2 2-2-.896-2-2zm0 0V7m0 4v4m0 0c0 1.104-.896 2-2 2s-2-.896-2-2 .896-2 2-2 2 .896 2 2z"/></svg>
        <h2 class="text-2xl font-bold text-gray-900 mb-1">Password Reset</h2>
        <p class="text-gray-500 mb-8">Fill in the verification code sent to your email</p>
      </div>
      <form class="space-y-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Verification Code</label>
          <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Enter code">
        </div>
        <div class="flex space-x-2">
          <button type="submit" class="w-full py-2.5 px-4 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md focus:ring-2 focus:ring-blue-500">Continue</button>
          <button type="button" class="w-full py-2.5 px-4 text-sm font-medium text-blue-600 bg-blue-100 hover:bg-blue-200 rounded-md focus:ring-2 focus:ring-blue-500">Resend</button>
        </div>
      </form>
      <div class="flex items-center mt-8">
        <a routerLink="/login" class="flex items-center text-base font-medium text-black hover:underline">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7"/></svg>
          Go to login
        </a>
      </div>
    </div>
  </div>
</div>
