<div class="min-h-screen flex">
  <!-- Left Panel -->
  <div class="w-1/2 bg-blue-500 flex items-center justify-center">
    <div class="text-white text-center px-12">
      <h1 class="text-5xl font-bold mb-6">WASH MIS</h1>
      <p class="text-lg leading-relaxed">
        WASH MIS is a comprehensive system that monitors and manages water supply, sanitation, and hygiene services and makes interventions all over the country.
      </p>
    </div>
  </div>
  <!-- Right Panel -->
  <div class="w-1/2 bg-gray-50 flex items-center justify-center">
    <div class="bg-white rounded-2xl border border-gray-200 shadow-sm p-12 w-full max-w-xl min-h-[450px] flex flex-col justify-center">
      <div class="flex flex-col">
        <svg class="w-10 h-10 mb-6 text-gray-900" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><rect x="7" y="11" width="10" height="8" rx="2"/><path d="M12 15v2"/><path d="M9 11V7a3 3 0 1 1 6 0v4"/></svg>
        <h2 class="text-2xl font-bold text-gray-900 mb-1">Set Password</h2>
        <p class="text-gray-500 mb-8">You can now set your password</p>
      </div>
      <form [formGroup]="setForm" (ngSubmit)="onSubmit()" class="space-y-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
          <input
            type="password"
            formControlName="password"
            class="w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Password"
          />
          <div *ngIf="password?.touched && password?.errors?.['strongPassword']" class="text-red-500 text-xs">
            Password must be at least 8 characters, include uppercase, lowercase, number, and special character.
          </div>
          <div *ngIf="password?.touched && password?.errors?.['required']" class="text-red-500 text-xs">
            Password is required.
          </div>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
          <input
            type="password"
            formControlName="confirmPassword"
            class="w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Confirm Password"
          />
          <div *ngIf="confirmPassword?.touched && setForm?.errors?.['passwordMismatch']" class="text-red-500 text-xs">
            Passwords do not match.
          </div>
          <div *ngIf="confirmPassword?.touched && confirmPassword?.errors?.['required']" class="text-red-500 text-xs">
            Confirm password is required.
          </div>
        </div>
        <button
          type="submit"
          [disabled]="setForm.invalid || loading"
          class="w-full py-2.5 px-4 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md focus:ring-2 focus:ring-blue-500"
        >
          {{ loading ? 'Setting...' : 'Set Password' }}
        </button>
        <div *ngIf="message" class="mt-4 text-green-600 text-center">{{ message }}</div>
        <div *ngIf="error" class="mt-4 text-red-600 text-center">{{ error }}</div>
      </form>
      <div class="flex items-center mt-8">
        <a routerLink="/login" class="flex items-center text-base font-medium text-black hover:underline">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7"/></svg>
          Go to login
        </a>
      </div>
    </div>
  </div>
</div>
