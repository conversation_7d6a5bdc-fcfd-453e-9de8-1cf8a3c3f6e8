import AppButton from '@/components/ui/Button';
import AppText from '@/components/ui/Text';
import { useAuth } from '@/hooks/useAuth';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from 'expo-router';
import React, { useState } from "react";
import { Pressable, TouchableOpacity, View } from "react-native";
import { TextInput } from 'react-native-paper';
import tw from 'twrnc';

export default function ProfileScreen() {
  const { user } = useAuth();
  const navigation = useNavigation();
  const [editMode, setEditMode] = useState(false);
  const [firstName, setFirstName] = useState(user?.firstName || '');
  const [lastName, setLastName] = useState(user?.lastName || '');

  return (
    <View style={tw`flex-1 bg-white`}>  
      {/* Header */}
      <View style={tw`bg-white`}> 
        <View style={tw`flex-row items-center p-4 pt-8 gap-2`}> 
          <Pressable onPress={() => navigation.goBack()} hitSlop={10}>
            <Ionicons name="chevron-back" size={24} color="gray" />
          </Pressable>
          <AppText weight="semibold" style={tw`text-gray-500 text-lg`}>Profile</AppText>
        </View>
      </View>
      <View style={tw`px-5 flex flex-col gap-8 mt-4`}>  
        <View style={tw`flex flex-col gap-12`}>
          <View style={tw`flex gap-2 flex-row items-center justify-between`}> 
            <View>
              <AppText weight='bold' style={tw`text-2xl text-gray-600`}>Profile</AppText>
              <AppText style={tw``}>Manage your profile</AppText>
            </View>
            <TouchableOpacity onPress={() => setEditMode(e => !e)} style={tw`px-3 py-1 bg-blue-100 rounded-lg`}>
              <AppText weight="semibold" style={tw`text-blue-600`}>{editMode ? 'Cancel' : 'Edit'}</AppText>
            </TouchableOpacity>
          </View>
          <View style={tw`flex flex-col gap-4 bg-gray-100 rounded-xl p-5 mt-4`}> 
            <View style={tw`flex-row items-center gap-3 mb-4`}>
              <Ionicons name="person-circle" size={48} color={tw.color('gray-400')} />
            </View>
            {editMode ? (
              <>
                <AppText weight='medium' style={tw`text-gray-600 mb-2`}>First Name</AppText>
                <TextInput
                  placeholder="First Name"
                  value={firstName}
                  onChangeText={setFirstName}
                  mode='outlined'
                  style={tw`bg-white mb-2`}
                  outlineStyle={tw`rounded-xl`}
                  outlineColor='#E5E7EB'
                />
                <AppText weight='medium' style={tw`text-gray-600 mb-2`}>Last Name</AppText>
                <TextInput
                  placeholder="Last Name"
                  value={lastName}
                  onChangeText={setLastName}
                  mode='outlined'
                  style={tw`bg-white mb-2`}
                  outlineStyle={tw`rounded-xl`}
                  outlineColor='#E5E7EB'
                />
                <AppText weight='medium' style={tw`text-gray-600 mb-2`}>Email</AppText>
                <TextInput
                  placeholder="Email"
                  value={user?.email || ''}
                  editable={false}
                  mode='outlined'
                  style={tw`bg-gray-100 mb-2`}
                  outlineStyle={tw`rounded-xl`}
                  outlineColor='#E5E7EB'
                />
                <AppButton title="Save" disabled />
              </>
            ) : (
              <>
                <AppText weight="bold" style={tw`text-lg text-gray-700 mb-1`}>
                  {user ? `${user.firstName} ${user.lastName}` : 'User Name'}
                </AppText>
                <AppText style={tw`text-gray-500`}>{user?.email || '<EMAIL>'}</AppText>
              </>
            )}
          </View>
        </View>
      </View>
    </View>
  );
}
