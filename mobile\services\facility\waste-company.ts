import axios from '../../utils/axios';

// Types for API responses
export interface WasteCompanySubmissionResponse {
  id: string;
  submissionId: string;
  submittedAt: Date;
  companyName: string;
  [key: string]: any; // Allow additional properties for different company types
}

export interface ApiError {
  message: string;
  statusCode: number;
  error?: string;
}

// Waste Collection Company Service
export const submitWasteCollectionCompany = async (data: any): Promise<WasteCompanySubmissionResponse> => {
  try {
    const response = await axios.post<WasteCompanySubmissionResponse>('/submission/waste-collection-company', data);
    return response.data;
  } catch (error: any) {
    console.error('Error submitting waste collection company:', error);

    // Handle different types of errors
    if (error.response) {
      // Server responded with error status
      const apiError: ApiError = {
        message: error.response.data?.message || 'Failed to submit waste collection company',
        statusCode: error.response.status,
        error: error.response.data?.error
      };
      throw apiError;
    } else if (error.request) {
      // Network error
      throw {
        message: 'Network error. Please check your connection and try again.',
        statusCode: 0
      } as ApiError;
    } else {
      // Other error
      throw {
        message: error.message || 'An unexpected error occurred',
        statusCode: 500
      } as ApiError;
    }
  }
};

// Waste Recovery Company Service
export const submitWasteRecoveryCompany = async (data: any): Promise<WasteCompanySubmissionResponse> => {
  try {
    const response = await axios.post<WasteCompanySubmissionResponse>('/submission/waste-recovery-company', data);
    return response.data;
  } catch (error: any) {
    console.error('Error submitting waste recovery company:', error);

    // Handle different types of errors
    if (error.response) {
      // Server responded with error status
      const apiError: ApiError = {
        message: error.response.data?.message || 'Failed to submit waste recovery company',
        statusCode: error.response.status,
        error: error.response.data?.error
      };
      throw apiError;
    } else if (error.request) {
      // Network error
      throw {
        message: 'Network error. Please check your connection and try again.',
        statusCode: 0
      } as ApiError;
    } else {
      // Other error
      throw {
        message: error.message || 'An unexpected error occurred',
        statusCode: 500
      } as ApiError;
    }
  }
};

// Waste Disposal Company Service
export const submitWasteDisposalCompany = async (data: any): Promise<WasteCompanySubmissionResponse> => {
  try {
    const response = await axios.post<WasteCompanySubmissionResponse>('/submission/waste-disposal-company', data);
    return response.data;
  } catch (error: any) {
    console.error('Error submitting waste disposal company:', error);

    // Handle different types of errors
    if (error.response) {
      // Server responded with error status
      const apiError: ApiError = {
        message: error.response.data?.message || 'Failed to submit waste disposal company',
        statusCode: error.response.status,
        error: error.response.data?.error
      };
      throw apiError;
    } else if (error.request) {
      // Network error
      throw {
        message: 'Network error. Please check your connection and try again.',
        statusCode: 0
      } as ApiError;
    } else {
      // Other error
      throw {
        message: error.message || 'An unexpected error occurred',
        statusCode: 500
      } as ApiError;
    }
  }
};