import { CellsListResponse, DistrictsListResponse, ProvincesListResponse, SectorsListResponse, Village, VillagesListResponse } from '@/types/administrative';
import axios from '../../utils/axios';

export const getProvinces = () => {
    return axios.get<ProvincesListResponse>(`/provinces?page=1&limit=1000`);
};

export const getDistricts = (provinceId: number) => {
    return axios.get<DistrictsListResponse>(`/districts`, { params: { provinceId, page: 1, limit: 100 } });
};

export const getSectors = (districtId: number) => {
    return axios.get<SectorsListResponse>(`/sectors`, { params: { districtId, page: 1, limit: 100 } });
};

export const getCells = (sectorId: number) => {
    return axios.get<CellsListResponse>(`/cells`, { params: { sectorId, page: 1, limit: 100 } });
};

export const getVillages = (cellId: number) => {
    return axios.get<VillagesListResponse>(`/villages`, { params: { cellId, page: 1, limit: 100 } }  );
};

export const getVillage = (villageId: string) => {
    return axios.get<Village>(`/villages/${villageId}`);
};