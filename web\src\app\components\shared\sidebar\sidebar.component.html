<div class="sidebar-container"
     [ngClass]="isCollapsed ? 'sidebar-collapsed' : 'sidebar-expanded'">
  
  <!-- Logo Section - No toggle button on desktop -->
  <div class="flex items-center justify-between border-b border-gray-200">
    <div class="flex items-center flex-1">
      <img 
        src="/images/logo-transparent.png" 
        alt="Company Logo" 
        [ngClass]="isCollapsed ? 'sidebar-logo-icon' : 'sidebar-logo-full'">
    </div>
    
    <!-- Toggle Button - Only visible on mobile -->
    <p-button 
      [text]="true"
      [rounded]="true"
      icon="pi pi-bars"
      (onClick)="onToggleSidebar()"
      class="text-gray-600 hover:bg-gray-100 toggle-btn mobile-only">
    </p-button>
  </div>

  <!-- Navigation Items -->
  <nav class="flex-1 overflow-y-auto">
    <ul class="space-y-0">
      <li *ngFor="let item of currentNavItems">
        <a [routerLink]="item.routerLink"
           class="nav-item flex items-center text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors group cursor-pointer"
           routerLinkActive="bg-blue-50 text-blue-600">
          <i [class]="item.icon + ' text-gray-500 group-hover:text-blue-600'"></i>
          <span *ngIf="!isCollapsed">{{ item.label }}</span>
        </a>
      </li>
    </ul>
  </nav>

  <!-- Profile Section -->
  <div *ngIf="!isCollapsed" class="border-t border-gray-200">
    <ul class="space-y-0">
      <li *ngFor="let item of profileItems">
        <a [routerLink]="item.routerLink"
           (click)="item.command && item.command()"
           class="nav-item flex items-center text-gray-700 hover:bg-gray-50 transition-colors group cursor-pointer"
           routerLinkActive="bg-gray-50">
          <i [class]="item.icon + ' text-gray-500'"></i>
          <span>{{ item.label }}</span>
        </a>
      </li>
    </ul>
  </div>

  <!-- Profile icons for collapsed state (mobile only) -->
  <div *ngIf="isCollapsed" class="border-t border-gray-200">
    <div class="flex flex-col items-center py-2">
      <a *ngFor="let item of profileItems"
         [routerLink]="item.routerLink"
         (click)="item.command && item.command()"
         class="p-2 rounded hover:bg-gray-50 text-gray-500 hover:text-blue-600 transition-colors">
        <i [class]="item.icon + ' text-sm'"></i>
      </a>
    </div>
  </div>
</div>