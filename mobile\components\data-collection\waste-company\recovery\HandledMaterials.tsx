import React, { useState } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Controller } from 'react-hook-form';
import tw from 'twrnc';
import AppText from '@/components/ui/Text';
import { TextInput } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { PRIMARY_COLOR } from '@/constants/colors';

interface HandledMaterialsSectionProps {
  control: any;
  errors: any;
  watch: any;
}

const HandledMaterialsSection = ({ control, errors, watch }: HandledMaterialsSectionProps) => {
  const materials = watch('handledMaterials.materials') || [];

  const addMaterial = (onChange: (value: any) => void) => {
    const newMaterial = {
      materialName: '',
      supplier: '',
      quantityPerDay: null,
    };
    onChange([...materials, newMaterial]);
  };

  const removeMaterial = (index: number, onChange: (value: any) => void) => {
    const updatedMaterials = materials.filter((_: any, i: number) => i !== index);
    onChange(updatedMaterials);
  };

  const updateMaterial = (index: number, field: string, value: any, onChange: (value: any) => void) => {
    const updatedMaterials = [...materials];
    updatedMaterials[index] = { ...updatedMaterials[index], [field]: value };
    onChange(updatedMaterials);
  };

  return (
    <View style={tw`gap-4`}>
      <Controller
        control={control}
        name="handledMaterials.materials"
        render={({ field: { onChange, value } }) => (
          <View style={tw`gap-4`}>
            {(value || []).map((material: any, index: number) => (
              <View key={index} style={tw`p-4 border border-gray-300 rounded-lg`}>
                <View style={tw`flex-row justify-between items-center mb-3`}>
                  <AppText weight="semibold">Material {index + 1}</AppText>
                  {value.length > 1 && (
                    <TouchableOpacity
                      onPress={() => removeMaterial(index, onChange)}
                      style={tw`p-1`}
                    >
                      <Ionicons name="trash" size={20} color="red" />
                    </TouchableOpacity>
                  )}
                </View>

                {/* Material Name */}
                <View style={tw`mb-3`}>
                  <AppText style={tw`mb-2`}>Name of material</AppText>
                  <TextInput
                    mode="outlined"
                    value={material.materialName || ''}
                    onChangeText={(text) => updateMaterial(index, 'materialName', text, onChange)}
                    placeholder="Enter material name"
                    error={!!errors.handledMaterials?.materials?.[index]?.materialName}
                    outlineColor="gray"
                  />
                  {errors.handledMaterials?.materials?.[index]?.materialName && (
                    <AppText style={tw`text-red-500 text-sm mt-1`}>
                      {errors.handledMaterials.materials[index].materialName.message}
                    </AppText>
                  )}
                </View>

                {/* Supplier */}
                <View style={tw`mb-3`}>
                  <AppText style={tw`mb-2`}>Its supplier</AppText>
                  <TextInput
                    mode="outlined"
                    value={material.supplier || ''}
                    onChangeText={(text) => updateMaterial(index, 'supplier', text, onChange)}
                    placeholder="Enter supplier information"
                    error={!!errors.handledMaterials?.materials?.[index]?.supplier}
                    outlineColor="gray"
                  />
                  {errors.handledMaterials?.materials?.[index]?.supplier && (
                    <AppText style={tw`text-red-500 text-sm mt-1`}>
                      {errors.handledMaterials.materials[index].supplier.message}
                    </AppText>
                  )}
                </View>

                {/* Quantity Per Day */}
                <View>
                  <AppText style={tw`mb-2`}>Quantity collected (kg/day)</AppText>
                  <TextInput
                    mode="outlined"
                    value={material.quantityPerDay?.toString() || ''}
                    onChangeText={(text) => updateMaterial(index, 'quantityPerDay', text ? parseFloat(text) : null, onChange)}
                    placeholder="Enter daily quantity in kg"
                    keyboardType="numeric"
                    error={!!errors.handledMaterials?.materials?.[index]?.quantityPerDay}
                    outlineColor="gray"
                  />
                  {errors.handledMaterials?.materials?.[index]?.quantityPerDay && (
                    <AppText style={tw`text-red-500 text-sm mt-1`}>
                      {errors.handledMaterials.materials[index].quantityPerDay.message}
                    </AppText>
                  )}
                </View>
              </View>
            ))}

            <TouchableOpacity
              onPress={() => addMaterial(onChange)}
              style={tw`flex-row items-center justify-center gap-2 p-4 border border-dashed border-gray-400 rounded-lg`}
            >
              <Ionicons name="add-circle" size={24} color={PRIMARY_COLOR} />
              <AppText style={tw`text-[${PRIMARY_COLOR}]`}>Add Material</AppText>
            </TouchableOpacity>

            {errors.handledMaterials?.materials && (
              <AppText style={tw`text-red-500 text-sm`}>
                {errors.handledMaterials.materials.message}
              </AppText>
            )}
          </View>
        )}
      />
    </View>
  );
};

export default HandledMaterialsSection;
