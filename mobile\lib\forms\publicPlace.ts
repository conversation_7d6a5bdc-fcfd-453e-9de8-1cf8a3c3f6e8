import { CleanWaterStorageCapacity, DailyPatientVolume, EducationLevel, ExcretaManagement, FacilitySlabConstructionMaterial, Gender, HandWashingFacilityType, HandWashingMaterial, HealthFacilityManagement, HealthFacilityType, MainWaterSource, MarketCategory, MarketOpeningDays, PwsNonFunctionalityDuration, PwsNonFunctionalityReason, SchoolManagement, SchoolType, SchoolTypeDayBoarding, ToiletFacilityCompleteness, ToiletFacilityType, UnimprovedWaterReason, WasteCollectionFrequency, WasteManagementAfterSeparation, WasteTreatmentType, WasteWaterManagement, WaterAvailability, WaterAvailabilityFrequency, WaterFetchingTime, WaterSourceDistance } from '@/types/enums';
import * as yup from 'yup';


export const publicPlaceSchema = yup.object().shape({
  generalInfo: yup.object().shape({
    category: yup
      .string()
      .oneOf(Object.keys(MarketCategory))
      .required('Category is required'),
    openingDays: yup
      .string()
      .oneOf(Object.keys(MarketOpeningDays))
      .required('Opening days is required'),
  }),
  waterSupply: yup.object().shape({
    connectedToPipeline: yup.boolean().required('This field is required'),
    waterAvailability: yup
      .string()
      .oneOf(Object.keys(WaterAvailability))
      .nullable()
      .when('connectedToPipeline', {
          is: (ctp: boolean) => ctp === true,
          then: schema => schema.required('Water availability is required'),
          otherwise: schema => schema.notRequired(),
      }),
    availableDays: yup
      .string().oneOf(Object.keys(WaterAvailabilityFrequency))
      .nullable()
      .when('waterAvailability', {
        is: (wa: string) => wa === 'SOMETIMES_UNAVAILABLE',
        then: schema => schema.required('Available days is required'),
        otherwise: schema => schema.notRequired(),
      }),
    storageCapacity: yup
      .string()
      .oneOf(Object.keys(CleanWaterStorageCapacity))
      .required('Storage capacity is required'),
    mainWaterSource: yup
      .string()
      .oneOf(Object.keys(MainWaterSource))
      .when('connectedToPipeline', {
        is: (ctp: boolean) => ctp === false,
        then: schema => schema.required('Water source is required'),
        otherwise: schema => schema.notRequired(),
      }),
    distanceToSource: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(WaterSourceDistance), 'Distance to water source must be a valid option')
      .required('Distance to water source is required'),
  }),

  sanitation: yup.object().shape({
    toiletType: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(ToiletFacilityType), 'Invalid toilet type')
      .nullable()
      .required('Toilet type is required'),
    slabConstructionMaterial: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(FacilitySlabConstructionMaterial), 'Invalid slab construction material')
      .nullable()
      .when('toiletType', {
        is: (tt: string) => [
          'PIT_WITH_SLAB',
          'PIT_WITHOUT_SLAB',
          'VENTILATED_IMPROVED_PIT',
          'COMPOSTING_TOILET',
          'URINE_DIVERSION_DRY_TOILET',
        ].includes(tt),
        then: schema => schema.required('Slab construction material is required'),
        otherwise: schema => schema.notRequired(),
      }),
    totalToilets: yup.number().required('Total number of toilets is required'),
    genderSeparation: yup.boolean().required('This field is required'),
    femaleToilets: yup.number().required('Number of female toilets is required'),
    maleToilets: yup.number().required('Number of male toilets is required'),
    disabilityAccess: yup.boolean().required('This field is required'),
    hasToiletFullInLast2Years: yup.boolean().required('This field is required'),
    excretaManagement: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(ExcretaManagement), 'Invalid excreta management')
      .when('hasToiletFullInLast2Years', {
        is: true,
        then: schema => schema.required('Excreta management is required'),
        otherwise: schema => schema.notRequired(),
      }),
  }),

  hygiene: yup.object().shape({
    handwashingFacility: yup.boolean().required('Handwashing facility is required'),
    facilityType: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(HandWashingFacilityType), 'Invalid handwashing facility type')
      .when('handwashingFacility', {
        is: true,
        then: schema => schema.required('Handwashing facility type is required'),
        otherwise: schema => schema.notRequired(),
      }),
    handwashingMaterials: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(HandWashingMaterial), 'Invalid handwashing material')
      .when('handwashingFacility', {
        is: true,
        then: schema => schema.required('Handwashing materials are required'),
        otherwise: schema => schema.notRequired(),
      }),
      handWashingfacilityNearToilet: yup.boolean().required('Handwashing facility near toilet is required'),
      toiletHandWashingFacilityType: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(HandWashingFacilityType), 'Invalid handwashing facility type')
      .when('handWashingfacilityNearToilet', {
        is: true,
        then: schema => schema.required('Handwashing facility type is required'),
        otherwise: schema => schema.notRequired(),
      }),
      toiletHandWashingMaterials: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(HandWashingMaterial), 'Invalid handwashing material')
      .when('handWashingfacilityNearToilet', {
        is: true,
        then: schema => schema.required('Handwashing materials are required'),
        otherwise: schema => schema.notRequired(),
      }),
  }),

  solidWaste: yup.object().shape({
    wasteSeparation: yup.boolean().required('Waste separation is required'),
    wasteManagement: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(WasteManagementAfterSeparation), 'Invalid waste management')
      .nullable()
      .when('wasteSeparation', {
        is: true,
        then: schema => schema.required('Waste management is required'),
        otherwise: schema => schema.notRequired(),
      }),
    treatmentType: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(WasteTreatmentType), 'Invalid treatment type')
      .nullable()
      .required('Treatment type is required'),
    collectionFrequency: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(WasteCollectionFrequency), 'Invalid collection frequency')
      .nullable()
      .required('Collection frequency is required'),

    collectionCost: yup.number().required('Collection cost is required'),
  }),

  liquidWaste: yup.object().shape({
    liquidWasteManagement: yup
      .string()
      .transform(value => value === "" ? null : value)
      .oneOf(Object.keys(WasteWaterManagement), 'Invalid waste water management')
      .nullable()
      .required('Waste water management is required'),
  }),
});